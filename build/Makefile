# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Studio/600-codes/awtk_prj

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Studio/600-codes/awtk_prj/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles /home/<USER>/Studio/600-codes/awtk_prj/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named awtk_external

# Build rule for target.
awtk_external: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 awtk_external
.PHONY : awtk_external

# fast build rule for target.
awtk_external/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/awtk_external.dir/build.make CMakeFiles/awtk_external.dir/build
.PHONY : awtk_external/fast

#=============================================================================
# Target rules for targets named copy_awtk_lib

# Build rule for target.
copy_awtk_lib: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 copy_awtk_lib
.PHONY : copy_awtk_lib

# fast build rule for target.
copy_awtk_lib/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/copy_awtk_lib.dir/build.make CMakeFiles/copy_awtk_lib.dir/build
.PHONY : copy_awtk_lib/fast

#=============================================================================
# Target rules for targets named CoffeeMachineApp

# Build rule for target.
CoffeeMachineApp: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CoffeeMachineApp
.PHONY : CoffeeMachineApp

# fast build rule for target.
CoffeeMachineApp/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CoffeeMachineApp.dir/build.make CMakeFiles/CoffeeMachineApp.dir/build
.PHONY : CoffeeMachineApp/fast

#=============================================================================
# Target rules for targets named test_app

# Build rule for target.
test_app: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_app
.PHONY : test_app

# fast build rule for target.
test_app/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_app.dir/build.make CMakeFiles/test_app.dir/build
.PHONY : test_app/fast

src/assets.o: src/assets.c.o
.PHONY : src/assets.o

# target to build an object file
src/assets.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CoffeeMachineApp.dir/build.make CMakeFiles/CoffeeMachineApp.dir/src/assets.c.o
.PHONY : src/assets.c.o

src/assets.i: src/assets.c.i
.PHONY : src/assets.i

# target to preprocess a source file
src/assets.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CoffeeMachineApp.dir/build.make CMakeFiles/CoffeeMachineApp.dir/src/assets.c.i
.PHONY : src/assets.c.i

src/assets.s: src/assets.c.s
.PHONY : src/assets.s

# target to generate assembly for a file
src/assets.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CoffeeMachineApp.dir/build.make CMakeFiles/CoffeeMachineApp.dir/src/assets.c.s
.PHONY : src/assets.c.s

src/device_manager.o: src/device_manager.cpp.o
.PHONY : src/device_manager.o

# target to build an object file
src/device_manager.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CoffeeMachineApp.dir/build.make CMakeFiles/CoffeeMachineApp.dir/src/device_manager.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_app.dir/build.make CMakeFiles/test_app.dir/src/device_manager.cpp.o
.PHONY : src/device_manager.cpp.o

src/device_manager.i: src/device_manager.cpp.i
.PHONY : src/device_manager.i

# target to preprocess a source file
src/device_manager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CoffeeMachineApp.dir/build.make CMakeFiles/CoffeeMachineApp.dir/src/device_manager.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_app.dir/build.make CMakeFiles/test_app.dir/src/device_manager.cpp.i
.PHONY : src/device_manager.cpp.i

src/device_manager.s: src/device_manager.cpp.s
.PHONY : src/device_manager.s

# target to generate assembly for a file
src/device_manager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CoffeeMachineApp.dir/build.make CMakeFiles/CoffeeMachineApp.dir/src/device_manager.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_app.dir/build.make CMakeFiles/test_app.dir/src/device_manager.cpp.s
.PHONY : src/device_manager.cpp.s

src/main.o: src/main.cpp.o
.PHONY : src/main.o

# target to build an object file
src/main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CoffeeMachineApp.dir/build.make CMakeFiles/CoffeeMachineApp.dir/src/main.cpp.o
.PHONY : src/main.cpp.o

src/main.i: src/main.cpp.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CoffeeMachineApp.dir/build.make CMakeFiles/CoffeeMachineApp.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CoffeeMachineApp.dir/build.make CMakeFiles/CoffeeMachineApp.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

src/ui_loader.o: src/ui_loader.cpp.o
.PHONY : src/ui_loader.o

# target to build an object file
src/ui_loader.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CoffeeMachineApp.dir/build.make CMakeFiles/CoffeeMachineApp.dir/src/ui_loader.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_app.dir/build.make CMakeFiles/test_app.dir/src/ui_loader.cpp.o
.PHONY : src/ui_loader.cpp.o

src/ui_loader.i: src/ui_loader.cpp.i
.PHONY : src/ui_loader.i

# target to preprocess a source file
src/ui_loader.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CoffeeMachineApp.dir/build.make CMakeFiles/CoffeeMachineApp.dir/src/ui_loader.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_app.dir/build.make CMakeFiles/test_app.dir/src/ui_loader.cpp.i
.PHONY : src/ui_loader.cpp.i

src/ui_loader.s: src/ui_loader.cpp.s
.PHONY : src/ui_loader.s

# target to generate assembly for a file
src/ui_loader.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CoffeeMachineApp.dir/build.make CMakeFiles/CoffeeMachineApp.dir/src/ui_loader.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_app.dir/build.make CMakeFiles/test_app.dir/src/ui_loader.cpp.s
.PHONY : src/ui_loader.cpp.s

test_app.o: test_app.cpp.o
.PHONY : test_app.o

# target to build an object file
test_app.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_app.dir/build.make CMakeFiles/test_app.dir/test_app.cpp.o
.PHONY : test_app.cpp.o

test_app.i: test_app.cpp.i
.PHONY : test_app.i

# target to preprocess a source file
test_app.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_app.dir/build.make CMakeFiles/test_app.dir/test_app.cpp.i
.PHONY : test_app.cpp.i

test_app.s: test_app.cpp.s
.PHONY : test_app.s

# target to generate assembly for a file
test_app.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_app.dir/build.make CMakeFiles/test_app.dir/test_app.cpp.s
.PHONY : test_app.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... awtk_external"
	@echo "... copy_awtk_lib"
	@echo "... CoffeeMachineApp"
	@echo "... test_app"
	@echo "... src/assets.o"
	@echo "... src/assets.i"
	@echo "... src/assets.s"
	@echo "... src/device_manager.o"
	@echo "... src/device_manager.i"
	@echo "... src/device_manager.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
	@echo "... src/ui_loader.o"
	@echo "... src/ui_loader.i"
	@echo "... src/ui_loader.s"
	@echo "... test_app.o"
	@echo "... test_app.i"
	@echo "... test_app.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

