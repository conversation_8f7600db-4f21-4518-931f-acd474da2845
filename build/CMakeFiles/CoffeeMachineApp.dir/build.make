# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Studio/600-codes/awtk_prj

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Studio/600-codes/awtk_prj/build

# Include any dependencies generated for this target.
include CMakeFiles/CoffeeMachineApp.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/CoffeeMachineApp.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/CoffeeMachineApp.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/CoffeeMachineApp.dir/flags.make

CMakeFiles/CoffeeMachineApp.dir/src/main.cpp.o: CMakeFiles/CoffeeMachineApp.dir/flags.make
CMakeFiles/CoffeeMachineApp.dir/src/main.cpp.o: ../src/main.cpp
CMakeFiles/CoffeeMachineApp.dir/src/main.cpp.o: CMakeFiles/CoffeeMachineApp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/CoffeeMachineApp.dir/src/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CoffeeMachineApp.dir/src/main.cpp.o -MF CMakeFiles/CoffeeMachineApp.dir/src/main.cpp.o.d -o CMakeFiles/CoffeeMachineApp.dir/src/main.cpp.o -c /home/<USER>/Studio/600-codes/awtk_prj/src/main.cpp

CMakeFiles/CoffeeMachineApp.dir/src/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/CoffeeMachineApp.dir/src/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Studio/600-codes/awtk_prj/src/main.cpp > CMakeFiles/CoffeeMachineApp.dir/src/main.cpp.i

CMakeFiles/CoffeeMachineApp.dir/src/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/CoffeeMachineApp.dir/src/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Studio/600-codes/awtk_prj/src/main.cpp -o CMakeFiles/CoffeeMachineApp.dir/src/main.cpp.s

CMakeFiles/CoffeeMachineApp.dir/src/ui_loader.cpp.o: CMakeFiles/CoffeeMachineApp.dir/flags.make
CMakeFiles/CoffeeMachineApp.dir/src/ui_loader.cpp.o: ../src/ui_loader.cpp
CMakeFiles/CoffeeMachineApp.dir/src/ui_loader.cpp.o: CMakeFiles/CoffeeMachineApp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/CoffeeMachineApp.dir/src/ui_loader.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CoffeeMachineApp.dir/src/ui_loader.cpp.o -MF CMakeFiles/CoffeeMachineApp.dir/src/ui_loader.cpp.o.d -o CMakeFiles/CoffeeMachineApp.dir/src/ui_loader.cpp.o -c /home/<USER>/Studio/600-codes/awtk_prj/src/ui_loader.cpp

CMakeFiles/CoffeeMachineApp.dir/src/ui_loader.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/CoffeeMachineApp.dir/src/ui_loader.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Studio/600-codes/awtk_prj/src/ui_loader.cpp > CMakeFiles/CoffeeMachineApp.dir/src/ui_loader.cpp.i

CMakeFiles/CoffeeMachineApp.dir/src/ui_loader.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/CoffeeMachineApp.dir/src/ui_loader.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Studio/600-codes/awtk_prj/src/ui_loader.cpp -o CMakeFiles/CoffeeMachineApp.dir/src/ui_loader.cpp.s

CMakeFiles/CoffeeMachineApp.dir/src/device_manager.cpp.o: CMakeFiles/CoffeeMachineApp.dir/flags.make
CMakeFiles/CoffeeMachineApp.dir/src/device_manager.cpp.o: ../src/device_manager.cpp
CMakeFiles/CoffeeMachineApp.dir/src/device_manager.cpp.o: CMakeFiles/CoffeeMachineApp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/CoffeeMachineApp.dir/src/device_manager.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CoffeeMachineApp.dir/src/device_manager.cpp.o -MF CMakeFiles/CoffeeMachineApp.dir/src/device_manager.cpp.o.d -o CMakeFiles/CoffeeMachineApp.dir/src/device_manager.cpp.o -c /home/<USER>/Studio/600-codes/awtk_prj/src/device_manager.cpp

CMakeFiles/CoffeeMachineApp.dir/src/device_manager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/CoffeeMachineApp.dir/src/device_manager.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Studio/600-codes/awtk_prj/src/device_manager.cpp > CMakeFiles/CoffeeMachineApp.dir/src/device_manager.cpp.i

CMakeFiles/CoffeeMachineApp.dir/src/device_manager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/CoffeeMachineApp.dir/src/device_manager.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Studio/600-codes/awtk_prj/src/device_manager.cpp -o CMakeFiles/CoffeeMachineApp.dir/src/device_manager.cpp.s

CMakeFiles/CoffeeMachineApp.dir/src/assets.c.o: CMakeFiles/CoffeeMachineApp.dir/flags.make
CMakeFiles/CoffeeMachineApp.dir/src/assets.c.o: ../src/assets.c
CMakeFiles/CoffeeMachineApp.dir/src/assets.c.o: CMakeFiles/CoffeeMachineApp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/CoffeeMachineApp.dir/src/assets.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/CoffeeMachineApp.dir/src/assets.c.o -MF CMakeFiles/CoffeeMachineApp.dir/src/assets.c.o.d -o CMakeFiles/CoffeeMachineApp.dir/src/assets.c.o -c /home/<USER>/Studio/600-codes/awtk_prj/src/assets.c

CMakeFiles/CoffeeMachineApp.dir/src/assets.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/CoffeeMachineApp.dir/src/assets.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/Studio/600-codes/awtk_prj/src/assets.c > CMakeFiles/CoffeeMachineApp.dir/src/assets.c.i

CMakeFiles/CoffeeMachineApp.dir/src/assets.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/CoffeeMachineApp.dir/src/assets.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/Studio/600-codes/awtk_prj/src/assets.c -o CMakeFiles/CoffeeMachineApp.dir/src/assets.c.s

# Object files for target CoffeeMachineApp
CoffeeMachineApp_OBJECTS = \
"CMakeFiles/CoffeeMachineApp.dir/src/main.cpp.o" \
"CMakeFiles/CoffeeMachineApp.dir/src/ui_loader.cpp.o" \
"CMakeFiles/CoffeeMachineApp.dir/src/device_manager.cpp.o" \
"CMakeFiles/CoffeeMachineApp.dir/src/assets.c.o"

# External object files for target CoffeeMachineApp
CoffeeMachineApp_EXTERNAL_OBJECTS =

bin/CoffeeMachineApp: CMakeFiles/CoffeeMachineApp.dir/src/main.cpp.o
bin/CoffeeMachineApp: CMakeFiles/CoffeeMachineApp.dir/src/ui_loader.cpp.o
bin/CoffeeMachineApp: CMakeFiles/CoffeeMachineApp.dir/src/device_manager.cpp.o
bin/CoffeeMachineApp: CMakeFiles/CoffeeMachineApp.dir/src/assets.c.o
bin/CoffeeMachineApp: CMakeFiles/CoffeeMachineApp.dir/build.make
bin/CoffeeMachineApp: bin/libawtk.so
bin/CoffeeMachineApp: CMakeFiles/CoffeeMachineApp.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Linking CXX executable bin/CoffeeMachineApp"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/CoffeeMachineApp.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/CoffeeMachineApp.dir/build: bin/CoffeeMachineApp
.PHONY : CMakeFiles/CoffeeMachineApp.dir/build

CMakeFiles/CoffeeMachineApp.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/CoffeeMachineApp.dir/cmake_clean.cmake
.PHONY : CMakeFiles/CoffeeMachineApp.dir/clean

CMakeFiles/CoffeeMachineApp.dir/depend:
	cd /home/<USER>/Studio/600-codes/awtk_prj/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Studio/600-codes/awtk_prj /home/<USER>/Studio/600-codes/awtk_prj /home/<USER>/Studio/600-codes/awtk_prj/build /home/<USER>/Studio/600-codes/awtk_prj/build /home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles/CoffeeMachineApp.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/CoffeeMachineApp.dir/depend

