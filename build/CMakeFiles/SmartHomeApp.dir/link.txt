/usr/bin/c++ -g CMakeFiles/SmartHomeApp.dir/src/main.cpp.o CMakeFiles/SmartHomeApp.dir/src/ui_loader.cpp.o CMakeFiles/SmartHomeApp.dir/src/device_manager.cpp.o CMakeFiles/SmartHomeApp.dir/src/assets.c.o -o bin/SmartHomeApp   -L/home/<USER>/Studio/600-codes/awtk_prj/build/bin  -Wl,-rpath,/home/<USER>/Studio/600-codes/awtk_prj/libs/awtk/bin -lawtk -lgtk-3 -lgdk-3 -lpangocairo-1.0 -lpango-1.0 -lharfbuzz -latk-1.0 -lcairo-gobject -lcairo -lgdk_pixbuf-2.0 -lgio-2.0 -lgobject-2.0 -lglib-2.0 -lGL -lpthread 
