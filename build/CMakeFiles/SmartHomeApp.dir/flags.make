# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# compile C with /usr/bin/cc
# compile CXX with /usr/bin/c++
C_DEFINES = -DDEBUG -DDESKTOP_PLATFORM

C_INCLUDES = -I/home/<USER>/Studio/600-codes/awtk_prj/include -I/usr/include/gtk-3.0 -I/usr/include/at-spi2-atk/2.0 -I/usr/include/at-spi-2.0 -I/usr/include/dbus-1.0 -I/usr/lib/x86_64-linux-gnu/dbus-1.0/include -I/usr/include/gio-unix-2.0 -I/usr/include/cairo -I/usr/include/pango-1.0 -I/usr/include/harfbuzz -I/usr/include/fribidi -I/usr/include/atk-1.0 -I/usr/include/pixman-1 -I/usr/include/uuid -I/usr/include/freetype2 -I/usr/include/gdk-pixbuf-2.0 -I/usr/include/libpng16 -I/usr/include/libmount -I/usr/include/blkid -I/usr/include/glib-2.0 -I/usr/lib/x86_64-linux-gnu/glib-2.0/include -isystem /home/<USER>/Studio/600-codes/awtk_prj/libs/awtk/src -isystem /home/<USER>/Studio/600-codes/awtk_prj/libs/awtk/src/ext_widgets -isystem /home/<USER>/Studio/600-codes/awtk_prj/libs/awtk/3rd

C_FLAGS = -g -Wall -Wextra

CXX_DEFINES = -DDEBUG -DDESKTOP_PLATFORM

CXX_INCLUDES = -I/home/<USER>/Studio/600-codes/awtk_prj/include -I/usr/include/gtk-3.0 -I/usr/include/at-spi2-atk/2.0 -I/usr/include/at-spi-2.0 -I/usr/include/dbus-1.0 -I/usr/lib/x86_64-linux-gnu/dbus-1.0/include -I/usr/include/gio-unix-2.0 -I/usr/include/cairo -I/usr/include/pango-1.0 -I/usr/include/harfbuzz -I/usr/include/fribidi -I/usr/include/atk-1.0 -I/usr/include/pixman-1 -I/usr/include/uuid -I/usr/include/freetype2 -I/usr/include/gdk-pixbuf-2.0 -I/usr/include/libpng16 -I/usr/include/libmount -I/usr/include/blkid -I/usr/include/glib-2.0 -I/usr/lib/x86_64-linux-gnu/glib-2.0/include -isystem /home/<USER>/Studio/600-codes/awtk_prj/libs/awtk/src -isystem /home/<USER>/Studio/600-codes/awtk_prj/libs/awtk/src/ext_widgets -isystem /home/<USER>/Studio/600-codes/awtk_prj/libs/awtk/3rd

CXX_FLAGS = -g -Wall -Wextra -std=gnu++11

