# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Studio/600-codes/awtk_prj

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Studio/600-codes/awtk_prj/build

# Utility rule file for copy_awtk_lib.

# Include any custom commands dependencies for this target.
include CMakeFiles/copy_awtk_lib.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/copy_awtk_lib.dir/progress.make

CMakeFiles/copy_awtk_lib: bin/libawtk.so

bin/libawtk.so:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) 复制AWTK库到build目录
	/usr/bin/cmake -E copy /home/<USER>/Studio/600-codes/awtk_prj/libs/awtk/bin/libawtk.so /home/<USER>/Studio/600-codes/awtk_prj/build/bin/libawtk.so

copy_awtk_lib: CMakeFiles/copy_awtk_lib
copy_awtk_lib: bin/libawtk.so
copy_awtk_lib: CMakeFiles/copy_awtk_lib.dir/build.make
.PHONY : copy_awtk_lib

# Rule to build all files generated by this target.
CMakeFiles/copy_awtk_lib.dir/build: copy_awtk_lib
.PHONY : CMakeFiles/copy_awtk_lib.dir/build

CMakeFiles/copy_awtk_lib.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/copy_awtk_lib.dir/cmake_clean.cmake
.PHONY : CMakeFiles/copy_awtk_lib.dir/clean

CMakeFiles/copy_awtk_lib.dir/depend:
	cd /home/<USER>/Studio/600-codes/awtk_prj/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Studio/600-codes/awtk_prj /home/<USER>/Studio/600-codes/awtk_prj /home/<USER>/Studio/600-codes/awtk_prj/build /home/<USER>/Studio/600-codes/awtk_prj/build /home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles/copy_awtk_lib.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/copy_awtk_lib.dir/depend

