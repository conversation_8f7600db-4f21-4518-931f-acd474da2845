# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Studio/600-codes/awtk_prj

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Studio/600-codes/awtk_prj/build

# Include any dependencies generated for this target.
include CMakeFiles/test_app.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/test_app.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/test_app.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/test_app.dir/flags.make

CMakeFiles/test_app.dir/test_app.cpp.o: CMakeFiles/test_app.dir/flags.make
CMakeFiles/test_app.dir/test_app.cpp.o: ../test_app.cpp
CMakeFiles/test_app.dir/test_app.cpp.o: CMakeFiles/test_app.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/test_app.dir/test_app.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_app.dir/test_app.cpp.o -MF CMakeFiles/test_app.dir/test_app.cpp.o.d -o CMakeFiles/test_app.dir/test_app.cpp.o -c /home/<USER>/Studio/600-codes/awtk_prj/test_app.cpp

CMakeFiles/test_app.dir/test_app.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/test_app.dir/test_app.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Studio/600-codes/awtk_prj/test_app.cpp > CMakeFiles/test_app.dir/test_app.cpp.i

CMakeFiles/test_app.dir/test_app.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/test_app.dir/test_app.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Studio/600-codes/awtk_prj/test_app.cpp -o CMakeFiles/test_app.dir/test_app.cpp.s

CMakeFiles/test_app.dir/src/ui_loader.cpp.o: CMakeFiles/test_app.dir/flags.make
CMakeFiles/test_app.dir/src/ui_loader.cpp.o: ../src/ui_loader.cpp
CMakeFiles/test_app.dir/src/ui_loader.cpp.o: CMakeFiles/test_app.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/test_app.dir/src/ui_loader.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_app.dir/src/ui_loader.cpp.o -MF CMakeFiles/test_app.dir/src/ui_loader.cpp.o.d -o CMakeFiles/test_app.dir/src/ui_loader.cpp.o -c /home/<USER>/Studio/600-codes/awtk_prj/src/ui_loader.cpp

CMakeFiles/test_app.dir/src/ui_loader.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/test_app.dir/src/ui_loader.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Studio/600-codes/awtk_prj/src/ui_loader.cpp > CMakeFiles/test_app.dir/src/ui_loader.cpp.i

CMakeFiles/test_app.dir/src/ui_loader.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/test_app.dir/src/ui_loader.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Studio/600-codes/awtk_prj/src/ui_loader.cpp -o CMakeFiles/test_app.dir/src/ui_loader.cpp.s

CMakeFiles/test_app.dir/src/device_manager.cpp.o: CMakeFiles/test_app.dir/flags.make
CMakeFiles/test_app.dir/src/device_manager.cpp.o: ../src/device_manager.cpp
CMakeFiles/test_app.dir/src/device_manager.cpp.o: CMakeFiles/test_app.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/test_app.dir/src/device_manager.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_app.dir/src/device_manager.cpp.o -MF CMakeFiles/test_app.dir/src/device_manager.cpp.o.d -o CMakeFiles/test_app.dir/src/device_manager.cpp.o -c /home/<USER>/Studio/600-codes/awtk_prj/src/device_manager.cpp

CMakeFiles/test_app.dir/src/device_manager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/test_app.dir/src/device_manager.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Studio/600-codes/awtk_prj/src/device_manager.cpp > CMakeFiles/test_app.dir/src/device_manager.cpp.i

CMakeFiles/test_app.dir/src/device_manager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/test_app.dir/src/device_manager.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Studio/600-codes/awtk_prj/src/device_manager.cpp -o CMakeFiles/test_app.dir/src/device_manager.cpp.s

# Object files for target test_app
test_app_OBJECTS = \
"CMakeFiles/test_app.dir/test_app.cpp.o" \
"CMakeFiles/test_app.dir/src/ui_loader.cpp.o" \
"CMakeFiles/test_app.dir/src/device_manager.cpp.o"

# External object files for target test_app
test_app_EXTERNAL_OBJECTS =

bin/test_app: CMakeFiles/test_app.dir/test_app.cpp.o
bin/test_app: CMakeFiles/test_app.dir/src/ui_loader.cpp.o
bin/test_app: CMakeFiles/test_app.dir/src/device_manager.cpp.o
bin/test_app: CMakeFiles/test_app.dir/build.make
bin/test_app: bin/libawtk.so
bin/test_app: CMakeFiles/test_app.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking CXX executable bin/test_app"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/test_app.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/test_app.dir/build: bin/test_app
.PHONY : CMakeFiles/test_app.dir/build

CMakeFiles/test_app.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/test_app.dir/cmake_clean.cmake
.PHONY : CMakeFiles/test_app.dir/clean

CMakeFiles/test_app.dir/depend:
	cd /home/<USER>/Studio/600-codes/awtk_prj/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Studio/600-codes/awtk_prj /home/<USER>/Studio/600-codes/awtk_prj /home/<USER>/Studio/600-codes/awtk_prj/build /home/<USER>/Studio/600-codes/awtk_prj/build /home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles/test_app.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/test_app.dir/depend

