# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Studio/600-codes/awtk_prj

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Studio/600-codes/awtk_prj/build

# Utility rule file for awtk_external.

# Include any custom commands dependencies for this target.
include CMakeFiles/awtk_external.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/awtk_external.dir/progress.make

CMakeFiles/awtk_external: CMakeFiles/awtk_external-complete

CMakeFiles/awtk_external-complete: awtk_external-prefix/src/awtk_external-stamp/awtk_external-install
CMakeFiles/awtk_external-complete: awtk_external-prefix/src/awtk_external-stamp/awtk_external-mkdir
CMakeFiles/awtk_external-complete: awtk_external-prefix/src/awtk_external-stamp/awtk_external-download
CMakeFiles/awtk_external-complete: awtk_external-prefix/src/awtk_external-stamp/awtk_external-update
CMakeFiles/awtk_external-complete: awtk_external-prefix/src/awtk_external-stamp/awtk_external-patch
CMakeFiles/awtk_external-complete: awtk_external-prefix/src/awtk_external-stamp/awtk_external-configure
CMakeFiles/awtk_external-complete: awtk_external-prefix/src/awtk_external-stamp/awtk_external-build
CMakeFiles/awtk_external-complete: awtk_external-prefix/src/awtk_external-stamp/awtk_external-install
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Completed 'awtk_external'"
	/usr/bin/cmake -E make_directory /home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles
	/usr/bin/cmake -E touch /home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles/awtk_external-complete
	/usr/bin/cmake -E touch /home/<USER>/Studio/600-codes/awtk_prj/build/awtk_external-prefix/src/awtk_external-stamp/awtk_external-done

awtk_external-prefix/src/awtk_external-stamp/awtk_external-build: awtk_external-prefix/src/awtk_external-stamp/awtk_external-configure
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Performing build step for 'awtk_external'"
	cd /home/<USER>/Studio/600-codes/awtk_prj/build/awtk-build && scons -C /home/<USER>/Studio/600-codes/awtk_prj/libs/awtk
	cd /home/<USER>/Studio/600-codes/awtk_prj/build/awtk-build && /usr/bin/cmake -E touch /home/<USER>/Studio/600-codes/awtk_prj/build/awtk_external-prefix/src/awtk_external-stamp/awtk_external-build

awtk_external-prefix/src/awtk_external-stamp/awtk_external-configure: awtk_external-prefix/tmp/awtk_external-cfgcmd.txt
awtk_external-prefix/src/awtk_external-stamp/awtk_external-configure: awtk_external-prefix/src/awtk_external-stamp/awtk_external-patch
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "No configure step for 'awtk_external'"
	cd /home/<USER>/Studio/600-codes/awtk_prj/build/awtk-build && /usr/bin/cmake -E echo_append
	cd /home/<USER>/Studio/600-codes/awtk_prj/build/awtk-build && /usr/bin/cmake -E touch /home/<USER>/Studio/600-codes/awtk_prj/build/awtk_external-prefix/src/awtk_external-stamp/awtk_external-configure

awtk_external-prefix/src/awtk_external-stamp/awtk_external-download: awtk_external-prefix/src/awtk_external-stamp/awtk_external-mkdir
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "No download step for 'awtk_external'"
	/usr/bin/cmake -E echo_append
	/usr/bin/cmake -E touch /home/<USER>/Studio/600-codes/awtk_prj/build/awtk_external-prefix/src/awtk_external-stamp/awtk_external-download

awtk_external-prefix/src/awtk_external-stamp/awtk_external-install: awtk_external-prefix/src/awtk_external-stamp/awtk_external-build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "No install step for 'awtk_external'"
	cd /home/<USER>/Studio/600-codes/awtk_prj/build/awtk-build && /usr/bin/cmake -E echo_append
	cd /home/<USER>/Studio/600-codes/awtk_prj/build/awtk-build && /usr/bin/cmake -E touch /home/<USER>/Studio/600-codes/awtk_prj/build/awtk_external-prefix/src/awtk_external-stamp/awtk_external-install

awtk_external-prefix/src/awtk_external-stamp/awtk_external-mkdir:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Creating directories for 'awtk_external'"
	/usr/bin/cmake -E make_directory /home/<USER>/Studio/600-codes/awtk_prj/libs/awtk
	/usr/bin/cmake -E make_directory /home/<USER>/Studio/600-codes/awtk_prj/build/awtk-build
	/usr/bin/cmake -E make_directory /home/<USER>/Studio/600-codes/awtk_prj/build/awtk_external-prefix
	/usr/bin/cmake -E make_directory /home/<USER>/Studio/600-codes/awtk_prj/build/awtk_external-prefix/tmp
	/usr/bin/cmake -E make_directory /home/<USER>/Studio/600-codes/awtk_prj/build/awtk_external-prefix/src/awtk_external-stamp
	/usr/bin/cmake -E make_directory /home/<USER>/Studio/600-codes/awtk_prj/build/awtk_external-prefix/src
	/usr/bin/cmake -E make_directory /home/<USER>/Studio/600-codes/awtk_prj/build/awtk_external-prefix/src/awtk_external-stamp
	/usr/bin/cmake -E touch /home/<USER>/Studio/600-codes/awtk_prj/build/awtk_external-prefix/src/awtk_external-stamp/awtk_external-mkdir

awtk_external-prefix/src/awtk_external-stamp/awtk_external-patch: awtk_external-prefix/src/awtk_external-stamp/awtk_external-update
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "No patch step for 'awtk_external'"
	/usr/bin/cmake -E echo_append
	/usr/bin/cmake -E touch /home/<USER>/Studio/600-codes/awtk_prj/build/awtk_external-prefix/src/awtk_external-stamp/awtk_external-patch

awtk_external-prefix/src/awtk_external-stamp/awtk_external-update: awtk_external-prefix/src/awtk_external-stamp/awtk_external-download
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "No update step for 'awtk_external'"
	/usr/bin/cmake -E echo_append
	/usr/bin/cmake -E touch /home/<USER>/Studio/600-codes/awtk_prj/build/awtk_external-prefix/src/awtk_external-stamp/awtk_external-update

awtk_external: CMakeFiles/awtk_external
awtk_external: CMakeFiles/awtk_external-complete
awtk_external: awtk_external-prefix/src/awtk_external-stamp/awtk_external-build
awtk_external: awtk_external-prefix/src/awtk_external-stamp/awtk_external-configure
awtk_external: awtk_external-prefix/src/awtk_external-stamp/awtk_external-download
awtk_external: awtk_external-prefix/src/awtk_external-stamp/awtk_external-install
awtk_external: awtk_external-prefix/src/awtk_external-stamp/awtk_external-mkdir
awtk_external: awtk_external-prefix/src/awtk_external-stamp/awtk_external-patch
awtk_external: awtk_external-prefix/src/awtk_external-stamp/awtk_external-update
awtk_external: CMakeFiles/awtk_external.dir/build.make
.PHONY : awtk_external

# Rule to build all files generated by this target.
CMakeFiles/awtk_external.dir/build: awtk_external
.PHONY : CMakeFiles/awtk_external.dir/build

CMakeFiles/awtk_external.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/awtk_external.dir/cmake_clean.cmake
.PHONY : CMakeFiles/awtk_external.dir/clean

CMakeFiles/awtk_external.dir/depend:
	cd /home/<USER>/Studio/600-codes/awtk_prj/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Studio/600-codes/awtk_prj /home/<USER>/Studio/600-codes/awtk_prj /home/<USER>/Studio/600-codes/awtk_prj/build /home/<USER>/Studio/600-codes/awtk_prj/build /home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles/awtk_external.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/awtk_external.dir/depend

