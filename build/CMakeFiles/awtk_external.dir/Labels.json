{"sources": [{"file": "/home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles/awtk_external"}, {"file": "/home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles/awtk_external.rule"}, {"file": "/home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles/awtk_external-complete.rule"}, {"file": "/home/<USER>/Studio/600-codes/awtk_prj/build/awtk_external-prefix/src/awtk_external-stamp/awtk_external-build.rule"}, {"file": "/home/<USER>/Studio/600-codes/awtk_prj/build/awtk_external-prefix/src/awtk_external-stamp/awtk_external-configure.rule"}, {"file": "/home/<USER>/Studio/600-codes/awtk_prj/build/awtk_external-prefix/src/awtk_external-stamp/awtk_external-download.rule"}, {"file": "/home/<USER>/Studio/600-codes/awtk_prj/build/awtk_external-prefix/src/awtk_external-stamp/awtk_external-install.rule"}, {"file": "/home/<USER>/Studio/600-codes/awtk_prj/build/awtk_external-prefix/src/awtk_external-stamp/awtk_external-mkdir.rule"}, {"file": "/home/<USER>/Studio/600-codes/awtk_prj/build/awtk_external-prefix/src/awtk_external-stamp/awtk_external-patch.rule"}, {"file": "/home/<USER>/Studio/600-codes/awtk_prj/build/awtk_external-prefix/src/awtk_external-stamp/awtk_external-update.rule"}], "target": {"labels": ["awtk_external"], "name": "awtk_external"}}