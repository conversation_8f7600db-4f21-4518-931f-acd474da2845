# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Studio/600-codes/awtk_prj

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Studio/600-codes/awtk_prj/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/awtk_external.dir/all
all: CMakeFiles/CoffeeMachineApp.dir/all
all: CMakeFiles/test_app.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/awtk_external.dir/clean
clean: CMakeFiles/copy_awtk_lib.dir/clean
clean: CMakeFiles/CoffeeMachineApp.dir/clean
clean: CMakeFiles/test_app.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/awtk_external.dir

# All Build rule for target.
CMakeFiles/awtk_external.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/awtk_external.dir/build.make CMakeFiles/awtk_external.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/awtk_external.dir/build.make CMakeFiles/awtk_external.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles --progress-num=6,7,8,9,10,11,12,13 "Built target awtk_external"
.PHONY : CMakeFiles/awtk_external.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/awtk_external.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles 8
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/awtk_external.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles 0
.PHONY : CMakeFiles/awtk_external.dir/rule

# Convenience name for target.
awtk_external: CMakeFiles/awtk_external.dir/rule
.PHONY : awtk_external

# clean rule for target.
CMakeFiles/awtk_external.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/awtk_external.dir/build.make CMakeFiles/awtk_external.dir/clean
.PHONY : CMakeFiles/awtk_external.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/copy_awtk_lib.dir

# All Build rule for target.
CMakeFiles/copy_awtk_lib.dir/all: CMakeFiles/awtk_external.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/copy_awtk_lib.dir/build.make CMakeFiles/copy_awtk_lib.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/copy_awtk_lib.dir/build.make CMakeFiles/copy_awtk_lib.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles --progress-num=14 "Built target copy_awtk_lib"
.PHONY : CMakeFiles/copy_awtk_lib.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/copy_awtk_lib.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles 9
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/copy_awtk_lib.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles 0
.PHONY : CMakeFiles/copy_awtk_lib.dir/rule

# Convenience name for target.
copy_awtk_lib: CMakeFiles/copy_awtk_lib.dir/rule
.PHONY : copy_awtk_lib

# clean rule for target.
CMakeFiles/copy_awtk_lib.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/copy_awtk_lib.dir/build.make CMakeFiles/copy_awtk_lib.dir/clean
.PHONY : CMakeFiles/copy_awtk_lib.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/CoffeeMachineApp.dir

# All Build rule for target.
CMakeFiles/CoffeeMachineApp.dir/all: CMakeFiles/awtk_external.dir/all
CMakeFiles/CoffeeMachineApp.dir/all: CMakeFiles/copy_awtk_lib.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CoffeeMachineApp.dir/build.make CMakeFiles/CoffeeMachineApp.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CoffeeMachineApp.dir/build.make CMakeFiles/CoffeeMachineApp.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles --progress-num=1,2,3,4,5 "Built target CoffeeMachineApp"
.PHONY : CMakeFiles/CoffeeMachineApp.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/CoffeeMachineApp.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles 14
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/CoffeeMachineApp.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles 0
.PHONY : CMakeFiles/CoffeeMachineApp.dir/rule

# Convenience name for target.
CoffeeMachineApp: CMakeFiles/CoffeeMachineApp.dir/rule
.PHONY : CoffeeMachineApp

# clean rule for target.
CMakeFiles/CoffeeMachineApp.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CoffeeMachineApp.dir/build.make CMakeFiles/CoffeeMachineApp.dir/clean
.PHONY : CMakeFiles/CoffeeMachineApp.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_app.dir

# All Build rule for target.
CMakeFiles/test_app.dir/all: CMakeFiles/awtk_external.dir/all
CMakeFiles/test_app.dir/all: CMakeFiles/copy_awtk_lib.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_app.dir/build.make CMakeFiles/test_app.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_app.dir/build.make CMakeFiles/test_app.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles --progress-num=15,16,17,18 "Built target test_app"
.PHONY : CMakeFiles/test_app.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_app.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_app.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Studio/600-codes/awtk_prj/build/CMakeFiles 0
.PHONY : CMakeFiles/test_app.dir/rule

# Convenience name for target.
test_app: CMakeFiles/test_app.dir/rule
.PHONY : test_app

# clean rule for target.
CMakeFiles/test_app.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_app.dir/build.make CMakeFiles/test_app.dir/clean
.PHONY : CMakeFiles/test_app.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

