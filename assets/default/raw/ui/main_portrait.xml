<?xml version="1.0" encoding="UTF-8" ?>
<window name="main_window" w="100%" h="100%" text="智能家居控制">
  <view x="0" y="0" w="100%" h="100%" children_layout="default(c=1,r=0,m=10,s=10)">
    
    <!-- 标题栏 -->
    <view x="0" y="0" w="100%" h="60" style="bg_color=0xFF2196F3">
      <label x="c" y="m" w="200" h="40" text="智能家居控制中心" 
             style="text_color=white;font_size=18;text_align_h=center;text_align_v=middle"/>
    </view>
    
    <!-- 传感器数据显示区域 -->
    <view x="0" y="70" w="100%" h="200" style="bg_color=0xFFF5F5F5;border_color=0xFFE0E0E0;border=1;round_radius=8">
      <label x="10" y="10" w="100" h="30" text="传感器数据" 
             style="text_color=0xFF333333;font_size=16;font_weight=bold"/>
      
      <view x="10" y="50" w="100%-20" h="40" children_layout="default(c=3,r=1,m=10,s=10)">
        <!-- 温度显示 -->
        <view x="0" y="0" w="33%" h="40" style="bg_color=white;border_color=0xFFDDDDDD;border=1;round_radius=4">
          <label x="c" y="5" w="100%" h="15" text="温度" 
                 style="text_color=0xFF666666;font_size=12;text_align_h=center"/>
          <label name="temperature_label" x="c" y="20" w="100%" h="15" text="25.6°C" 
                 style="text_color=0xFF2196F3;font_size=14;font_weight=bold;text_align_h=center"/>
        </view>
        
        <!-- 湿度显示 -->
        <view x="33%" y="0" w="33%" h="40" style="bg_color=white;border_color=0xFFDDDDDD;border=1;round_radius=4">
          <label x="c" y="5" w="100%" h="15" text="湿度" 
                 style="text_color=0xFF666666;font_size=12;text_align_h=center"/>
          <label name="humidity_label" x="c" y="20" w="100%" h="15" text="65%" 
                 style="text_color=0xFF4CAF50;font_size=14;font_weight=bold;text_align_h=center"/>
        </view>
        
        <!-- 空气质量显示 -->
        <view x="66%" y="0" w="34%" h="40" style="bg_color=white;border_color=0xFFDDDDDD;border=1;round_radius=4">
          <label x="c" y="5" w="100%" h="15" text="空气质量" 
                 style="text_color=0xFF666666;font_size=12;text_align_h=center"/>
          <label name="air_quality_label" x="c" y="20" w="100%" h="15" text="优秀" 
                 style="text_color=0xFF4CAF50;font_size=14;font_weight=bold;text_align_h=center"/>
        </view>
      </view>
    </view>
    
    <!-- 设备控制区域 -->
    <view x="0" y="280" w="100%" h="100%-290" style="bg_color=0xFFF5F5F5;border_color=0xFFE0E0E0;border=1;round_radius=8">
      <label x="10" y="10" w="100" h="30" text="设备控制" 
             style="text_color=0xFF333333;font_size=16;font_weight=bold"/>
      
      <!-- 设备控制网格 -->
      <view x="10" y="50" w="100%-20" h="100%-60" children_layout="default(c=2,r=2,m=10,s=15)">
        
        <!-- 电灯控制 -->
        <button name="light_button" x="0" y="0" w="50%-7.5" h="45%" text="电灯" 
                style="bg_color=0xFFFFC107;text_color=white;font_size=16;round_radius=8;border=0">
          <image x="c" y="10" w="32" h="32" image="light_icon"/>
          <label x="c" y="50" w="100%" h="20" text="电灯" 
                 style="text_color=white;text_align_h=center;font_size=14"/>
        </button>
        
        <!-- 窗帘控制 -->
        <button name="curtain_button" x="50%+7.5" y="0" w="50%-7.5" h="45%" text="窗帘" 
                style="bg_color=0xFF795548;text_color=white;font_size=16;round_radius=8;border=0">
          <image x="c" y="10" w="32" h="32" image="curtain_icon"/>
          <label x="c" y="50" w="100%" h="20" text="窗帘" 
                 style="text_color=white;text_align_h=center;font_size=14"/>
        </button>
        
        <!-- 空调控制 -->
        <button name="ac_button" x="0" y="55%" w="50%-7.5" h="45%" text="空调" 
                style="bg_color=0xFF03A9F4;text_color=white;font_size=16;round_radius=8;border=0">
          <image x="c" y="10" w="32" h="32" image="ac_icon"/>
          <label x="c" y="50" w="100%" h="20" text="空调" 
                 style="text_color=white;text_align_h=center;font_size=14"/>
        </button>
        
        <!-- 更多设置 -->
        <button x="50%+7.5" y="55%" w="50%-7.5" h="45%" text="设置" 
                style="bg_color=0xFF9E9E9E;text_color=white;font_size=16;round_radius=8;border=0">
          <image x="c" y="10" w="32" h="32" image="settings_icon"/>
          <label x="c" y="50" w="100%" h="20" text="设置" 
                 style="text_color=white;text_align_h=center;font_size=14"/>
        </button>
        
      </view>
    </view>
    
  </view>
</window> 