cmake_minimum_required(VERSION 3.10)

# 项目基本信息
project(SmartHomeApp 
    VERSION 1.0.0
    DESCRIPTION "跨平台智能家居应用"
    LANGUAGES C CXX
)

# 设置C++标准
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置构建类型
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# 平台检测
if(ANDROID)
    message(STATUS "构建目标平台: Android")
    set(PLATFORM_ANDROID ON)
elseif(IOS)
    message(STATUS "构建目标平台: iOS") 
    set(PLATFORM_IOS ON)
else()
    message(STATUS "构建目标平台: Desktop")
    set(PLATFORM_DESKTOP ON)
endif()

# 包含目录
include_directories(
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/libs/awtk/src
    ${CMAKE_SOURCE_DIR}/libs/awtk/src/ext_widgets
    ${CMAKE_SOURCE_DIR}/libs/awtk/3rd
)

# 编译选项
if(MSVC)
    add_compile_options(/W3)
else()
    add_compile_options(-Wall -Wextra)
endif()

# 添加DEBUG宏定义（用于调试模式）
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    add_compile_definitions(DEBUG)
    message(STATUS "DEBUG模式已启用，将显示详细日志")
endif()

# 平台特定配置
if(PLATFORM_ANDROID)
    # Android NDK配置
    set(CMAKE_SYSTEM_NAME Android)
    set(CMAKE_ANDROID_ARCH_ABI armeabi-v7a)
    set(CMAKE_ANDROID_NDK_TOOLCHAIN_VERSION clang)
    set(CMAKE_ANDROID_STL_TYPE c++_shared)
    
    # Android特定编译选项
    add_compile_definitions(ANDROID_PLATFORM)
    
elseif(PLATFORM_IOS)
    # iOS配置
    set(CMAKE_OSX_DEPLOYMENT_TARGET "9.0")
    set(CMAKE_XCODE_ATTRIBUTE_DEVELOPMENT_TEAM "")
    set(CMAKE_XCODE_ATTRIBUTE_CODE_SIGN_IDENTITY "iPhone Developer")
    
    # iOS特定编译选项
    add_compile_definitions(IOS_PLATFORM)
    
else()
    # Desktop平台配置
    add_compile_definitions(DESKTOP_PLATFORM)
endif()

# AWTK库配置
set(AWTK_ROOT_DIR ${CMAKE_SOURCE_DIR}/libs/awtk)

# 使用ExternalProject构建AWTK
include(ExternalProject)

# 设置AWTK构建目录
set(AWTK_BUILD_DIR ${CMAKE_BINARY_DIR}/awtk-build)
set(AWTK_INSTALL_DIR ${CMAKE_BINARY_DIR}/awtk-install)

# 构建AWTK库
ExternalProject_Add(awtk_external
    SOURCE_DIR ${AWTK_ROOT_DIR}
    BINARY_DIR ${AWTK_BUILD_DIR}
    CONFIGURE_COMMAND ""
    BUILD_COMMAND scons -C ${AWTK_ROOT_DIR}
    INSTALL_COMMAND ""
    BUILD_ALWAYS OFF
    BUILD_BYPRODUCTS 
        ${AWTK_ROOT_DIR}/bin/libawtk.so
        ${AWTK_ROOT_DIR}/bin/libawtk.a
)

# 复制AWTK库到build目录的自定义目标
add_custom_command(
    OUTPUT ${CMAKE_BINARY_DIR}/bin/libawtk.so
    COMMAND ${CMAKE_COMMAND} -E copy ${AWTK_ROOT_DIR}/bin/libawtk.so ${CMAKE_BINARY_DIR}/bin/libawtk.so
    DEPENDS awtk_external
    COMMENT "复制AWTK库到build目录"
)

add_custom_target(copy_awtk_lib DEPENDS ${CMAKE_BINARY_DIR}/bin/libawtk.so)

# 创建AWTK导入库 - 使用build目录中的库
add_library(awtk SHARED IMPORTED)
set_target_properties(awtk PROPERTIES
    IMPORTED_LOCATION ${CMAKE_BINARY_DIR}/bin/libawtk.so
    IMPORTED_NO_SONAME ON
    INTERFACE_INCLUDE_DIRECTORIES "${AWTK_ROOT_DIR}/src;${AWTK_ROOT_DIR}/src/ext_widgets;${AWTK_ROOT_DIR}/3rd"
)

# 确保AWTK在主项目之前构建
add_dependencies(awtk copy_awtk_lib)

# 源文件
set(SOURCES
    src/main.cpp
    src/ui_loader.cpp
    src/device_manager.cpp
    src/assets.c
)

# 头文件
set(HEADERS
    include/device_manager.h
    include/ui_loader.h
    include/common.h
)

# 创建可执行文件
if(PLATFORM_ANDROID)
    # Android动态库
    add_library(${PROJECT_NAME} SHARED ${SOURCES} ${HEADERS})
    target_compile_definitions(${PROJECT_NAME} PRIVATE WITH_JNI)
elseif(PLATFORM_IOS)
    # iOS静态库
    add_library(${PROJECT_NAME} STATIC ${SOURCES} ${HEADERS})
else()
    # Desktop可执行文件
    add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})
endif()

# 确保主项目依赖于AWTK构建
add_dependencies(${PROJECT_NAME} awtk_external)

# 设置RPATH以便运行时找到AWTK库
set_target_properties(${PROJECT_NAME} PROPERTIES
    INSTALL_RPATH "${CMAKE_SOURCE_DIR}/libs/awtk/bin"
    BUILD_WITH_INSTALL_RPATH TRUE
)

# 链接AWTK库
target_link_libraries(${PROJECT_NAME} 
    awtk
)

# 平台特定链接配置
if(PLATFORM_ANDROID)
    # Android特定库
    find_library(log-lib log)
    find_library(android-lib android)
    target_link_libraries(${PROJECT_NAME} 
        ${log-lib}
        ${android-lib}
    )
    
elseif(PLATFORM_IOS)
    # iOS特定框架
    target_link_libraries(${PROJECT_NAME}
        "-framework Foundation"
        "-framework UIKit" 
        "-framework QuartzCore"
        "-framework OpenGLES"
    )
    
else()
    # Desktop平台库
    if(WIN32)
        target_link_libraries(${PROJECT_NAME} 
            opengl32
            gdi32
            user32
        )
    elseif(APPLE)
        target_link_libraries(${PROJECT_NAME}
            "-framework OpenGL"
            "-framework Cocoa"
        )
    else()
        # Linux
        find_package(PkgConfig REQUIRED)
        pkg_check_modules(GTK3 REQUIRED gtk+-3.0)
        target_link_libraries(${PROJECT_NAME} 
            ${GTK3_LIBRARIES}
            GL
            pthread
        )
        target_include_directories(${PROJECT_NAME} PRIVATE ${GTK3_INCLUDE_DIRS})
    endif()
endif()

# 设置输出目录
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
)

# 创建测试程序
add_executable(test_app test_app.cpp src/ui_loader.cpp src/device_manager.cpp)
target_link_libraries(test_app awtk)
add_dependencies(test_app awtk_external)

# 设置测试程序输出目录
set_target_properties(test_app PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 安装配置
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

# 复制资源文件
install(DIRECTORY assets/ DESTINATION assets)

# 打印配置信息
message(STATUS "项目名称: ${PROJECT_NAME}")
message(STATUS "构建类型: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++标准: ${CMAKE_CXX_STANDARD}")
message(STATUS "安装前缀: ${CMAKE_INSTALL_PREFIX}") 