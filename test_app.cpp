#include "awtk.h"
#include "base/theme_default.h"
#include "ui_loader.h"
#include "device_manager.h"

// 简单的主题数据结构
static const uint8_t simple_theme_data[] = {
    // theme_header_t
    0x54, 0x4B, 0x54, 0x48,  // magic: "TKTH" 
    0x01, 0x00, 0x00, 0x00,  // version: 1
    0x00, 0x00, 0x00, 0x00,  // nr: 0 (no styles)
};

/**
 * 测试不同屏幕分辨率的UI布局选择
 */
void test_ui_layout_selection() {
    printf("\n=== 测试UI布局选择逻辑 ===\n");
    
    // 测试用例
    struct {
        uint32_t width;
        uint32_t height;
        const char* description;
        bool_t expected_landscape;
    } test_cases[] = {
        {320, 480, "竖屏手机", FALSE},
        {480, 320, "横屏手机", FALSE},
        {800, 450, "16:9横屏", TRUE},
        {1920, 1080, "16:9高清", TRUE},
        {1024, 768, "4:3平板", FALSE},
        {1366, 768, "笔记本屏幕", TRUE},
        {1280, 720, "16:9标准", TRUE},
    };
    
    int num_tests = sizeof(test_cases) / sizeof(test_cases[0]);
    
    for (int i = 0; i < num_tests; i++) {
        bool_t result = is_landscape_16_9(test_cases[i].width, test_cases[i].height);
        const char* status = (result == test_cases[i].expected_landscape) ? "✓ 通过" : "✗ 失败";
        
        printf("测试 %d: %s (%dx%d) - %s\n", 
               i + 1, test_cases[i].description, 
               test_cases[i].width, test_cases[i].height, status);
        
        if (result != test_cases[i].expected_landscape) {
            printf("  期望: %s, 实际: %s\n", 
                   test_cases[i].expected_landscape ? "横屏16:9" : "其他布局",
                   result ? "横屏16:9" : "其他布局");
        }
    }
}

/**
 * 测试设备管理器功能
 */
void test_device_manager() {
    printf("\n=== 测试设备管理器功能 ===\n");
    
    // 初始化设备管理器
    if (device_manager_init() != APP_OK) {
        printf("✗ 设备管理器初始化失败\n");
        return;
    }
    printf("✓ 设备管理器初始化成功\n");
    
    // 测试传感器数据获取
    sensor_data_t sensor_data;
    if (get_sensor_data(&sensor_data) == APP_OK) {
        printf("✓ 传感器数据获取成功\n");
        printf("  温度: %.1f°C\n", sensor_data.temperature);
        printf("  湿度: %.1f%%\n", sensor_data.humidity);
        printf("  空气质量: %d\n", sensor_data.air_quality);
    } else {
        printf("✗ 传感器数据获取失败\n");
    }
    
    // 测试设备控制
    printf("✓ 测试设备控制功能\n");
    
    // 测试电灯控制
    if (control_light(DEVICE_STATUS_ON) == APP_OK) {
        printf("  ✓ 电灯开启成功\n");
    }
    if (control_light(DEVICE_STATUS_OFF) == APP_OK) {
        printf("  ✓ 电灯关闭成功\n");
    }
    
    // 测试窗帘控制
    if (control_curtain(DEVICE_STATUS_ON) == APP_OK) {
        printf("  ✓ 窗帘打开成功\n");
    }
    if (control_curtain(DEVICE_STATUS_OFF) == APP_OK) {
        printf("  ✓ 窗帘关闭成功\n");
    }
    
    // 测试空调控制
    if (control_ac(DEVICE_STATUS_ON) == APP_OK) {
        printf("  ✓ 空调开启成功\n");
    }
    if (set_ac_temperature(26) == APP_OK) {
        printf("  ✓ 空调温度设置成功\n");
    }
    if (control_ac(DEVICE_STATUS_OFF) == APP_OK) {
        printf("  ✓ 空调关闭成功\n");
    }
    
    // 获取设备状态
    device_control_t control;
    if (get_device_control_status(&control) == APP_OK) {
        printf("✓ 设备状态获取成功\n");
        printf("  电灯状态: %s\n", control.light_status == DEVICE_STATUS_ON ? "开启" : "关闭");
        printf("  窗帘状态: %s\n", control.curtain_status == DEVICE_STATUS_ON ? "打开" : "关闭");
        printf("  空调状态: %s\n", control.ac_status == DEVICE_STATUS_ON ? "开启" : "关闭");
        printf("  空调温度: %d°C\n", control.ac_temperature);
    }
    
    // 清理设备管理器
    device_manager_cleanup();
    printf("✓ 设备管理器清理完成\n");
}

/**
 * 主测试函数
 */
int main() {
    printf("智能家居应用功能测试\n");
    printf("====================\n");
    
    // 测试UI布局选择逻辑
    test_ui_layout_selection();
    
    // 测试设备管理器功能
    test_device_manager();
    
    printf("\n=== 测试完成 ===\n");
    return 0;
}
