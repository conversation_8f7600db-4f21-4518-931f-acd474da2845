#include "awtk.h"
#include "base/theme_default.h"
#include "ui_loader.h"
#include "device_manager.h"
#include <unistd.h>  // for sleep

// 简单的主题数据结构
static const uint8_t simple_theme_data[] = {
    // theme_header_t
    0x54, 0x4B, 0x54, 0x48,  // magic: "TKTH" 
    0x01, 0x00, 0x00, 0x00,  // version: 1
    0x00, 0x00, 0x00, 0x00,  // nr: 0 (no styles)
};

/**
 * 测试不同屏幕分辨率的UI布局选择
 */
void test_ui_layout_selection() {
    printf("\n=== 测试UI布局选择逻辑 ===\n");
    
    // 测试用例
    struct {
        uint32_t width;
        uint32_t height;
        const char* description;
        bool_t expected_landscape;
    } test_cases[] = {
        {320, 480, "竖屏手机", FALSE},
        {480, 320, "横屏手机", FALSE},
        {800, 450, "16:9横屏", TRUE},
        {1920, 1080, "16:9高清", TRUE},
        {1024, 768, "4:3平板", FALSE},
        {1366, 768, "笔记本屏幕", TRUE},
        {1280, 720, "16:9标准", TRUE},
    };
    
    int num_tests = sizeof(test_cases) / sizeof(test_cases[0]);
    
    for (int i = 0; i < num_tests; i++) {
        bool_t result = is_landscape_16_9(test_cases[i].width, test_cases[i].height);
        const char* status = (result == test_cases[i].expected_landscape) ? "✓ 通过" : "✗ 失败";
        
        printf("测试 %d: %s (%dx%d) - %s\n", 
               i + 1, test_cases[i].description, 
               test_cases[i].width, test_cases[i].height, status);
        
        if (result != test_cases[i].expected_landscape) {
            printf("  期望: %s, 实际: %s\n", 
                   test_cases[i].expected_landscape ? "横屏16:9" : "其他布局",
                   result ? "横屏16:9" : "其他布局");
        }
    }
}

/**
 * 测试咖啡机管理器功能
 */
void test_coffee_machine_manager() {
    printf("\n=== 测试咖啡机管理器功能 ===\n");

    // 初始化咖啡机管理器
    if (coffee_machine_init() != APP_OK) {
        printf("✗ 咖啡机管理器初始化失败\n");
        return;
    }
    printf("✓ 咖啡机管理器初始化成功\n");

    // 测试咖啡信息获取
    printf("✓ 测试咖啡信息获取\n");
    for (int i = 0; i < COFFEE_TYPE_COUNT; i++) {
        const coffee_info_t* info = get_coffee_info((coffee_type_t)i);
        if (info != NULL) {
            printf("  %s: %s, %d分钟\n", info->name, info->ingredients, info->brew_time_mins);
        }
    }

    // 测试咖啡选择
    printf("✓ 测试咖啡选择功能\n");
    if (select_coffee(COFFEE_TYPE_CAPPUCCINO) == APP_OK) {
        printf("  ✓ 选择Cappuccino成功\n");
    }

    // 测试配方设置
    coffee_recipe_t recipe = {80, 60, 40, 5};  // coffee, milk, foam, sugar
    if (set_coffee_recipe(&recipe) == APP_OK) {
        printf("  ✓ 配方设置成功: 咖啡%dml, 牛奶%dml, 奶泡%dml, 糖%dg\n",
               recipe.coffee_ml, recipe.milk_ml, recipe.foam_ml, recipe.sugar_g);
    }

    // 测试制作流程
    printf("✓ 测试咖啡制作流程\n");
    if (start_brewing() == APP_OK) {
        printf("  ✓ 开始制作咖啡\n");

        // 模拟制作进度
        for (int i = 0; i < 5; i++) {
            int progress = get_brew_progress();
            printf("  制作进度: %d%%\n", progress);
            if (progress >= 100) break;
            sleep(1);  // 等待1秒
        }

        if (stop_brewing() == APP_OK) {
            printf("  ✓ 停止制作成功\n");
        }
    }

    // 获取机器状态
    machine_state_t state;
    if (get_machine_state(&state) == APP_OK) {
        printf("✓ 机器状态获取成功\n");
        const coffee_info_t* info = get_coffee_info(state.current_coffee);
        printf("  当前咖啡: %s\n", info ? info->name : "未知");
        printf("  机器状态: %d\n", state.status);
        printf("  制作进度: %d%%\n", state.brew_progress);
    }

    // 清理咖啡机管理器
    coffee_machine_cleanup();
    printf("✓ 咖啡机管理器清理完成\n");
}

/**
 * 主测试函数
 */
int main() {
    printf("咖啡机应用功能测试\n");
    printf("==================\n");

    // 测试UI布局选择逻辑
    test_ui_layout_selection();

    // 测试咖啡机管理器功能
    test_coffee_machine_manager();

    printf("\n=== 测试完成 ===\n");
    return 0;
}
