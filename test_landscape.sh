#!/bin/bash

# 智能家居应用测试脚本
# 测试横屏16:9模式

echo "=== 智能家居应用测试脚本 ==="
echo "测试横屏16:9模式"

# 设置环境变量来模拟16:9横屏分辨率
export AWTK_LCD_W=800
export AWTK_LCD_H=450

echo "设置模拟屏幕分辨率: ${AWTK_LCD_W}x${AWTK_LCD_H}"
echo "宽高比: $(echo "scale=2; $AWTK_LCD_W / $AWTK_LCD_H" | bc)"

# 进入build目录
cd build

echo "启动应用程序..."
echo "注意观察日志输出中的UI布局选择"
echo "应该显示: '检测到16:9横屏，创建横屏UI布局'"
echo ""
echo "按Ctrl+C退出应用程序"
echo ""

# 运行应用程序
./bin/SmartHomeApp
