#include "device_manager.h"
#include <time.h>
#include <stdlib.h>

// 内部数据结构
static machine_state_t g_machine_state;
static bool_t g_initialized = FALSE;

// 咖啡信息数据
static const coffee_info_t g_coffee_info[COFFEE_TYPE_COUNT] = {
    {
        COFFEE_TYPE_CAPPUCCINO,
        "Cappuccino",
        "Milk, Espresso, Foam",
        2,
        {60, 60, 60, 0}  // coffee, milk, foam, sugar
    },
    {
        COFFEE_TYPE_ESPRESSO,
        "Espresso",
        "Milk, Espresso",
        2,
        {60, 60, 0, 0}
    },
    {
        COFFEE_TYPE_LATTE,
        "Latte",
        "Coffee, Foam",
        3,
        {60, 0, 60, 0}
    },
    {
        COFFEE_TYPE_MACCHIATO,
        "Macchiato",
        "Milk foam, Espresso",
        4,
        {60, 0, 60, 0}
    }
};

// 内部辅助函数
static void update_brew_progress(void);

app_result_t coffee_machine_init(void) {
    if (g_initialized) {
        APP_LOG("咖啡机管理器已经初始化");
        return APP_OK;
    }

    APP_LOG("初始化咖啡机管理器");

    // 初始化随机数种子
    srand((unsigned int)time(NULL));

    // 初始化咖啡机状态
    g_machine_state.status = MACHINE_STATUS_IDLE;
    g_machine_state.current_coffee = COFFEE_TYPE_CAPPUCCINO;
    g_machine_state.current_recipe = g_coffee_info[COFFEE_TYPE_CAPPUCCINO].default_recipe;
    g_machine_state.brew_progress = 0;
    g_machine_state.last_update = (uint32_t)time(NULL);

    g_initialized = TRUE;

    APP_LOG("咖啡机管理器初始化完成");
    APP_LOG("默认选择: %s", g_coffee_info[COFFEE_TYPE_CAPPUCCINO].name);

    return APP_OK;
}

void coffee_machine_cleanup(void) {
    if (!g_initialized) {
        return;
    }

    APP_LOG("清理咖啡机管理器资源");

    // 重置所有状态
    memset(&g_machine_state, 0, sizeof(g_machine_state));

    g_initialized = FALSE;

    APP_LOG("咖啡机管理器清理完成");
}

const coffee_info_t* get_coffee_info(coffee_type_t type) {
    if (type < 0 || type >= COFFEE_TYPE_COUNT) {
        APP_LOG("错误: 无效的咖啡类型: %d", type);
        return NULL;
    }

    return &g_coffee_info[type];
}

const coffee_info_t* get_all_coffee_info(void) {
    return g_coffee_info;
}

app_result_t get_machine_state(machine_state_t* state) {
    if (!g_initialized) {
        APP_LOG("错误: 咖啡机管理器未初始化");
        return APP_ERROR_DEVICE_ERROR;
    }

    if (state == NULL) {
        APP_LOG("错误: 机器状态指针为空");
        return APP_ERROR_DEVICE_ERROR;
    }

    update_brew_progress();
    memcpy(state, &g_machine_state, sizeof(machine_state_t));

    return APP_OK;
}

app_result_t select_coffee(coffee_type_t type) {
    if (!g_initialized) {
        APP_LOG("错误: 咖啡机管理器未初始化");
        return APP_ERROR_DEVICE_ERROR;
    }

    if (type < 0 || type >= COFFEE_TYPE_COUNT) {
        APP_LOG("错误: 无效的咖啡类型: %d", type);
        return APP_ERROR_DEVICE_ERROR;
    }

    g_machine_state.current_coffee = type;
    g_machine_state.current_recipe = g_coffee_info[type].default_recipe;
    g_machine_state.last_update = (uint32_t)time(NULL);

    APP_LOG("选择咖啡: %s", g_coffee_info[type].name);

    return APP_OK;
}

app_result_t set_coffee_recipe(const coffee_recipe_t* recipe) {
    if (!g_initialized) {
        APP_LOG("错误: 咖啡机管理器未初始化");
        return APP_ERROR_DEVICE_ERROR;
    }

    if (recipe == NULL) {
        APP_LOG("错误: 咖啡配方指针为空");
        return APP_ERROR_DEVICE_ERROR;
    }

    // 验证配方参数范围
    if (recipe->coffee_ml < 0 || recipe->coffee_ml > 100 ||
        recipe->milk_ml < 0 || recipe->milk_ml > 100 ||
        recipe->foam_ml < 0 || recipe->foam_ml > 100 ||
        recipe->sugar_g < 0 || recipe->sugar_g > 20) {
        APP_LOG("错误: 配方参数超出有效范围");
        return APP_ERROR_DEVICE_ERROR;
    }

    memcpy(&g_machine_state.current_recipe, recipe, sizeof(coffee_recipe_t));
    g_machine_state.last_update = (uint32_t)time(NULL);

    APP_LOG("配方设置成功 - 咖啡:%dml, 牛奶:%dml, 奶泡:%dml, 糖:%dg",
            recipe->coffee_ml, recipe->milk_ml, recipe->foam_ml, recipe->sugar_g);

    return APP_OK;
}

app_result_t start_brewing(void) {
    if (!g_initialized) {
        APP_LOG("错误: 咖啡机管理器未初始化");
        return APP_ERROR_DEVICE_ERROR;
    }

    if (g_machine_state.status == MACHINE_STATUS_BREWING) {
        APP_LOG("警告: 咖啡机正在制作中");
        return APP_OK;
    }

    g_machine_state.status = MACHINE_STATUS_BREWING;
    g_machine_state.brew_progress = 0;
    g_machine_state.last_update = (uint32_t)time(NULL);

    APP_LOG("开始制作咖啡: %s", g_coffee_info[g_machine_state.current_coffee].name);

    return APP_OK;
}

app_result_t stop_brewing(void) {
    if (!g_initialized) {
        APP_LOG("错误: 咖啡机管理器未初始化");
        return APP_ERROR_DEVICE_ERROR;
    }

    g_machine_state.status = MACHINE_STATUS_IDLE;
    g_machine_state.brew_progress = 0;
    g_machine_state.last_update = (uint32_t)time(NULL);

    APP_LOG("停止制作咖啡");

    return APP_OK;
}

int get_brew_progress(void) {
    if (!g_initialized) {
        return 0;
    }

    update_brew_progress();
    return g_machine_state.brew_progress;
}

// 内部辅助函数实现

static void update_brew_progress(void) {
    if (g_machine_state.status != MACHINE_STATUS_BREWING) {
        return;
    }

    uint32_t current_time = (uint32_t)time(NULL);
    uint32_t elapsed = current_time - g_machine_state.last_update;

    // 根据咖啡类型计算制作时间
    const coffee_info_t* info = &g_coffee_info[g_machine_state.current_coffee];
    uint32_t total_time = info->brew_time_mins * 60;  // 转换为秒

    // 计算进度百分比
    int new_progress = (int)((elapsed * 100) / total_time);
    if (new_progress > 100) {
        new_progress = 100;
        g_machine_state.status = MACHINE_STATUS_READY;
        APP_LOG("咖啡制作完成: %s", info->name);
    }

    if (new_progress != g_machine_state.brew_progress) {
        g_machine_state.brew_progress = new_progress;
        if (new_progress % 10 == 0) {  // 每10%输出一次日志
            APP_LOG("制作进度: %d%%", new_progress);
        }
    }
}