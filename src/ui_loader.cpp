#include "ui_loader.h"
#include "device_manager.h"
#include <math.h>
#include <stdio.h>
#include <stdlib.h>

// 全局变量
static widget_t* g_current_window = NULL;
static page_type_t g_current_page = PAGE_WELCOME;

// 按钮点击事件回调函数声明
static ret_t on_get_started_click(void* ctx, event_t* e);
static ret_t on_back_button_click(void* ctx, event_t* e);
static ret_t on_settings_button_click(void* ctx, event_t* e);
static ret_t on_coffee_card_click(void* ctx, event_t* e);
static ret_t on_start_brewing_click(void* ctx, event_t* e);
static ret_t on_slider_changed(void* ctx, event_t* e);

// 定时器回调，用于更新制作进度
static ret_t on_brew_progress_timer(const timer_info_t* timer);

bool_t is_landscape_16_9(uint32_t width, uint32_t height) {
    if (width == 0 || height == 0) {
        return FALSE;
    }
    
    float aspect_ratio = (float)width / (float)height;
    float target_ratio = ASPECT_RATIO_16_9;
    
    APP_LOG("屏幕分辨率: %dx%d, 宽高比: %.2f, 目标比例: %.2f", 
            width, height, aspect_ratio, target_ratio);
    
    // 检查是否接近16:9比例
    return fabs(aspect_ratio - target_ratio) <= ASPECT_RATIO_TOLERANCE;
}

widget_t* app_ui_loader(void) {
    // 获取屏幕尺寸
    uint32_t screen_width = system_info()->lcd_w;
    uint32_t screen_height = system_info()->lcd_h;

    APP_LOG("开始加载咖啡机UI界面，屏幕尺寸: %dx%d", screen_width, screen_height);

    // 创建最简单的测试窗口
    widget_t* win = window_create(NULL, 0, 0, screen_width, screen_height);
    if (win == NULL) {
        APP_LOG("窗口创建失败");
        return NULL;
    }

    widget_set_name(win, "test_window");

    // 创建最基本的控件，完全不设置样式
    widget_t* label1 = label_create(win, 10, 50, screen_width - 20, 30);
    widget_set_text_utf8(label1, "COFFEE MACHINE TEST");

    widget_t* label2 = label_create(win, 10, 100, screen_width - 20, 30);
    widget_set_text_utf8(label2, "This is a test label");

    widget_t* label3 = label_create(win, 10, 150, screen_width - 20, 30);
    widget_set_text_utf8(label3, "Can you see this text?");

    // 创建按钮，不设置样式
    widget_t* btn = button_create(win, 50, 200, 200, 40);
    widget_set_text_utf8(btn, "TEST BUTTON");
    widget_set_name(btn, "test_btn");
    widget_on(btn, EVT_CLICK, on_get_started_click, NULL);

    g_current_window = win;
    g_current_page = PAGE_WELCOME;

    APP_LOG("测试UI界面创建成功");
    return win;
}

widget_t* create_welcome_page(void) {
    APP_LOG("创建咖啡机欢迎页面");

    // 尝试从XML文件加载UI
    widget_t* win = window_open("welcome");
    if (win != NULL) {
        APP_LOG("从XML文件加载欢迎页面成功");

        // 手动设置控件样式以确保文字可见
        widget_t* title = widget_lookup(win, "title", TRUE);
        if (title != NULL) {
            widget_set_style_color(title, STYLE_ID_FG_COLOR, 0xFF000000);  // 黑色文字
            widget_set_style_color(title, STYLE_ID_BG_COLOR, 0xFFFFFFFF);  // 白色背景
            widget_set_style_int(title, STYLE_ID_FONT_SIZE, 20);
            APP_LOG("标题样式设置完成");
        }

        widget_t* subtitle = widget_lookup(win, "subtitle", TRUE);
        if (subtitle != NULL) {
            widget_set_style_color(subtitle, STYLE_ID_FG_COLOR, 0xFF000000);  // 黑色文字
            widget_set_style_color(subtitle, STYLE_ID_BG_COLOR, 0xFFFFFFFF);  // 白色背景
            widget_set_style_int(subtitle, STYLE_ID_FONT_SIZE, 14);
            APP_LOG("副标题样式设置完成");
        }

        // 绑定按钮事件
        widget_t* btn = widget_lookup(win, "get_started_btn", TRUE);
        if (btn != NULL) {
            widget_set_style_color(btn, STYLE_ID_FG_COLOR, 0xFFFFFFFF);  // 白色文字
            widget_set_style_color(btn, STYLE_ID_BG_COLOR, 0xFF4CAF50);  // 绿色背景
            widget_set_style_int(btn, STYLE_ID_FONT_SIZE, 16);
            widget_on(btn, EVT_CLICK, on_get_started_click, NULL);
            APP_LOG("Get Started按钮事件绑定成功");
        }

        return win;
    }

    APP_LOG("XML加载失败，使用代码创建UI");

    // 获取屏幕大小
    uint32_t screen_width = system_info()->lcd_w;
    uint32_t screen_height = system_info()->lcd_h;

    // 创建主窗口 - 使用全屏大小
    win = window_create(NULL, 0, 0, screen_width, screen_height);
    widget_set_name(win, "welcome_window");

    // 创建最简单的控件测试文字显示
    widget_t* title = label_create(win, 10, 100, screen_width - 20, 40);
    widget_set_text_utf8(title, "Coffee Machine");

    widget_t* subtitle = label_create(win, 10, 150, screen_width - 20, 60);
    widget_set_text_utf8(subtitle, "Welcome to Coffee Machine App");

    widget_t* btn = button_create(win, 50, 300, 200, 50);
    widget_set_text_utf8(btn, "Get Started");
    widget_set_name(btn, WIDGET_NAME_WELCOME_BTN);
    widget_on(btn, EVT_CLICK, on_get_started_click, NULL);

    APP_LOG("咖啡机欢迎页面创建完成 - 窗口大小: %dx%d", screen_width, screen_height);
    return win;
}

widget_t* create_coffee_selection_page(void) {
    APP_LOG("创建咖啡选择页面");

    // 尝试从XML文件加载UI
    widget_t* win = window_open("coffee_selection");
    if (win != NULL) {
        APP_LOG("从XML文件加载咖啡选择页面成功");

        // 设置页面标题样式
        widget_t* title = widget_lookup(win, "title", TRUE);
        if (title != NULL) {
            widget_set_style_color(title, STYLE_ID_FG_COLOR, 0xFF000000);  // 黑色文字
            widget_set_style_color(title, STYLE_ID_BG_COLOR, 0xFFFFFFFF);  // 白色背景
            widget_set_style_int(title, STYLE_ID_FONT_SIZE, 20);
            APP_LOG("咖啡选择页面标题样式设置完成");
        }

        // 绑定返回按钮事件并设置样式
        widget_t* back_btn = widget_lookup(win, "back_btn", TRUE);
        if (back_btn != NULL) {
            widget_set_style_color(back_btn, STYLE_ID_FG_COLOR, 0xFFFFFFFF);  // 白色文字
            widget_set_style_color(back_btn, STYLE_ID_BG_COLOR, 0xFF666666);  // 灰色背景
            widget_set_style_int(back_btn, STYLE_ID_FONT_SIZE, 14);
            widget_on(back_btn, EVT_CLICK, on_back_button_click, NULL);
            APP_LOG("返回按钮样式设置完成");
        }

        // 绑定咖啡卡片事件并设置样式
        const coffee_info_t* coffee_info = get_all_coffee_info();
        for (int i = 0; i < COFFEE_TYPE_COUNT; i++) {
            char card_name[32];
            snprintf(card_name, sizeof(card_name), "coffee_card_%d", i);
            widget_t* card = widget_lookup(win, card_name, TRUE);
            if (card != NULL) {
                widget_set_prop_int(card, "coffee_type", i);
                widget_on(card, EVT_CLICK, on_coffee_card_click, NULL);
                // 更新卡片文字为咖啡名称并设置样式
                widget_set_text_utf8(card, coffee_info[i].name);
                widget_set_style_color(card, STYLE_ID_FG_COLOR, 0xFFFFFFFF);  // 白色文字
                widget_set_style_color(card, STYLE_ID_BG_COLOR, 0xFF4CAF50);  // 绿色背景
                widget_set_style_int(card, STYLE_ID_FONT_SIZE, 16);
                APP_LOG("咖啡卡片 %s 样式设置完成", coffee_info[i].name);
            }
        }

        return win;
    }

    APP_LOG("XML加载失败，使用代码创建UI");

    // 获取屏幕大小
    uint32_t screen_width = system_info()->lcd_w;
    uint32_t screen_height = system_info()->lcd_h;

    // 创建主窗口
    win = window_create(NULL, 0, 0, screen_width, screen_height);
    widget_set_name(win, "coffee_selection_window");

    // 返回按钮
    widget_t* back_btn = button_create(win, 20, 30, 80, 40);
    widget_set_text_utf8(back_btn, "Back");
    widget_set_name(back_btn, WIDGET_NAME_BACK_BTN);
    widget_on(back_btn, EVT_CLICK, on_back_button_click, NULL);

    // 页面标题
    widget_t* title = label_create(win, 20, 90, screen_width - 40, 50);
    widget_set_text_utf8(title, "Coffee Selection");

    // 创建简单的咖啡选择按钮
    const coffee_info_t* coffee_info = get_all_coffee_info();

    for (int i = 0; i < COFFEE_TYPE_COUNT; i++) {
        int row = i / 2;
        int col = i % 2;
        int x = 30 + col * 140;
        int y = 150 + row * 80;

        // 咖啡选择按钮
        widget_t* card = button_create(win, x, y, 120, 60);
        widget_set_text_utf8(card, coffee_info[i].name);
        widget_set_name(card, WIDGET_NAME_COFFEE_CARD);
        widget_set_prop_int(card, "coffee_type", i);
        widget_on(card, EVT_CLICK, on_coffee_card_click, NULL);
    }

    APP_LOG("咖啡选择页面创建完成 - 窗口大小: %dx%d", screen_width, screen_height);
    return win;
}

widget_t* create_coffee_brewing_page(coffee_type_t coffee_type) {
    APP_LOG("创建咖啡制作页面: %d", coffee_type);

    // 获取咖啡信息
    const coffee_info_t* coffee_info = get_coffee_info(coffee_type);
    if (coffee_info == NULL) {
        APP_LOG("错误: 无效的咖啡类型");
        return NULL;
    }

    // 尝试从XML文件加载UI
    widget_t* win = window_open("coffee_brewing");
    if (win != NULL) {
        APP_LOG("从XML文件加载咖啡制作页面成功");

        // 设置页面标题为咖啡名称
        widget_t* title = widget_lookup(win, "title", TRUE);
        if (title != NULL) {
            widget_set_text_utf8(title, coffee_info->name);
            widget_set_style_color(title, STYLE_ID_FG_COLOR, 0xFF000000);  // 黑色文字
            widget_set_style_color(title, STYLE_ID_BG_COLOR, 0xFFFFFFFF);  // 白色背景
            widget_set_style_int(title, STYLE_ID_FONT_SIZE, 20);
            APP_LOG("咖啡制作页面标题设置为: %s", coffee_info->name);
        }

        // 设置返回按钮样式
        widget_t* back_btn = widget_lookup(win, "back_btn", TRUE);
        if (back_btn != NULL) {
            widget_set_style_color(back_btn, STYLE_ID_FG_COLOR, 0xFFFFFFFF);  // 白色文字
            widget_set_style_color(back_btn, STYLE_ID_BG_COLOR, 0xFF666666);  // 灰色背景
            widget_set_style_int(back_btn, STYLE_ID_FONT_SIZE, 14);
            widget_on(back_btn, EVT_CLICK, on_back_button_click, NULL);
        }

        // 设置滑块标签样式
        const char* slider_labels[] = {"coffee_label", "milk_label", "foam_label", "sugar_label"};
        for (int i = 0; i < 4; i++) {
            widget_t* label = widget_lookup(win, slider_labels[i], TRUE);
            if (label != NULL) {
                widget_set_style_color(label, STYLE_ID_FG_COLOR, 0xFF000000);  // 黑色文字
                widget_set_style_color(label, STYLE_ID_BG_COLOR, 0xFFFFFFFF);  // 白色背景
                widget_set_style_int(label, STYLE_ID_FONT_SIZE, 16);
            }
        }

        // 设置滑块值标签样式
        const char* value_labels[] = {"coffee_value", "milk_value", "foam_value", "sugar_value"};
        for (int i = 0; i < 4; i++) {
            widget_t* label = widget_lookup(win, value_labels[i], TRUE);
            if (label != NULL) {
                widget_set_style_color(label, STYLE_ID_FG_COLOR, 0xFF000000);  // 黑色文字
                widget_set_style_color(label, STYLE_ID_BG_COLOR, 0xFFFFFFFF);  // 白色背景
                widget_set_style_int(label, STYLE_ID_FONT_SIZE, 14);
            }
        }

        // 设置滑块事件
        const char* sliders[] = {"coffee_slider", "milk_slider", "foam_slider", "sugar_slider"};
        for (int i = 0; i < 4; i++) {
            widget_t* slider = widget_lookup(win, sliders[i], TRUE);
            if (slider != NULL) {
                widget_on(slider, EVT_VALUE_CHANGED, on_slider_changed, NULL);
            }
        }

        // 设置开始按钮样式
        widget_t* start_btn = widget_lookup(win, "start_btn", TRUE);
        if (start_btn != NULL) {
            widget_set_style_color(start_btn, STYLE_ID_FG_COLOR, 0xFFFFFFFF);  // 白色文字
            widget_set_style_color(start_btn, STYLE_ID_BG_COLOR, 0xFF4CAF50);  // 绿色背景
            widget_set_style_int(start_btn, STYLE_ID_FONT_SIZE, 18);
            widget_on(start_btn, EVT_CLICK, on_start_brewing_click, NULL);
        }

        APP_LOG("咖啡制作页面样式设置完成");
        return win;
    }

    APP_LOG("XML加载失败，使用代码创建UI");

    // 获取屏幕大小
    uint32_t screen_width = system_info()->lcd_w;
    uint32_t screen_height = system_info()->lcd_h;

    // 创建主窗口
    win = window_create(NULL, 0, 0, screen_width, screen_height);
    widget_set_name(win, "coffee_brewing_window");

    // 设置深色背景
    widget_set_style_color(win, STYLE_ID_BG_COLOR, 0xFF2C2C2C);

    // 顶部导航栏
    // 返回按钮
    widget_t* back_btn = button_create(win, 20, 30, 50, 50);
    widget_set_text_utf8(back_btn, "←");
    widget_set_name(back_btn, WIDGET_NAME_BACK_BTN);
    widget_set_style_color(back_btn, STYLE_ID_FG_COLOR, 0xFFFFFFFF);
    widget_set_style_color(back_btn, STYLE_ID_BG_COLOR, 0x00000000);
    widget_set_style_int(back_btn, STYLE_ID_FONT_SIZE, 28);
    widget_set_style_int(back_btn, STYLE_ID_TEXT_ALIGN_H, ALIGN_H_CENTER);
    widget_set_style_int(back_btn, STYLE_ID_TEXT_ALIGN_V, ALIGN_V_MIDDLE);
    widget_on(back_btn, EVT_CLICK, on_back_button_click, NULL);

    // 设置按钮
    widget_t* settings_btn = button_create(win, screen_width - 70, 30, 50, 50);
    widget_set_text_utf8(settings_btn, "☀");
    widget_set_name(settings_btn, WIDGET_NAME_SETTINGS_BTN);
    widget_set_style_color(settings_btn, STYLE_ID_FG_COLOR, 0xFFFFFFFF);
    widget_set_style_color(settings_btn, STYLE_ID_BG_COLOR, 0x00000000);
    widget_set_style_int(settings_btn, STYLE_ID_FONT_SIZE, 24);
    widget_set_style_int(settings_btn, STYLE_ID_TEXT_ALIGN_H, ALIGN_H_CENTER);
    widget_set_style_int(settings_btn, STYLE_ID_TEXT_ALIGN_V, ALIGN_V_MIDDLE);
    widget_on(settings_btn, EVT_CLICK, on_settings_button_click, NULL);

    // 页面标题
    widget_t* title = label_create(win, 20, 90, screen_width - 40, 50);
    widget_set_text_utf8(title, coffee_info->name);
    widget_set_style_color(title, STYLE_ID_FG_COLOR, 0xFFFFFFFF);
    widget_set_style_color(title, STYLE_ID_BG_COLOR, 0x00000000);
    widget_set_style_int(title, STYLE_ID_FONT_SIZE, 28);
    widget_set_style_int(title, STYLE_ID_TEXT_ALIGN_H, ALIGN_H_LEFT);
    widget_set_style_int(title, STYLE_ID_TEXT_ALIGN_V, ALIGN_V_MIDDLE);

    // 咖啡杯图标
    widget_t* coffee_icon = label_create(win, screen_width/2 - 50, 150, 100, 100);
    widget_set_text_utf8(coffee_icon, "☕");
    widget_set_style_color(coffee_icon, STYLE_ID_FG_COLOR, 0xFFD4AF37);
    widget_set_style_color(coffee_icon, STYLE_ID_BG_COLOR, 0x00000000);
    widget_set_style_int(coffee_icon, STYLE_ID_FONT_SIZE, 64);
    widget_set_style_int(coffee_icon, STYLE_ID_TEXT_ALIGN_H, ALIGN_H_CENTER);
    widget_set_style_int(coffee_icon, STYLE_ID_TEXT_ALIGN_V, ALIGN_V_MIDDLE);

    // 配方滑块区域
    int slider_y = 270;
    int slider_spacing = 55;

    // Coffee 滑块
    widget_t* coffee_label = label_create(win, 40, slider_y, 80, 35);
    widget_set_text_utf8(coffee_label, "Coffee");
    widget_set_style_color(coffee_label, STYLE_ID_FG_COLOR, 0xFFFFFFFF);
    widget_set_style_color(coffee_label, STYLE_ID_BG_COLOR, 0x00000000);
    widget_set_style_int(coffee_label, STYLE_ID_FONT_SIZE, 18);
    widget_set_style_int(coffee_label, STYLE_ID_TEXT_ALIGN_V, ALIGN_V_MIDDLE);

    widget_t* coffee_slider = slider_create(win, 130, slider_y + 5, screen_width - 250, 25);
    widget_set_name(coffee_slider, WIDGET_NAME_COFFEE_SLIDER);
    widget_set_value(coffee_slider, coffee_info->default_recipe.coffee_ml);
    widget_set_prop_int(coffee_slider, WIDGET_PROP_MIN, 0);
    widget_set_prop_int(coffee_slider, WIDGET_PROP_MAX, 100);
    widget_set_style_color(coffee_slider, STYLE_ID_FG_COLOR, 0xFF4CAF50);
    widget_set_style_color(coffee_slider, STYLE_ID_BG_COLOR, 0xFF555555);
    widget_on(coffee_slider, EVT_VALUE_CHANGED, on_slider_changed, NULL);

    char value_text[16];
    snprintf(value_text, sizeof(value_text), "%dml", coffee_info->default_recipe.coffee_ml);
    widget_t* coffee_value = label_create(win, screen_width - 100, slider_y, 80, 35);
    widget_set_text_utf8(coffee_value, value_text);
    widget_set_style_color(coffee_value, STYLE_ID_FG_COLOR, 0xFFAAAAAA);
    widget_set_style_color(coffee_value, STYLE_ID_BG_COLOR, 0x00000000);
    widget_set_style_int(coffee_value, STYLE_ID_FONT_SIZE, 14);
    widget_set_style_int(coffee_value, STYLE_ID_TEXT_ALIGN_H, ALIGN_H_RIGHT);
    widget_set_style_int(coffee_value, STYLE_ID_TEXT_ALIGN_V, ALIGN_V_MIDDLE);

    slider_y += slider_spacing;

    // Milk 滑块
    widget_t* milk_label = label_create(win, 40, slider_y, 80, 35);
    widget_set_text_utf8(milk_label, "Milk");
    widget_set_style_color(milk_label, STYLE_ID_FG_COLOR, 0xFFFFFFFF);
    widget_set_style_color(milk_label, STYLE_ID_BG_COLOR, 0x00000000);
    widget_set_style_int(milk_label, STYLE_ID_FONT_SIZE, 18);
    widget_set_style_int(milk_label, STYLE_ID_TEXT_ALIGN_V, ALIGN_V_MIDDLE);

    widget_t* milk_slider = slider_create(win, 130, slider_y + 5, screen_width - 250, 25);
    widget_set_name(milk_slider, WIDGET_NAME_MILK_SLIDER);
    widget_set_value(milk_slider, coffee_info->default_recipe.milk_ml);
    widget_set_prop_int(milk_slider, WIDGET_PROP_MIN, 0);
    widget_set_prop_int(milk_slider, WIDGET_PROP_MAX, 100);
    widget_set_style_color(milk_slider, STYLE_ID_FG_COLOR, 0xFF4CAF50);
    widget_set_style_color(milk_slider, STYLE_ID_BG_COLOR, 0xFF555555);
    widget_on(milk_slider, EVT_VALUE_CHANGED, on_slider_changed, NULL);

    snprintf(value_text, sizeof(value_text), "%dml", coffee_info->default_recipe.milk_ml);
    widget_t* milk_value = label_create(win, screen_width - 100, slider_y, 80, 35);
    widget_set_text_utf8(milk_value, value_text);
    widget_set_style_color(milk_value, STYLE_ID_FG_COLOR, 0xFFAAAAAA);
    widget_set_style_color(milk_value, STYLE_ID_BG_COLOR, 0x00000000);
    widget_set_style_int(milk_value, STYLE_ID_FONT_SIZE, 14);
    widget_set_style_int(milk_value, STYLE_ID_TEXT_ALIGN_H, ALIGN_H_RIGHT);
    widget_set_style_int(milk_value, STYLE_ID_TEXT_ALIGN_V, ALIGN_V_MIDDLE);

    slider_y += slider_spacing;

    // Foam 滑块
    widget_t* foam_label = label_create(win, 40, slider_y, 80, 35);
    widget_set_text_utf8(foam_label, "Foam");
    widget_set_style_color(foam_label, STYLE_ID_FG_COLOR, 0xFFFFFFFF);
    widget_set_style_color(foam_label, STYLE_ID_BG_COLOR, 0x00000000);
    widget_set_style_int(foam_label, STYLE_ID_FONT_SIZE, 18);
    widget_set_style_int(foam_label, STYLE_ID_TEXT_ALIGN_V, ALIGN_V_MIDDLE);

    widget_t* foam_slider = slider_create(win, 130, slider_y + 5, screen_width - 250, 25);
    widget_set_name(foam_slider, WIDGET_NAME_FOAM_SLIDER);
    widget_set_value(foam_slider, coffee_info->default_recipe.foam_ml);
    widget_set_prop_int(foam_slider, WIDGET_PROP_MIN, 0);
    widget_set_prop_int(foam_slider, WIDGET_PROP_MAX, 100);
    widget_set_style_color(foam_slider, STYLE_ID_FG_COLOR, 0xFF4CAF50);
    widget_set_style_color(foam_slider, STYLE_ID_BG_COLOR, 0xFF555555);
    widget_on(foam_slider, EVT_VALUE_CHANGED, on_slider_changed, NULL);

    snprintf(value_text, sizeof(value_text), "%dml", coffee_info->default_recipe.foam_ml);
    widget_t* foam_value = label_create(win, screen_width - 100, slider_y, 80, 35);
    widget_set_text_utf8(foam_value, value_text);
    widget_set_style_color(foam_value, STYLE_ID_FG_COLOR, 0xFFAAAAAA);
    widget_set_style_color(foam_value, STYLE_ID_BG_COLOR, 0x00000000);
    widget_set_style_int(foam_value, STYLE_ID_FONT_SIZE, 14);
    widget_set_style_int(foam_value, STYLE_ID_TEXT_ALIGN_H, ALIGN_H_RIGHT);
    widget_set_style_int(foam_value, STYLE_ID_TEXT_ALIGN_V, ALIGN_V_MIDDLE);

    slider_y += slider_spacing;

    // Sugar 滑块
    widget_t* sugar_label = label_create(win, 40, slider_y, 80, 35);
    widget_set_text_utf8(sugar_label, "Sugar");
    widget_set_style_color(sugar_label, STYLE_ID_FG_COLOR, 0xFFFFFFFF);
    widget_set_style_color(sugar_label, STYLE_ID_BG_COLOR, 0x00000000);
    widget_set_style_int(sugar_label, STYLE_ID_FONT_SIZE, 18);
    widget_set_style_int(sugar_label, STYLE_ID_TEXT_ALIGN_V, ALIGN_V_MIDDLE);

    widget_t* sugar_slider = slider_create(win, 130, slider_y + 5, screen_width - 250, 25);
    widget_set_name(sugar_slider, WIDGET_NAME_SUGAR_SLIDER);
    widget_set_value(sugar_slider, coffee_info->default_recipe.sugar_g);
    widget_set_prop_int(sugar_slider, WIDGET_PROP_MIN, 0);
    widget_set_prop_int(sugar_slider, WIDGET_PROP_MAX, 20);
    widget_set_style_color(sugar_slider, STYLE_ID_FG_COLOR, 0xFF4CAF50);
    widget_set_style_color(sugar_slider, STYLE_ID_BG_COLOR, 0xFF555555);
    widget_on(sugar_slider, EVT_VALUE_CHANGED, on_slider_changed, NULL);

    snprintf(value_text, sizeof(value_text), "%dg", coffee_info->default_recipe.sugar_g);
    widget_t* sugar_value = label_create(win, screen_width - 100, slider_y, 80, 35);
    widget_set_text_utf8(sugar_value, value_text);
    widget_set_style_color(sugar_value, STYLE_ID_FG_COLOR, 0xFFAAAAAA);
    widget_set_style_color(sugar_value, STYLE_ID_BG_COLOR, 0x00000000);
    widget_set_style_int(sugar_value, STYLE_ID_FONT_SIZE, 14);
    widget_set_style_int(sugar_value, STYLE_ID_TEXT_ALIGN_H, ALIGN_H_RIGHT);
    widget_set_style_int(sugar_value, STYLE_ID_TEXT_ALIGN_V, ALIGN_V_MIDDLE);

    // Start 按钮
    widget_t* start_btn = button_create(win, screen_width/2 - 120, screen_height - 80, 240, 60);
    widget_set_text_utf8(start_btn, "Start");
    widget_set_name(start_btn, WIDGET_NAME_START_BTN);
    widget_set_style_color(start_btn, STYLE_ID_FG_COLOR, 0xFFFFFFFF);
    widget_set_style_color(start_btn, STYLE_ID_BG_COLOR, 0x00000000);
    widget_set_style_color(start_btn, STYLE_ID_BORDER_COLOR, 0xFF888888);
    widget_set_style_int(start_btn, STYLE_ID_BORDER, 2);
    widget_set_style_int(start_btn, STYLE_ID_ROUND_RADIUS, 12);
    widget_set_style_int(start_btn, STYLE_ID_FONT_SIZE, 20);
    widget_set_style_int(start_btn, STYLE_ID_TEXT_ALIGN_H, ALIGN_H_CENTER);
    widget_set_style_int(start_btn, STYLE_ID_TEXT_ALIGN_V, ALIGN_V_MIDDLE);
    widget_on(start_btn, EVT_CLICK, on_start_brewing_click, NULL);

    APP_LOG("咖啡制作页面创建完成 - %s", coffee_info->name);
    return win;
}

app_result_t init_ui_events(widget_t* widget) {
    if (widget == NULL) {
        return APP_ERROR_UI_LOAD_FAILED;
    }

    APP_LOG("UI事件绑定完成");
    return APP_OK;
}

// 页面导航函数实现
app_result_t navigate_to_page(page_type_t page_type, int param) {
    if (g_current_window != NULL) {
        widget_destroy(g_current_window);
        g_current_window = NULL;
    }

    widget_t* new_window = NULL;

    switch (page_type) {
        case PAGE_WELCOME:
            new_window = create_welcome_page();
            break;
        case PAGE_COFFEE_SELECTION:
            new_window = create_coffee_selection_page();
            break;
        case PAGE_COFFEE_BREWING:
            new_window = create_coffee_brewing_page((coffee_type_t)param);
            break;
        default:
            APP_LOG("错误: 未知的页面类型: %d", page_type);
            return APP_ERROR_UI_LOAD_FAILED;
    }

    if (new_window == NULL) {
        APP_LOG("错误: 页面创建失败");
        return APP_ERROR_UI_LOAD_FAILED;
    }

    g_current_window = new_window;
    g_current_page = page_type;

    APP_LOG("页面导航成功: %d", page_type);
    return APP_OK;
}

// Get Started 按钮点击事件
static ret_t on_get_started_click(void* ctx, event_t* e) {
    (void)ctx;
    (void)e;
    APP_LOG("Get Started 按钮被点击");
    navigate_to_page(PAGE_COFFEE_SELECTION, 0);
    return RET_OK;
}

// 返回按钮点击事件
static ret_t on_back_button_click(void* ctx, event_t* e) {
    (void)ctx;
    (void)e;
    APP_LOG("返回按钮被点击");

    if (g_current_page == PAGE_COFFEE_SELECTION) {
        navigate_to_page(PAGE_WELCOME, 0);
    } else if (g_current_page == PAGE_COFFEE_BREWING) {
        navigate_to_page(PAGE_COFFEE_SELECTION, 0);
    }

    return RET_OK;
}

// 设置按钮点击事件
static ret_t on_settings_button_click(void* ctx, event_t* e) {
    (void)ctx;
    (void)e;
    APP_LOG("设置按钮被点击");
    // 这里可以实现设置页面
    return RET_OK;
}

// 咖啡卡片点击事件
static ret_t on_coffee_card_click(void* ctx, event_t* e) {
    (void)ctx;
    widget_t* card = WIDGET(e->target);
    int coffee_type = widget_get_prop_int(card, "coffee_type", 0);

    APP_LOG("咖啡卡片被点击: %d", coffee_type);

    // 选择咖啡类型
    if (select_coffee((coffee_type_t)coffee_type) == APP_OK) {
        navigate_to_page(PAGE_COFFEE_BREWING, coffee_type);
    }

    return RET_OK;
}

// 开始制作按钮点击事件
static ret_t on_start_brewing_click(void* ctx, event_t* e) {
    (void)ctx;
    (void)e;
    APP_LOG("开始制作按钮被点击");

    if (start_brewing() == APP_OK) {
        APP_LOG("开始制作咖啡");
        // 启动进度更新定时器
        timer_add(on_brew_progress_timer, g_current_window, 1000);
    }

    return RET_OK;
}

// 滑块值改变事件
static ret_t on_slider_changed(void* ctx, event_t* e) {
    (void)ctx;
    widget_t* slider = WIDGET(e->target);
    const char* name = slider->name;  // 直接访问name字段
    int value = widget_get_value(slider);

    APP_LOG("滑块 %s 值改变: %d", name ? name : "unknown", value);

    // 更新配方
    machine_state_t state;
    if (get_machine_state(&state) == APP_OK) {
        coffee_recipe_t recipe = state.current_recipe;

        if (name && strcmp(name, WIDGET_NAME_COFFEE_SLIDER) == 0) {
            recipe.coffee_ml = value;
        } else if (name && strcmp(name, WIDGET_NAME_MILK_SLIDER) == 0) {
            recipe.milk_ml = value;
        } else if (name && strcmp(name, WIDGET_NAME_FOAM_SLIDER) == 0) {
            recipe.foam_ml = value;
        } else if (name && strcmp(name, WIDGET_NAME_SUGAR_SLIDER) == 0) {
            recipe.sugar_g = value;
        }

        set_coffee_recipe(&recipe);
    }

    return RET_OK;
}

// 制作进度更新定时器
static ret_t on_brew_progress_timer(const timer_info_t* timer) {
    (void)timer;
    int progress = get_brew_progress();

    if (progress >= 100) {
        APP_LOG("咖啡制作完成!");
        return RET_REMOVE;  // 移除定时器
    }

    return RET_OK;  // 继续定时器
}