#include "ui_loader.h"
#include "device_manager.h"
#include <math.h>
#include <stdio.h>
#include <stdlib.h>

// 全局变量
static widget_t* g_current_window = NULL;
static page_type_t g_current_page = PAGE_WELCOME;

// 按钮点击事件回调函数声明
static ret_t on_get_started_click(void* ctx, event_t* e);
static ret_t on_back_button_click(void* ctx, event_t* e);
static ret_t on_settings_button_click(void* ctx, event_t* e);
static ret_t on_coffee_card_click(void* ctx, event_t* e);
static ret_t on_start_brewing_click(void* ctx, event_t* e);
static ret_t on_slider_changed(void* ctx, event_t* e);

// 定时器回调，用于更新制作进度
static ret_t on_brew_progress_timer(const timer_info_t* timer);

bool_t is_landscape_16_9(uint32_t width, uint32_t height) {
    if (width == 0 || height == 0) {
        return FALSE;
    }
    
    float aspect_ratio = (float)width / (float)height;
    float target_ratio = ASPECT_RATIO_16_9;
    
    APP_LOG("屏幕分辨率: %dx%d, 宽高比: %.2f, 目标比例: %.2f", 
            width, height, aspect_ratio, target_ratio);
    
    // 检查是否接近16:9比例
    return fabs(aspect_ratio - target_ratio) <= ASPECT_RATIO_TOLERANCE;
}

widget_t* app_ui_loader(void) {
    // 获取屏幕尺寸
    uint32_t screen_width = system_info()->lcd_w;
    uint32_t screen_height = system_info()->lcd_h;

    APP_LOG("开始加载咖啡机UI界面，屏幕尺寸: %dx%d", screen_width, screen_height);

    // 创建欢迎页面作为初始页面
    widget_t* widget = create_welcome_page();

    if (widget == NULL) {
        APP_LOG("欢迎页面创建失败");
        return NULL;
    }

    g_current_window = widget;
    g_current_page = PAGE_WELCOME;

    APP_LOG("咖啡机UI界面创建成功，显示欢迎页面");
    return widget;
}

widget_t* create_welcome_page(void) {
    APP_LOG("创建咖啡机欢迎页面");

    // 获取屏幕大小
    uint32_t screen_width = system_info()->lcd_w;
    uint32_t screen_height = system_info()->lcd_h;

    // 创建主窗口 - 使用全屏大小
    widget_t* win = window_create(NULL, 0, 0, screen_width, screen_height);
    widget_set_name(win, "welcome_window");

    // 设置深色背景
    widget_set_style_color(win, STYLE_ID_BG_COLOR, 0xFF2C2C2C);

    // 顶部绿色标识区域（去掉Qt文字）
    widget_t* top_indicator = view_create(win, screen_width/2 - 25, 20, 50, 25);
    widget_set_style_color(top_indicator, STYLE_ID_BG_COLOR, 0xFF4CAF50);
    widget_set_style_int(top_indicator, STYLE_ID_ROUND_RADIUS, 4);

    // 咖啡杯图标区域（使用文字代替图标）
    widget_t* coffee_icon = label_create(win, screen_width/2 - 40, screen_height/2 - 120, 80, 80);
    widget_set_text_utf8(coffee_icon, "☕");
    widget_set_style_color(coffee_icon, STYLE_ID_FG_COLOR, 0xFFD4AF37);  // 金色
    widget_set_style_int(coffee_icon, STYLE_ID_FONT_SIZE, 48);
    widget_set_style_int(coffee_icon, STYLE_ID_TEXT_ALIGN_H, ALIGN_H_CENTER);
    widget_set_style_int(coffee_icon, STYLE_ID_TEXT_ALIGN_V, ALIGN_V_MIDDLE);

    // 主标题
    widget_t* title = label_create(win, 50, screen_height/2 - 20, screen_width - 100, 40);
    widget_set_text_utf8(title, "Coffee Machine");
    widget_set_style_color(title, STYLE_ID_FG_COLOR, 0xFFFFFFFF);
    widget_set_style_int(title, STYLE_ID_FONT_SIZE, 28);
    widget_set_style_int(title, STYLE_ID_TEXT_ALIGN_H, ALIGN_H_CENTER);
    widget_set_style_int(title, STYLE_ID_TEXT_ALIGN_V, ALIGN_V_MIDDLE);

    // 副标题
    widget_t* subtitle = label_create(win, 30, screen_height/2 + 30, screen_width - 60, 60);
    widget_set_text_utf8(subtitle, "pick your blend, tailor your flavors,\nand savor the perfection!");
    widget_set_style_color(subtitle, STYLE_ID_FG_COLOR, 0xFFCCCCCC);
    widget_set_style_int(subtitle, STYLE_ID_FONT_SIZE, 14);
    widget_set_style_int(subtitle, STYLE_ID_TEXT_ALIGN_H, ALIGN_H_CENTER);
    widget_set_style_int(subtitle, STYLE_ID_TEXT_ALIGN_V, ALIGN_V_MIDDLE);

    // Get Started 按钮
    widget_t* get_started_btn = button_create(win, screen_width/2 - 100, screen_height - 120, 200, 50);
    widget_set_text_utf8(get_started_btn, "Get Started →");
    widget_set_name(get_started_btn, WIDGET_NAME_WELCOME_BTN);
    widget_set_style_color(get_started_btn, STYLE_ID_FG_COLOR, 0xFFFFFFFF);
    widget_set_style_color(get_started_btn, STYLE_ID_BG_COLOR, 0x00000000);  // 透明背景
    widget_set_style_color(get_started_btn, STYLE_ID_BORDER_COLOR, 0xFF666666);
    widget_set_style_int(get_started_btn, STYLE_ID_BORDER, 2);
    widget_set_style_int(get_started_btn, STYLE_ID_ROUND_RADIUS, 8);
    widget_set_style_int(get_started_btn, STYLE_ID_FONT_SIZE, 16);

    // 绑定按钮事件
    widget_on(get_started_btn, EVT_CLICK, on_get_started_click, NULL);

    APP_LOG("咖啡机欢迎页面创建完成 - 窗口大小: %dx%d", screen_width, screen_height);
    return win;
}

widget_t* create_coffee_selection_page(void) {
    APP_LOG("创建咖啡选择页面");

    // 获取屏幕大小
    uint32_t screen_width = system_info()->lcd_w;
    uint32_t screen_height = system_info()->lcd_h;

    // 创建主窗口
    widget_t* win = window_create(NULL, 0, 0, screen_width, screen_height);
    widget_set_name(win, "coffee_selection_window");

    // 设置深色背景
    widget_set_style_color(win, STYLE_ID_BG_COLOR, 0xFF2C2C2C);

    // 顶部导航栏
    // 返回按钮
    widget_t* back_btn = button_create(win, 20, 20, 40, 40);
    widget_set_text_utf8(back_btn, "←");
    widget_set_name(back_btn, WIDGET_NAME_BACK_BTN);
    widget_set_style_color(back_btn, STYLE_ID_FG_COLOR, 0xFFFFFFFF);
    widget_set_style_color(back_btn, STYLE_ID_BG_COLOR, 0x00000000);
    widget_set_style_int(back_btn, STYLE_ID_FONT_SIZE, 24);
    widget_on(back_btn, EVT_CLICK, on_back_button_click, NULL);

    // 顶部绿色标识
    widget_t* top_indicator = view_create(win, screen_width/2 - 25, 20, 50, 25);
    widget_set_style_color(top_indicator, STYLE_ID_BG_COLOR, 0xFF4CAF50);
    widget_set_style_int(top_indicator, STYLE_ID_ROUND_RADIUS, 4);

    // 设置按钮
    widget_t* settings_btn = button_create(win, screen_width - 60, 20, 40, 40);
    widget_set_text_utf8(settings_btn, "⚙");
    widget_set_name(settings_btn, WIDGET_NAME_SETTINGS_BTN);
    widget_set_style_color(settings_btn, STYLE_ID_FG_COLOR, 0xFFFFFFFF);
    widget_set_style_color(settings_btn, STYLE_ID_BG_COLOR, 0x00000000);
    widget_set_style_int(settings_btn, STYLE_ID_FONT_SIZE, 20);
    widget_on(settings_btn, EVT_CLICK, on_settings_button_click, NULL);

    // 页面标题
    widget_t* title = label_create(win, 50, 80, screen_width - 100, 40);
    widget_set_text_utf8(title, "Coffee Selection");
    widget_set_style_color(title, STYLE_ID_FG_COLOR, 0xFFFFFFFF);
    widget_set_style_int(title, STYLE_ID_FONT_SIZE, 24);
    widget_set_style_int(title, STYLE_ID_TEXT_ALIGN_H, ALIGN_H_LEFT);

    // 2x2 咖啡卡片网格
    const coffee_info_t* coffee_info = get_all_coffee_info();
    int card_width = (screen_width - 60) / 2 - 10;
    int card_height = (screen_height - 200) / 2 - 10;

    for (int i = 0; i < COFFEE_TYPE_COUNT; i++) {
        int row = i / 2;
        int col = i % 2;
        int x = 30 + col * (card_width + 20);
        int y = 140 + row * (card_height + 20);

        // 咖啡卡片容器
        widget_t* card = button_create(win, x, y, card_width, card_height);
        widget_set_name(card, WIDGET_NAME_COFFEE_CARD);
        widget_set_style_color(card, STYLE_ID_BG_COLOR, 0xFF3C3C3C);
        widget_set_style_int(card, STYLE_ID_ROUND_RADIUS, 12);
        widget_set_prop_int(card, "coffee_type", i);  // 存储咖啡类型
        widget_on(card, EVT_CLICK, on_coffee_card_click, NULL);

        // 咖啡图标
        widget_t* icon = label_create(card, card_width/2 - 30, 20, 60, 60);
        widget_set_text_utf8(icon, "☕");
        widget_set_style_color(icon, STYLE_ID_FG_COLOR, 0xFFD4AF37);
        widget_set_style_int(icon, STYLE_ID_FONT_SIZE, 32);
        widget_set_style_int(icon, STYLE_ID_TEXT_ALIGN_H, ALIGN_H_CENTER);

        // 咖啡名称
        widget_t* name = label_create(card, 10, card_height - 80, card_width - 20, 25);
        widget_set_text_utf8(name, coffee_info[i].name);
        widget_set_style_color(name, STYLE_ID_FG_COLOR, 0xFFFFFFFF);
        widget_set_style_int(name, STYLE_ID_FONT_SIZE, 16);
        widget_set_style_int(name, STYLE_ID_TEXT_ALIGN_H, ALIGN_H_CENTER);

        // 配料信息
        widget_t* ingredients = label_create(card, 10, card_height - 55, card_width - 20, 20);
        widget_set_text_utf8(ingredients, coffee_info[i].ingredients);
        widget_set_style_color(ingredients, STYLE_ID_FG_COLOR, 0xFFCCCCCC);
        widget_set_style_int(ingredients, STYLE_ID_FONT_SIZE, 12);
        widget_set_style_int(ingredients, STYLE_ID_TEXT_ALIGN_H, ALIGN_H_CENTER);

        // 制作时间
        char time_text[32];
        snprintf(time_text, sizeof(time_text), "%d Mins", coffee_info[i].brew_time_mins);
        widget_t* time_label = label_create(card, 10, card_height - 30, card_width - 60, 20);
        widget_set_text_utf8(time_label, time_text);
        widget_set_style_color(time_label, STYLE_ID_FG_COLOR, 0xFFCCCCCC);
        widget_set_style_int(time_label, STYLE_ID_FONT_SIZE, 12);

        // 时间指示器
        widget_t* time_indicator = view_create(card, card_width - 40, card_height - 35, 30, 20);
        widget_set_style_color(time_indicator, STYLE_ID_BG_COLOR, 0xFF4CAF50);
        widget_set_style_int(time_indicator, STYLE_ID_ROUND_RADIUS, 10);

        widget_t* time_num = label_create(time_indicator, 0, 0, 30, 20);
        snprintf(time_text, sizeof(time_text), "%d", coffee_info[i].brew_time_mins);
        widget_set_text_utf8(time_num, time_text);
        widget_set_style_color(time_num, STYLE_ID_FG_COLOR, 0xFFFFFFFF);
        widget_set_style_int(time_num, STYLE_ID_FONT_SIZE, 10);
        widget_set_style_int(time_num, STYLE_ID_TEXT_ALIGN_H, ALIGN_H_CENTER);
        widget_set_style_int(time_num, STYLE_ID_TEXT_ALIGN_V, ALIGN_V_MIDDLE);
    }

    APP_LOG("咖啡选择页面创建完成 - 窗口大小: %dx%d", screen_width, screen_height);
    return win;
}

app_result_t init_ui_events(widget_t* widget) {
    if (widget == NULL) {
        return APP_ERROR_UI_LOAD_FAILED;
    }

    APP_LOG("UI事件绑定完成");
    return APP_OK;
}

// 电灯按钮点击事件处理
static ret_t on_light_button_click(void* ctx, event_t* e) {
    (void)ctx;
    (void)e;
    
    APP_LOG("电灯按钮被点击!");
    
    // 获取当前电灯状态并切换
    device_control_t control;
    if (get_device_control_status(&control) == APP_OK) {
        device_status_t new_status = (control.light_status == DEVICE_STATUS_ON) ? 
                                     DEVICE_STATUS_OFF : DEVICE_STATUS_ON;
        
        if (control_light(new_status) == APP_OK) {
            APP_LOG("电灯状态切换为: %s", 
                    new_status == DEVICE_STATUS_ON ? "开启" : "关闭");
        } else {
            APP_LOG("电灯控制失败");
        }
    }
    
    return RET_OK;
}

// 窗帘按钮点击事件处理
static ret_t on_curtain_button_click(void* ctx, event_t* e) {
    (void)ctx;
    (void)e;
    
    APP_LOG("窗帘按钮被点击!");
    
    // 获取当前窗帘状态并切换
    device_control_t control;
    if (get_device_control_status(&control) == APP_OK) {
        device_status_t new_status = (control.curtain_status == DEVICE_STATUS_ON) ? 
                                     DEVICE_STATUS_OFF : DEVICE_STATUS_ON;
        
        if (control_curtain(new_status) == APP_OK) {
            APP_LOG("窗帘状态切换为: %s", 
                    new_status == DEVICE_STATUS_ON ? "打开" : "关闭");
        } else {
            APP_LOG("窗帘控制失败");
        }
    }
    
    return RET_OK;
}

// 空调按钮点击事件处理
static ret_t on_ac_button_click(void* ctx, event_t* e) {
    (void)ctx;
    (void)e;
    
    APP_LOG("空调按钮被点击!");
    
    // 获取当前空调状态并切换
    device_control_t control;
    if (get_device_control_status(&control) == APP_OK) {
        device_status_t new_status = (control.ac_status == DEVICE_STATUS_ON) ? 
                                     DEVICE_STATUS_OFF : DEVICE_STATUS_ON;
        
        if (control_ac(new_status) == APP_OK) {
            APP_LOG("空调状态切换为: %s", 
                    new_status == DEVICE_STATUS_ON ? "开启" : "关闭");
            
            // 如果开启空调，设置默认温度
            if (new_status == DEVICE_STATUS_ON) {
                set_ac_temperature(26);
                APP_LOG("空调温度设置为26°C");
            }
        } else {
            APP_LOG("空调控制失败");
        }
    }
    
    return RET_OK;
}

// 传感器数据更新定时器回调
static ret_t on_sensor_data_timer(const timer_info_t* timer) {
    widget_t* widget = (widget_t*)timer->ctx;
    if (widget == NULL) {
        return RET_REMOVE;  // 移除定时器
    }
    
    // 获取传感器数据
    sensor_data_t sensor_data;
    if (get_sensor_data(&sensor_data) != APP_OK) {
        APP_LOG("获取传感器数据失败");
        return RET_REPEAT;  // 继续定时器
    }
    
    // 更新UI显示
    char buffer[64];
    
    // 更新温度显示
    widget_t* temp_label = widget_lookup(widget, WIDGET_NAME_TEMP_LABEL, TRUE);
    if (temp_label != NULL) {
        snprintf(buffer, sizeof(buffer), "温度: %.1f°C", sensor_data.temperature);
        widget_set_text_utf8(temp_label, buffer);
    }
    
    // 更新湿度显示
    widget_t* humidity_label = widget_lookup(widget, WIDGET_NAME_HUMIDITY_LABEL, TRUE);
    if (humidity_label != NULL) {
        snprintf(buffer, sizeof(buffer), "湿度: %.1f%%", sensor_data.humidity);
        widget_set_text_utf8(humidity_label, buffer);
    }
    
    // 更新空气质量显示
    widget_t* air_quality_label = widget_lookup(widget, WIDGET_NAME_AIR_QUALITY_LABEL, TRUE);
    if (air_quality_label != NULL) {
        const char* quality_text = "未知";
        if (sensor_data.air_quality <= 50) {
            quality_text = "优秀";
        } else if (sensor_data.air_quality <= 100) {
            quality_text = "良好";
        } else if (sensor_data.air_quality <= 150) {
            quality_text = "中等";
        } else {
            quality_text = "差";
        }
        snprintf(buffer, sizeof(buffer), "空气质量: %s", quality_text);
        widget_set_text_utf8(air_quality_label, buffer);
    }
    
    return RET_REPEAT;  // 继续定时器
} 