#include "awtk.h"
#include "base/theme_default.h"
#include "ui_loader.h"
#include "device_manager.h"

// 简单的主题数据结构
static const uint8_t simple_theme_data[] = {
    // theme_header_t
    0x54, 0x4B, 0x54, 0x48,  // magic: "TKTH" 
    0x01, 0x00, 0x00, 0x00,  // version: 1
    0x00, 0x00, 0x00, 0x00,  // nr: 0 (no styles)
};

/**
 * 初始化应用程序
 */
ret_t application_init(void) {
    APP_LOG("智能家居应用开始初始化");
    
    // 初始化主题系统 - 创建一个基本主题
    theme_t* default_theme = theme_default_create(simple_theme_data);
    if (default_theme != NULL) {
        theme_set(default_theme);
        APP_LOG("基本主题系统初始化成功");
    } else {
        APP_LOG("警告: 主题系统初始化失败，将使用控件自定义样式");
    }
    
    // 初始化设备管理器
    if (device_manager_init() != APP_OK) {
        APP_LOG("设备管理器初始化失败");
        return RET_FAIL;
    }
    
    // 加载主界面
    widget_t* main_window = app_ui_loader();
    if (main_window == NULL) {
        APP_LOG("主界面加载失败");
        device_manager_cleanup();
        return RET_FAIL;
    }
    
    APP_LOG("智能家居应用初始化完成");
    return RET_OK;
}

/**
 * 退出应用程序
 */
ret_t application_exit(void) {
    APP_LOG("智能家居应用退出");

    // 清理设备管理器
    device_manager_cleanup();

    APP_LOG("应用程序清理完成");
    return RET_OK;
}

/**
 * 应用程序暂停回调（Android平台使用）
 */
ret_t app_pause(void) {
    APP_LOG("应用程序暂停");
    // 这里可以添加暂停时的清理逻辑
    return RET_OK;
}

/**
 * 应用程序恢复回调（Android平台使用）
 */
ret_t app_resume(void) {
    APP_LOG("应用程序恢复");
    // 这里可以添加恢复时的初始化逻辑
    return RET_OK;
}

// 包含AWTK标准主函数
#include "awtk_main.inc" 