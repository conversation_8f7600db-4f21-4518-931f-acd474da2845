#include "awtk.h"
#include "base/theme_default.h"
#include "ui_loader.h"
#include "device_manager.h"

// 创建最基本的主题数据
static const uint8_t basic_theme_data[] = {
    0x00, 0x00, 0x00, 0x00  // 最小的主题数据
};

// 简单的主题数据结构
static const uint8_t simple_theme_data[] = {
    // theme_header_t
    0x54, 0x4B, 0x54, 0x48,  // magic: "TKTH" 
    0x01, 0x00, 0x00, 0x00,  // version: 1
    0x00, 0x00, 0x00, 0x00,  // nr: 0 (no styles)
};

/**
 * 初始化应用程序
 */
ret_t application_init(void) {
    APP_LOG("咖啡机应用开始初始化");

    // 尝试创建空主题
    APP_LOG("尝试创建空主题");

    theme_t* theme = theme_default_create(NULL);
    if (theme != NULL) {
        theme_set(theme);
        APP_LOG("空主题设置成功");
    } else {
        APP_LOG("主题创建失败，继续不使用主题");
    }

    // 初始化咖啡机管理器
    if (coffee_machine_init() != APP_OK) {
        APP_LOG("咖啡机管理器初始化失败");
        return RET_FAIL;
    }

    // 加载主界面
    widget_t* main_window = app_ui_loader();
    if (main_window == NULL) {
        APP_LOG("主界面加载失败");
        coffee_machine_cleanup();
        return RET_FAIL;
    }

    APP_LOG("咖啡机应用初始化完成");
    return RET_OK;
}

/**
 * 退出应用程序
 */
ret_t application_exit(void) {
    APP_LOG("咖啡机应用退出");

    // 清理咖啡机管理器
    coffee_machine_cleanup();

    APP_LOG("应用程序清理完成");
    return RET_OK;
}

/**
 * 应用程序暂停回调（Android平台使用）
 */
ret_t app_pause(void) {
    APP_LOG("应用程序暂停");
    // 这里可以添加暂停时的清理逻辑
    return RET_OK;
}

/**
 * 应用程序恢复回调（Android/iOS平台使用）
 */
ret_t app_resume(void) {
    APP_LOG("应用程序恢复");
    // 这里可以添加恢复时的初始化逻辑
    return RET_OK;
}

/**
 * 应用程序进入后台回调（iOS平台使用）
 */
ret_t app_enter_background(void) {
    APP_LOG("应用程序进入后台");
    // 这里可以添加进入后台时的处理逻辑
    return RET_OK;
}

/**
 * 应用程序进入前台回调（iOS平台使用）
 */
ret_t app_enter_foreground(void) {
    APP_LOG("应用程序进入前台");
    // 这里可以添加进入前台时的处理逻辑
    return RET_OK;
}

/**
 * iOS应用程序主函数入口
 */
#ifdef IOS_PLATFORM
extern "C" int ios_app_main(int argc, char* argv[]) {
    APP_LOG("iOS应用程序主函数启动");

    // 初始化AWTK
    if (!tk_init(0, 0, APP_SIMULATOR, NULL, NULL)) {
        APP_LOG("AWTK初始化失败");
        return -1;
    }

    // 初始化应用程序
    if (application_init() != RET_OK) {
        APP_LOG("应用程序初始化失败");
        tk_exit();
        return -1;
    }

    APP_LOG("iOS应用程序初始化完成");
    return 0;
}
#endif

// 包含AWTK标准主函数
#include "awtk_main.inc" 