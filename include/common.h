#ifndef COFFEE_MACHINE_COMMON_H
#define COFFEE_MACHINE_COMMON_H

#include "awtk.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#ifdef __cplusplus
extern "C" {
#endif

// 应用程序版本信息
#define APP_VERSION_MAJOR 1
#define APP_VERSION_MINOR 0
#define APP_VERSION_PATCH 0
#define APP_NAME "CoffeeMachineApp"

// 屏幕配置
#define SCREEN_WIDTH_MIN 800
#define SCREEN_HEIGHT_MIN 480
#define ASPECT_RATIO_16_9 (16.0f / 9.0f)
#define ASPECT_RATIO_TOLERANCE 0.1f

// UI控件名称定义
#define WIDGET_NAME_MAIN_WIN "main_window"
#define WIDGET_NAME_TEMP_LABEL "temperature_label"
#define WIDGET_NAME_HUMIDITY_LABEL "humidity_label"
#define WIDGET_NAME_AIR_QUALITY_LABEL "air_quality_label"
#define WIDGET_NAME_LIGHT_BTN "light_button"
#define WIDGET_NAME_CURTAIN_BTN "curtain_button"
#define WIDGET_NAME_AC_BTN "ac_button"

// 错误码定义
typedef enum {
    APP_OK = 0,
    APP_ERROR_INIT_FAILED = -1,
    APP_ERROR_UI_LOAD_FAILED = -2,
    APP_ERROR_DEVICE_ERROR = -3
} app_result_t;

// 日志宏定义
#ifdef DEBUG
    #define APP_LOG(fmt, ...) printf("[CoffeeMachine] " fmt "\n", ##__VA_ARGS__)
#else
    #define APP_LOG(fmt, ...)
#endif

// 应用程序生命周期函数声明
ret_t application_init(void);
ret_t application_exit(void);
ret_t app_pause(void);
ret_t app_resume(void);
ret_t app_enter_background(void);
ret_t app_enter_foreground(void);

// iOS平台特定函数
#ifdef IOS_PLATFORM
int ios_app_main(int argc, char* argv[]);
#endif

#ifdef __cplusplus
}
#endif

#endif /* COFFEE_MACHINE_COMMON_H */