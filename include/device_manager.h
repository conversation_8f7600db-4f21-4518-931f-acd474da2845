#ifndef COFFEE_MACHINE_MANAGER_H
#define COFFEE_MACHINE_MANAGER_H

#include "common.h"

#ifdef __cplusplus
extern "C" {
#endif

// 咖啡类型枚举
typedef enum {
    COFFEE_TYPE_CAPPUCCINO = 0,
    COFFEE_TYPE_ESPRESSO = 1,
    COFFEE_TYPE_LATTE = 2,
    COFFEE_TYPE_MACCHIATO = 3,
    COFFEE_TYPE_COUNT = 4
} coffee_type_t;

// 咖啡机状态枚举
typedef enum {
    MACHINE_STATUS_IDLE = 0,
    MACHINE_STATUS_BREWING = 1,
    MACHINE_STATUS_READY = 2,
    MACHINE_STATUS_ERROR = -1
} machine_status_t;

// 咖啡配方结构
typedef struct {
    int coffee_ml;          // 咖啡量 (毫升)
    int milk_ml;            // 牛奶量 (毫升)
    int foam_ml;            // 奶泡量 (毫升)
    int sugar_g;            // 糖量 (克)
} coffee_recipe_t;

// 咖啡信息结构
typedef struct {
    coffee_type_t type;     // 咖啡类型
    const char* name;       // 咖啡名称
    const char* ingredients; // 配料描述
    int brew_time_mins;     // 制作时间(分钟)
    coffee_recipe_t default_recipe; // 默认配方
} coffee_info_t;

// 咖啡机状态结构
typedef struct {
    machine_status_t status;        // 机器状态
    coffee_type_t current_coffee;   // 当前选择的咖啡
    coffee_recipe_t current_recipe; // 当前配方
    int brew_progress;              // 制作进度 (0-100)
    uint32_t last_update;           // 最后更新时间戳
} machine_state_t;

/**
 * @brief 初始化咖啡机管理器
 * @return APP_OK表示成功，其他值表示失败
 */
app_result_t coffee_machine_init(void);

/**
 * @brief 清理咖啡机管理器资源
 */
void coffee_machine_cleanup(void);

/**
 * @brief 获取咖啡信息
 * @param type 咖啡类型
 * @return 咖啡信息指针，失败返回NULL
 */
const coffee_info_t* get_coffee_info(coffee_type_t type);

/**
 * @brief 获取所有咖啡信息
 * @return 咖啡信息数组指针
 */
const coffee_info_t* get_all_coffee_info(void);

/**
 * @brief 获取咖啡机当前状态
 * @param state 输出参数，机器状态结构指针
 * @return APP_OK表示成功，其他值表示失败
 */
app_result_t get_machine_state(machine_state_t* state);

/**
 * @brief 选择咖啡类型
 * @param type 咖啡类型
 * @return APP_OK表示成功，其他值表示失败
 */
app_result_t select_coffee(coffee_type_t type);

/**
 * @brief 设置咖啡配方
 * @param recipe 咖啡配方指针
 * @return APP_OK表示成功，其他值表示失败
 */
app_result_t set_coffee_recipe(const coffee_recipe_t* recipe);

/**
 * @brief 开始制作咖啡
 * @return APP_OK表示成功，其他值表示失败
 */
app_result_t start_brewing(void);

/**
 * @brief 停止制作咖啡
 * @return APP_OK表示成功，其他值表示失败
 */
app_result_t stop_brewing(void);

/**
 * @brief 获取制作进度
 * @return 制作进度 (0-100)
 */
int get_brew_progress(void);

#ifdef __cplusplus
}
#endif

#endif /* COFFEE_MACHINE_MANAGER_H */