#ifndef SMART_HOME_DEVICE_MANAGER_H
#define SMART_HOME_DEVICE_MANAGER_H

#include "common.h"

#ifdef __cplusplus
extern "C" {
#endif

// 设备状态枚举
typedef enum {
    DEVICE_STATUS_OFF = 0,
    DEVICE_STATUS_ON = 1,
    DEVICE_STATUS_UNKNOWN = -1
} device_status_t;

// 传感器数据结构
typedef struct {
    float temperature;      // 温度 (摄氏度)
    float humidity;         // 湿度 (百分比)
    int air_quality;        // 空气质量指数 (0-500)
    uint32_t last_update;   // 最后更新时间戳
} sensor_data_t;

// 设备控制结构
typedef struct {
    device_status_t light_status;       // 电灯状态
    device_status_t curtain_status;     // 窗帘状态
    device_status_t ac_status;          // 空调状态
    int ac_temperature;                 // 空调温度设置
} device_control_t;

/**
 * @brief 初始化设备管理器
 * @return APP_OK表示成功，其他值表示失败
 */
app_result_t device_manager_init(void);

/**
 * @brief 清理设备管理器资源
 */
void device_manager_cleanup(void);

/**
 * @brief 获取当前温度
 * @return 温度值（摄氏度）
 */
float get_temperature(void);

/**
 * @brief 获取当前湿度
 * @return 湿度值（百分比）
 */
float get_humidity(void);

/**
 * @brief 获取当前空气质量指数
 * @return 空气质量指数
 */
int get_air_quality(void);

/**
 * @brief 获取完整的传感器数据
 * @param data 输出参数，传感器数据结构指针
 * @return APP_OK表示成功，其他值表示失败
 */
app_result_t get_sensor_data(sensor_data_t* data);

/**
 * @brief 控制电灯开关
 * @param status 电灯状态
 * @return APP_OK表示成功，其他值表示失败
 */
app_result_t control_light(device_status_t status);

/**
 * @brief 控制窗帘开关
 * @param status 窗帘状态
 * @return APP_OK表示成功，其他值表示失败
 */
app_result_t control_curtain(device_status_t status);

/**
 * @brief 控制空调开关
 * @param status 空调状态
 * @return APP_OK表示成功，其他值表示失败
 */
app_result_t control_ac(device_status_t status);

/**
 * @brief 设置空调温度
 * @param temperature 目标温度
 * @return APP_OK表示成功，其他值表示失败
 */
app_result_t set_ac_temperature(int temperature);

/**
 * @brief 获取设备控制状态
 * @param control 输出参数，设备控制结构指针
 * @return APP_OK表示成功，其他值表示失败
 */
app_result_t get_device_control_status(device_control_t* control);

#ifdef __cplusplus
}
#endif

#endif /* SMART_HOME_DEVICE_MANAGER_H */ 