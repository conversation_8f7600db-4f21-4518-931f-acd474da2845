#ifndef UI_LOADER_H
#define UI_LOADER_H

#include "common.h"

/**
 * @brief 16:9 宽高比
 */
#define ASPECT_RATIO_16_9 (16.0f / 9.0f)

/**
 * @brief 宽高比容差值
 */
#define ASPECT_RATIO_TOLERANCE 0.1f

/**
 * 检测是否为16:9横屏比例
 * @param width 屏幕宽度
 * @param height 屏幕高度
 * @return 如果是16:9横屏比例返回TRUE，否则返回FALSE
 */
bool_t is_landscape_16_9(uint32_t width, uint32_t height);

/**
 * 应用程序UI加载器主函数
 * 根据屏幕分辨率自动选择合适的UI布局
 * @return 主窗口widget指针，失败返回NULL
 */
widget_t* app_ui_loader(void);

/**
 * 创建竖屏UI布局
 * @return UI窗口widget指针，失败返回NULL
 */
widget_t* create_portrait_ui(void);

/**
 * 创建16:9横屏UI布局
 * @return UI窗口widget指针，失败返回NULL
 */
widget_t* create_landscape_16_9_ui(void);

/**
 * 初始化UI事件绑定
 * @param widget 主窗口widget
 * @return 成功返回APP_OK，失败返回相应错误码
 */
app_result_t init_ui_events(widget_t* widget);

#endif // UI_LOADER_H 