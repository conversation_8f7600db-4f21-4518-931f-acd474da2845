#ifndef COFFEE_UI_LOADER_H
#define COFFEE_UI_LOADER_H

#include "common.h"
#include "device_manager.h"

/**
 * @brief 16:9 宽高比
 */
#define ASPECT_RATIO_16_9 (16.0f / 9.0f)

/**
 * @brief 宽高比容差值
 */
#define ASPECT_RATIO_TOLERANCE 0.1f

// 页面类型枚举
typedef enum {
    PAGE_WELCOME = 0,
    PAGE_COFFEE_SELECTION = 1,
    PAGE_COFFEE_BREWING = 2
} page_type_t;

// UI控件名称定义
#define WIDGET_NAME_WELCOME_BTN "get_started_button"
#define WIDGET_NAME_BACK_BTN "back_button"
#define WIDGET_NAME_SETTINGS_BTN "settings_button"
#define WIDGET_NAME_COFFEE_CARD "coffee_card"
#define WIDGET_NAME_START_BTN "start_button"
#define WIDGET_NAME_COFFEE_SLIDER "coffee_slider"
#define WIDGET_NAME_MILK_SLIDER "milk_slider"
#define WIDGET_NAME_FOAM_SLIDER "foam_slider"
#define WIDGET_NAME_SUGAR_SLIDER "sugar_slider"

/**
 * 检测是否为16:9横屏比例
 * @param width 屏幕宽度
 * @param height 屏幕高度
 * @return 如果是16:9横屏比例返回TRUE，否则返回FALSE
 */
bool_t is_landscape_16_9(uint32_t width, uint32_t height);

/**
 * 应用程序UI加载器主函数
 * 根据屏幕分辨率自动选择合适的UI布局
 * @return 主窗口widget指针，失败返回NULL
 */
widget_t* app_ui_loader(void);

/**
 * 创建欢迎页面
 * @return UI窗口widget指针，失败返回NULL
 */
widget_t* create_welcome_page(void);

/**
 * 创建咖啡选择页面
 * @return UI窗口widget指针，失败返回NULL
 */
widget_t* create_coffee_selection_page(void);

/**
 * 创建咖啡制作页面
 * @param coffee_type 咖啡类型
 * @return UI窗口widget指针，失败返回NULL
 */
widget_t* create_coffee_brewing_page(coffee_type_t coffee_type);

/**
 * 页面导航函数
 * @param page_type 目标页面类型
 * @param param 页面参数（如咖啡类型）
 * @return 成功返回APP_OK，失败返回相应错误码
 */
app_result_t navigate_to_page(page_type_t page_type, int param);

/**
 * 初始化UI事件绑定
 * @param widget 主窗口widget
 * @return 成功返回APP_OK，失败返回相应错误码
 */
app_result_t init_ui_events(widget_t* widget);

#endif // COFFEE_UI_LOADER_H