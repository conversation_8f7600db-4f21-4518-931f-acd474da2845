Format: http://www.debian.org/doc/packaging-manuals/copyright-format/1.0/
Upstream-Name: SDL
Upstream-Contact: <PERSON> <<EMAIL>>
Source: http://www.libsdl.org/

Files: *
Copyright: 1997-2018 <PERSON> <<EMAIL>>
License: zlib/libpng

Files: src/libm/*
Copyright: 1993 by Sun Microsystems, Inc. All rights reserved.
License: SunPro

Files: src/main/windows/SDL_windows_main.c
Copyright: 2018 Sam Lantinga
License: PublicDomain_Sam_Lantinga
Comment: SDL_main.c, placed in the public domain by <PERSON>  4/13/98

Files: src/render/mmx.h
Copyright: 1997-99 by <PERSON><PERSON> and R. Fisher
License: zlib/libpng
Comment:
  Copyright but no mention to license.
  .
  Included since long ago with SDL (and its Debian package) under directory
  src/video/mmx.h

Files: src/render/SDL_yuv_sw.c
Copyright: 1995 <PERSON>
           1995 The Regents of the University of California
           1995 Brown University
License: BrownUn_UnCalifornia_ErikCorry

Files: src/test/SDL_test_md5.c
Copyright: 1997-2018 <PERSON> <<EMAIL>>
           1990 RSA Data Security, Inc.
License: zlib/libpng and RSA_Data_Security

Files: src/thread/windows/win_ce_semaphore.c
Copyright: 1998, Johnson M. Hart (with corrections 2001 by Rainer Loritz)
License: Johnson_M._Hart

Files: src/video/x11/imKStoUCS.c
       src/video/x11/imKStoUCS.h
Copyright: 1994-2003 The XFree86 Project, Inc.
License: MIT/X11

Files: test/testhaptic.c
Copyright: 1997-2018 Sam Lantinga <<EMAIL>>
           2008 Edgar Simo Serra
License: BSD_3_clause

Files: test/testrumble.c
Copyright: 1997-2018 Sam Lantinga <<EMAIL>>
           2011 Edgar Simo Serra
License: BSD_3_clause

Files: test/shapes/*
Copyright: none
License: zlib/libpng
Comment: No specific information about the images

Files: Xcode/TemplatesForXcode*/*/main.c
Copyright: none
License: zlib/libpng
Comment: SDL files, no copyright or license notice

Files: Xcode/TemplatesForXcode*/*/atlantis/*
Copyright: 1993, 1994, Silicon Graphics, Inc.
License: SGI_atlantis

Files: Xcode/TemplatesForXcode*/*/atlantis/atlantis.c
Copyright: 1994 Mark J. Kilgard
           1993, 1994, Silicon Graphics, Inc.
License: SGI_atlantis
Comment: See license for full copyright notice, this one is the same except for
 the additional copyright holder

Files: Xcode/SDLTest/sdlcommon_prefix.h
Copyright: 2003 Darrell Walisser
License: zlib/libpng
Comment:
 Created by Darrell Walisser on Wed Aug 06 2003.
 Copyright (c) 2003 __MyCompanyName__. All rights reserved.

Files: debian/*
Copyright: 2011-2012, Manuel A. Fernandez Montecelo <<EMAIL>>
           2011-2012, Felix Geyer <<EMAIL>>
           2011, Roman Vasiyarov <<EMAIL>>
           2010, Jon Dowland <<EMAIL>>
           2009, Barry deFreese <<EMAIL>>
           2007-2008, Aurelien Jarno <<EMAIL>>
           2007-2008, Sam Hocevar (Debian packages) <<EMAIL>>
           2002-2007, Josselin Mouette <<EMAIL>>
           2001, Christian T. Steigies <<EMAIL>>
           2001, Branden Robinson <<EMAIL>>
License: LGPL-2.1+


License: zlib/libpng
 This software is provided 'as-is', without any express or implied
 warranty.  In no event will the authors be held liable for any damages
 arising from the use of this software.
 .
 Permission is granted to anyone to use this software for any purpose,
 including commercial applications, and to alter it and redistribute it
 freely, subject to the following restrictions:
 .
 1. The origin of this software must not be misrepresented; you must not
    claim that you wrote the original software. If you use this software
    in a product, an acknowledgment in the product documentation would be
    appreciated but is not required.
 2. Altered source versions must be plainly marked as such, and must not be
    misrepresented as being the original software.
 3. This notice may not be removed or altered from any source distribution.

License: LGPL-2.1+
 This program is free software; you can redistribute it and/or modify
 it under the terms of the GNU Lesser General Public License as published by
 the Free Software Foundation; either version 2.1 of the License, or (at
 your option) any later version.
 .
 On Debian systems, the complete text of version 2.1 of the GNU Lesser
 Public License can be found in '/usr/share/common-licenses/LGPL-2.1'.

License: MIT/X11
 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and associated documentation files (the "Software"), to deal
 in the Software without restriction, including without limitation the rights
 to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 copies of the Software, and to permit persons to whom the Software is fur-
 nished to do so, subject to the following conditions:
 .
 The above copyright notice and this permission notice shall be included in
 all copies or substantial portions of the Software.
 .
 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FIT-
 NESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL THE
 XFREE86 PROJECT BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
 IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CON-
 NECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 .
 Except as contained in this notice, the name of the XFree86 Project shall not
 be used in advertising or otherwise to promote the sale, use or other deal-
 ings in this Software without prior written authorization from the XFree86
 Project.

License: BSD_3_clause
 Redistribution and use in source and binary forms, with or without
 modification, are permitted provided that the following conditions are met:
     * Redistributions of source code must retain the above copyright
       notice, this list of conditions and the following disclaimer.
     * Redistributions in binary form must reproduce the above copyright
       notice, this list of conditions and the following disclaimer in the
       documentation and/or other materials provided with the distribution.
     * Neither the name of the LibQxt project nor the
       names of its contributors may be used to endorse or promote products
       derived from this software without specific prior written permission.
 .
 THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY
 DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
Comment:
  Copyright (C) 1997-2018 Sam Lantinga <<EMAIL>>
  .
  This software is provided 'as-is', without any express or implied
  warranty.  In no event will the authors be held liable for any damages
  arising from the use of this software.
  .
  Permission is granted to anyone to use this software for any purpose,
  including commercial applications, and to alter it and redistribute it
  freely.
  .
  .
  Copyright (c) 2011, Edgar Simo Serra
  All rights reserved.
  .
  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions are met:
  .
    * Redistributions of source code must retain the above copyright notice,
      this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above copyright notice,
      this list of conditions and the following disclaimer in the documentation
      and/or other materials provided with the distribution.
    * Neither the name of the Simple Directmedia Layer (SDL) nor the names of
      its contributors may be used to endorse or promote products derived from
      this software without specific prior written permission.
  .
  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
  DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE
  FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
  DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
  SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
  CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
  OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

License: BrownUn_UnCalifornia_ErikCorry
 /* This code was derived from code carrying the following copyright notices:
  * Copyright (c) 1995 The Regents of the University of California.
  * All rights reserved.
  *
  * Permission to use, copy, modify, and distribute this software and its
  * documentation for any purpose, without fee, and without written agreement is
  * hereby granted, provided that the above copyright notice and the following
  * two paragraphs appear in all copies of this software.
  *
  * IN NO EVENT SHALL THE UNIVERSITY OF CALIFORNIA BE LIABLE TO ANY PARTY FOR
  * DIRECT, INDIRECT, SPECIAL, INCIDENTAL, OR CONSEQUENTIAL DAMAGES ARISING OUT
  * OF THE USE OF THIS SOFTWARE AND ITS DOCUMENTATION, EVEN IF THE UNIVERSITY OF
  * CALIFORNIA HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  *
  * THE UNIVERSITY OF CALIFORNIA SPECIFICALLY DISCLAIMS ANY WARRANTIES,
  * INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY
  * AND FITNESS FOR A PARTICULAR PURPOSE.  THE SOFTWARE PROVIDED HEREUNDER IS
  * ON AN "AS IS" BASIS, AND THE UNIVERSITY OF CALIFORNIA HAS NO OBLIGATION TO
  * PROVIDE MAINTENANCE, SUPPORT, UPDATES, ENHANCEMENTS, OR MODIFICATIONS.
  *
  * Copyright (c) 1995 Erik Corry
  * All rights reserved.
  *
  * Permission to use, copy, modify, and distribute this software and its
  * documentation for any purpose, without fee, and without written agreement is
  * hereby granted, provided that the above copyright notice and the following
  * two paragraphs appear in all copies of this software.
  *
  * IN NO EVENT SHALL ERIK CORRY BE LIABLE TO ANY PARTY FOR DIRECT, INDIRECT,
  * SPECIAL, INCIDENTAL, OR CONSEQUENTIAL DAMAGES ARISING OUT OF THE USE OF
  * THIS SOFTWARE AND ITS DOCUMENTATION, EVEN IF ERIK CORRY HAS BEEN ADVISED
  * OF THE POSSIBILITY OF SUCH DAMAGE.
  *
  * ERIK CORRY SPECIFICALLY DISCLAIMS ANY WARRANTIES, INCLUDING, BUT NOT
  * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
  * PARTICULAR PURPOSE.  THE SOFTWARE PROVIDED HEREUNDER IS ON AN "AS IS"
  * BASIS, AND ERIK CORRY HAS NO OBLIGATION TO PROVIDE MAINTENANCE, SUPPORT,
  * UPDATES, ENHANCEMENTS, OR MODIFICATIONS.
  *
  * Portions of this software Copyright (c) 1995 Brown University.
  * All rights reserved.
  *
  * Permission to use, copy, modify, and distribute this software and its
  * documentation for any purpose, without fee, and without written agreement
  * is hereby granted, provided that the above copyright notice and the
  * following two paragraphs appear in all copies of this software.
  *
  * IN NO EVENT SHALL BROWN UNIVERSITY BE LIABLE TO ANY PARTY FOR
  * DIRECT, INDIRECT, SPECIAL, INCIDENTAL, OR CONSEQUENTIAL DAMAGES ARISING OUT
  * OF THE USE OF THIS SOFTWARE AND ITS DOCUMENTATION, EVEN IF BROWN
  * UNIVERSITY HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  *
  * BROWN UNIVERSITY SPECIFICALLY DISCLAIMS ANY WARRANTIES, INCLUDING, BUT NOT
  * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
  * PARTICULAR PURPOSE.  THE SOFTWARE PROVIDED HEREUNDER IS ON AN "AS IS"
  * BASIS, AND BROWN UNIVERSITY HAS NO OBLIGATION TO PROVIDE MAINTENANCE,
  * SUPPORT, UPDATES, ENHANCEMENTS, OR MODIFICATIONS.
  */

License: Johnson_M._Hart
  Permission is granted for any and all use providing that this
  copyright is properly acknowledged.
  There are no assurances of suitability for any use whatsoever.

License: SunPro
  Developed at SunPro, a Sun Microsystems, Inc. business.
  Permission to use, copy, modify, and distribute this
  software is freely granted, provided that this notice
  is preserved.

License: PublicDomain_Sam_Lantinga
 Placed in the public domain by Sam Lantinga  4/13/98

License: PublicDomain_Edgar_Simo
 Written by Edgar Simo "bobbens"
 .
 Released under Public Domain.

License: RSA_Data_Security
 ***********************************************************************
 ** utl_md5.c -- the source code for MD5 routines                      **
 ** RSA Data Security, Inc. MD5 Message-Digest Algorithm              **
 ** Created: 2/17/90 RLR                                              **
 ** Revised: 1/91 SRD,AJ,BSK,JT Reference C ver., 7/10 constant corr. **
 ***********************************************************************
 ***********************************************************************
 ** Copyright (C) 1990, RSA Data Security, Inc. All rights reserved.  **
 **                                                                   **
 ** License to copy and use this software is granted provided that    **
 ** it is identified as the "RSA Data Security, Inc. MD5 Message-     **
 ** Digest Algorithm" in all material mentioning or referencing this  **
 ** software or this function.                                        **
 **                                                                   **
 ** License is also granted to make and use derivative works          **
 ** provided that such works are identified as "derived from the RSA  **
 ** Data Security, Inc. MD5 Message-Digest Algorithm" in all          **
 ** material mentioning or referencing the derived work.              **
 **                                                                   **
 ** RSA Data Security, Inc. makes no representations concerning       **
 ** either the merchantability of this software or the suitability    **
 ** of this software for any particular purpose.  It is provided "as  **
 ** is" without express or implied warranty of any kind.              **
 **                                                                   **
 ** These notices must be retained in any copies of any part of this  **
 ** documentation and/or software.                                    **
 ***********************************************************************

License: SGI_atlantis
 (c) Copyright 1993, 1994, Silicon Graphics, Inc.
 ALL RIGHTS RESERVED
 Permission to use, copy, modify, and distribute this software for
 any purpose and without fee is hereby granted, provided that the above
 copyright notice appear in all copies and that both the copyright notice
 and this permission notice appear in supporting documentation, and that
 the name of Silicon Graphics, Inc. not be used in advertising
 or publicity pertaining to distribution of the software without specific,
 written prior permission.
 .
 THE MATERIAL EMBODIED ON THIS SOFTWARE IS PROVIDED TO YOU "AS-IS"
 AND WITHOUT WARRANTY OF ANY KIND, EXPRESS, IMPLIED OR OTHERWISE,
 INCLUDING WITHOUT LIMITATION, ANY WARRANTY OF MERCHANTABILITY OR
 FITNESS FOR A PARTICULAR PURPOSE.  IN NO EVENT SHALL SILICON
 GRAPHICS, INC.  BE LIABLE TO YOU OR ANYONE ELSE FOR ANY DIRECT,
 SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY
 KIND, OR ANY DAMAGES WHATSOEVER, INCLUDING WITHOUT LIMITATION,
 LOSS OF PROFIT, LOSS OF USE, SAVINGS OR REVENUE, OR THE CLAIMS OF
 THIRD PARTIES, WHETHER OR NOT SILICON GRAPHICS, INC.  HAS BEEN
 ADVISED OF THE POSSIBILITY OF SUCH LOSS, HOWEVER CAUSED AND ON
 ANY THEORY OF LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE
 POSSESSION, USE OR PERFORMANCE OF THIS SOFTWARE.
 .
 US Government Users Restricted Rights
 Use, duplication, or disclosure by the Government is subject to
 restrictions set forth in FAR 52.227.19(c)(2) or subparagraph
 (c)(1)(ii) of the Rights in Technical Data and Computer Software
 clause at DFARS 252.227-7013 and/or in similar or successor
 clauses in the FAR or the DOD or NASA FAR Supplement.
 Unpublished-- rights reserved under the copyright laws of the
 United States.  Contractor/manufacturer is Silicon Graphics,
 Inc., 2011 N.  Shoreline Blvd., Mountain View, CA 94039-7311.
 .
 OpenGL(TM) is a trademark of Silicon Graphics, Inc.
