libsdl2 (2.0.9) UNRELEASED; urgency=low

  * Updated SDL to version 2.0.9

 -- <PERSON> <<EMAIL>>  Wed, 26 Sep 2018 10:02:21 -0800

libsdl2 (2.0.8) UNRELEASED; urgency=low

  * Updated SDL to version 2.0.8

 -- <PERSON> <<EMAIL>>  Sat, 4 Nov 2017 21:21:53 -0800

libsdl2 (2.0.7) UNRELEASED; urgency=low

  * Updated SDL to version 2.0.7

 -- <PERSON> <<EMAIL>>  Thu, 12 Oct 2017 08:01:16 -0800

libsdl2 (2.0.6) UNRELEASED; urgency=low

  * Updated SDL to version 2.0.6

 -- <PERSON> <<EMAIL>>  Sat, 9 Sep 2017 07:29:36 -0800

libsdl2 (2.0.5) UNRELEASED; urgency=low

  * Updated SDL to version 2.0.5

 -- <PERSON> <<EMAIL>>  Mon, 28 Nov 2016 07:32:52 -0800

libsdl2 (2.0.4) UNRELEASED; urgency=low

  * Updated SDL to version 2.0.4

 -- <PERSON> <<EMAIL>>  Thu, 07 Jan 2016 11:02:39 -0800

libsdl2 (2.0.3) UNRELEASED; urgency=low

  * Updated SDL to version 2.0.3

 -- Sam Lantinga <<EMAIL>>  Sun, 9 Mar 2014 10:35:54 -0800

libsdl2 (2.0.2) UNRELEASED; urgency=low

  * Updated SDL to version 2.0.2

 -- Sam Lantinga <<EMAIL>>  Sun, 9 Feb 2014 2:55:59 -0800

libsdl2 (2.0.1) UNRELEASED; urgency=low

  * Updated SDL to version 2.0.1

 -- Sam Lantinga <<EMAIL>>  Wed, 23 Oct 2013 16:31:38 -0800

libsdl2 (2.0.0) UNRELEASED; urgency=low

  * SDL is now a native debian package.
  * Removed udeb package
  * Disabled DirectFB support by default, to avoid runtime dependencies

 -- Sam Lantinga <<EMAIL>>  Fri, 15 Feb 2013 08:55:04 -0800

libsdl2 (2.0.0~20130127-1) UNRELEASED; urgency=low

  * New upstream snapshot.

 -- Sam Hocevar <<EMAIL>>  Wed, 30 Jan 2013 23:01:12 +0100

libsdl2 (2.0.0~20130103-1) unstable; urgency=low

  [ Manuel A. Fernandez Montecelo ]
  * debian/control:
    - Updating maintainers/permissions:
      - Add myself and Felix Geyer
    - Update Build-Depends:
      - Remove a few obsolete items
      - Add items added lately to libsdl1.2, such as libts (touch screen)
        support
    - Add "libsdl2-dbg", analog to libsdl1.2-dbg
    - Change "XC-Package-Type: udeb" to "Package-Type"
  * debian/compat: set level 9
  * debian/source/format: Set to "3.0 (quilt)"
    - Remove README.source, not needed with new format
  * debian/sdl2-config.1: Fix typo, "progams"->"programs"
  * debian/libsdl2-dev.install:
    - Remove "usr/lib/*/*.la", discouraged
    - Add man pages: "usr/share/man/man3/*"
  * debian/libsdl2-dev.manpages: add file to install local "sdl2-config.1"
  * debian/sources: Removed, possible obsolete file from long ago?
  * debian/copyright:
    - Upstream updated to zlib/libpng
    - Copyright-file format conversion to 1.0
    - Complete revamp and detailed research about copyright and licenses used,
      it's very messy but hopefully complete

  [ Felix Geyer ]
  * Simplify debian/rules by using dh(1).

 -- Manuel A. Fernandez Montecelo <<EMAIL>>  Sun, 27 Jan 2013 16:40:49 +0100

libsdl2 (2.0~20120220c-1) experimental; urgency=low

  * Upstream version was renamed to 2.0 (Closes: #669367).
  * New upstream snapshot (Closes: #671506).
  * This package no longer conflicts with libsdl-1.2.
  * debian/rules: add multiarch support (Closes: #669364).

  * debian/patches/fix_joystick_misc_axes.diff: fix a joystick remapping
    bug causing some axes to malfunction.
  * debian/patches/external_header_paths.diff: provide additional CFLAGS
    so that headers such as SDL_syswm.h can be included (Closes: #669363).

 -- Sam Hocevar <<EMAIL>>  Thu, 17 May 2012 19:03:59 +0200

libsdl-1.3 (1.3.0~20111204-1) experimental; urgency=low

  * Initial upload from upstream snapshot.

 -- Sam Hocevar <<EMAIL>>  Sun, 04 Dec 2011 14:35:05 +0100

