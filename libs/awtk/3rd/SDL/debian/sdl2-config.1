.TH sdl2-config 1 "2012-02-20" "SDL 2.0"
.SH NAME
sdl2-config \- script to get information about the installed version of SDL
.SH SYNOPSIS
.B sdl2-config
[ --prefix=
.IR DIR ]
[ --exec-prefix=
.IR DIR ]
[ --version ] [ --libs | --library-libs | --plugin-libs ] [ --cflags ]
.SH DESCRIPTION
.B sdl2-config
is a tool that is used to configure and determine the compiler and linker
flags that should be used to compile and link programs, libraries, and
plugins that use SDL.  It is also used internally by the m4 macros that are
included with SDL.
.SH OPTIONS
.TP
.B --cflags
Print the compiler flags that are necessary to compile a program or library
that uses SDL.
.TP
.BI --exec-prefix= DIR
If specified, use
.I DIR
instead of the installation exec prefix that <PERSON>L was build with when
computing the output for the --cflags, --libs, --library-libs, and
--plugin-libs options.  This option must be specified before any of the
--cflags, --libs, --library-libs, and --plugin-libs options.
.TP
.B --libs
Print the linker flags that are necessary to link a program that uses SDL.
.TP
.B --static-libs
Print the linker flags that are necessary to statically link a program that uses SDL.
.TP
.B --library-libs
Print the linker flags that are necessary to link a library that uses SDL.
(This excludes any static objects required for linking, since these must be
linked only by executable programs.)
.TP
.B --plugin-libs
Print the linker flags that are necessary to link an SDL-using object that
is to be accessed via
.IR dlopen (3).
This may include static objects with PIC symbol information.  This option
should
.B not
be used for ordinary shared libraries or standalone applications.
.TP
.BI --prefix= DIR
If specified, use PREFIX instead of the installation prefix that SDL was
built with when computing the output for the --cflags, --libs,
--library-libs, and --plugin-libs options.  This option is also used for
the exec prefix if --exec-prefix was not specified.  This option must be
specified before any of the --cflags, --libs, --library-libs, and
--plugin-libs options.
.TP
.B --version
Prints the currently installed version of SDL on standard output.
.SH EXAMPLES
.TP
gcc -o main.o $(sdl2-config --cflags) main.c
is how you might use
.B sdl2-config
to compile a C source file for an executable program.
.TP
gcc -o my_app $(sdl2-config --libs) main.o util.o
is how you might use
.B sdl2-config
to link compiled objects into an executable program.
.TP
gcc -o libSDL_nifty-2.0.so.0.0.1 $(sdl --library-libs) read.o write.o munge.o
is how you might use
.B sdl2-config
to link compiled objects into a shared library.
.TP
gcc -o libnifty_xmms.so $(sdl --plugin-libs) stream.o blit.o deinterlace.o
is how you might use
.B sdl2-config
to link compiled objects into a plugin for use by another program.
.SH AUTHOR
The Simple DirectMedia Layer (SDL) library was written by Sam Lantinga.
.PP
This manual page was written by Branden Robinson, originally for Progeny
Linux Systems, Inc., and the Debian Project.
