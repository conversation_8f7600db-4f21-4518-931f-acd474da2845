/*
  Simple DirectMedia Layer
  Copyright (C) 1997-2018 <PERSON> <<EMAIL>>

  This software is provided 'as-is', without any express or implied
  warranty.  In no event will the authors be held liable for any damages
  arising from the use of this software.

  Permission is granted to anyone to use this software for any purpose,
  including commercial applications, and to alter it and redistribute it
  freely, subject to the following restrictions:

  1. The origin of this software must not be misrepresented; you must not
     claim that you wrote the original software. If you use this software
     in a product, an acknowledgment in the product documentation would be
     appreciated but is not required.
  2. Altered source versions must be plainly marked as such, and must not be
     misrepresented as being the original software.
  3. This notice may not be removed or altered from any source distribution.
*/
#include "../../SDL_internal.h"

#if SDL_VIDEO_RENDER_D3D11 && !SDL_RENDER_DISABLED

#include "SDL_stdinc.h"

#define COBJMACROS
#include "../../core/windows/SDL_windows.h"
#include <d3d11_1.h>

#include "SDL_shaders_d3d11.h"

#define SDL_COMPOSE_ERROR(str) SDL_STRINGIFY_ARG(__FUNCTION__) ", " str


/* Direct3D 11.x shaders

   SDL's shaders are compiled into SDL itself, to simplify distribution.

   All Direct3D 11.x shaders were compiled with the following:

   fxc /E"main" /T "<TYPE>" /Fo"<OUTPUT FILE>" "<INPUT FILE>"

     Variables:
     - <TYPE>: the type of shader.  A table of utilized shader types is
       listed below.
     - <OUTPUT FILE>: where to store compiled output
     - <INPUT FILE>: where to read shader source code from

     Shader types:
     - ps_4_0_level_9_1: Pixel shader for Windows 8+, including Windows RT
     - vs_4_0_level_9_1: Vertex shader for Windows 8+, including Windows RT
     - ps_4_0_level_9_3: Pixel shader for Windows Phone 8
     - vs_4_0_level_9_3: Vertex shader for Windows Phone 8
   

   Shader object code was converted to a list of DWORDs via the following
   *nix style command (available separately from Windows + MSVC):

     hexdump -v -e '6/4 "0x%08.8x, " "\n"' <FILE>
  */
#if WINAPI_FAMILY == WINAPI_FAMILY_PHONE_APP
#define D3D11_USE_SHADER_MODEL_4_0_level_9_3
#else
#define D3D11_USE_SHADER_MODEL_4_0_level_9_1
#endif

/* The color-only-rendering pixel shader:

   --- D3D11_PixelShader_Colors.hlsl ---
   struct PixelShaderInput
   {
       float4 pos : SV_POSITION;
       float2 tex : TEXCOORD0;
       float4 color : COLOR0;
   };

   float4 main(PixelShaderInput input) : SV_TARGET
   {
       return input.color;
   }
*/
#if defined(D3D11_USE_SHADER_MODEL_4_0_level_9_1)
static const DWORD D3D11_PixelShader_Colors[] = {
    0x43425844, 0xd74c28fe, 0xa1eb8804, 0x269d512a, 0x7699723d, 0x00000001,
    0x00000240, 0x00000006, 0x00000038, 0x00000084, 0x000000c4, 0x00000140,
    0x00000198, 0x0000020c, 0x396e6f41, 0x00000044, 0x00000044, 0xffff0200,
    0x00000020, 0x00000024, 0x00240000, 0x00240000, 0x00240000, 0x00240000,
    0x00240000, 0xffff0200, 0x0200001f, 0x80000000, 0xb00f0001, 0x02000001,
    0x800f0800, 0xb0e40001, 0x0000ffff, 0x52444853, 0x00000038, 0x00000040,
    0x0000000e, 0x03001062, 0x001010f2, 0x00000002, 0x03000065, 0x001020f2,
    0x00000000, 0x05000036, 0x001020f2, 0x00000000, 0x00101e46, 0x00000002,
    0x0100003e, 0x54415453, 0x00000074, 0x00000002, 0x00000000, 0x00000000,
    0x00000002, 0x00000000, 0x00000000, 0x00000000, 0x00000001, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000002, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x46454452, 0x00000050, 0x00000000, 0x00000000,
    0x00000000, 0x0000001c, 0xffff0400, 0x00000100, 0x0000001c, 0x7263694d,
    0x666f736f, 0x52282074, 0x4c482029, 0x53204c53, 0x65646168, 0x6f432072,
    0x6c69706d, 0x39207265, 0x2e30332e, 0x30303239, 0x3336312e, 0xab003438,
    0x4e475349, 0x0000006c, 0x00000003, 0x00000008, 0x00000050, 0x00000000,
    0x00000001, 0x00000003, 0x00000000, 0x0000000f, 0x0000005c, 0x00000000,
    0x00000000, 0x00000003, 0x00000001, 0x00000003, 0x00000065, 0x00000000,
    0x00000000, 0x00000003, 0x00000002, 0x00000f0f, 0x505f5653, 0x5449534f,
    0x004e4f49, 0x43584554, 0x44524f4f, 0x4c4f4300, 0xab00524f, 0x4e47534f,
    0x0000002c, 0x00000001, 0x00000008, 0x00000020, 0x00000000, 0x00000000,
    0x00000003, 0x00000000, 0x0000000f, 0x545f5653, 0x45475241, 0xabab0054
};
#elif defined(D3D11_USE_SHADER_MODEL_4_0_level_9_3)
static const DWORD D3D11_PixelShader_Colors[] = {
    0x43425844, 0x93f6ccfc, 0x5f919270, 0x7a11aa4f, 0x9148e931, 0x00000001,
    0x00000240, 0x00000006, 0x00000038, 0x00000084, 0x000000c4, 0x00000140,
    0x00000198, 0x0000020c, 0x396e6f41, 0x00000044, 0x00000044, 0xffff0200,
    0x00000020, 0x00000024, 0x00240000, 0x00240000, 0x00240000, 0x00240000,
    0x00240000, 0xffff0201, 0x0200001f, 0x80000000, 0xb00f0001, 0x02000001,
    0x800f0800, 0xb0e40001, 0x0000ffff, 0x52444853, 0x00000038, 0x00000040,
    0x0000000e, 0x03001062, 0x001010f2, 0x00000002, 0x03000065, 0x001020f2,
    0x00000000, 0x05000036, 0x001020f2, 0x00000000, 0x00101e46, 0x00000002,
    0x0100003e, 0x54415453, 0x00000074, 0x00000002, 0x00000000, 0x00000000,
    0x00000002, 0x00000000, 0x00000000, 0x00000000, 0x00000001, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000002, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x46454452, 0x00000050, 0x00000000, 0x00000000,
    0x00000000, 0x0000001c, 0xffff0400, 0x00000100, 0x0000001c, 0x7263694d,
    0x666f736f, 0x52282074, 0x4c482029, 0x53204c53, 0x65646168, 0x6f432072,
    0x6c69706d, 0x39207265, 0x2e30332e, 0x30303239, 0x3336312e, 0xab003438,
    0x4e475349, 0x0000006c, 0x00000003, 0x00000008, 0x00000050, 0x00000000,
    0x00000001, 0x00000003, 0x00000000, 0x0000000f, 0x0000005c, 0x00000000,
    0x00000000, 0x00000003, 0x00000001, 0x00000003, 0x00000065, 0x00000000,
    0x00000000, 0x00000003, 0x00000002, 0x00000f0f, 0x505f5653, 0x5449534f,
    0x004e4f49, 0x43584554, 0x44524f4f, 0x4c4f4300, 0xab00524f, 0x4e47534f,
    0x0000002c, 0x00000001, 0x00000008, 0x00000020, 0x00000000, 0x00000000,
    0x00000003, 0x00000000, 0x0000000f, 0x545f5653, 0x45475241, 0xabab0054
};
#else
#error "An appropriate 'colors' pixel shader is not defined."
#endif

/* The texture-rendering pixel shader:

    --- D3D11_PixelShader_Textures.hlsl ---
    Texture2D theTexture : register(t0);
    SamplerState theSampler : register(s0);

    struct PixelShaderInput
    {
        float4 pos : SV_POSITION;
        float2 tex : TEXCOORD0;
        float4 color : COLOR0;
    };

    float4 main(PixelShaderInput input) : SV_TARGET
    {
        return theTexture.Sample(theSampler, input.tex) * input.color;
    }
*/
#if defined(D3D11_USE_SHADER_MODEL_4_0_level_9_1)
static const DWORD D3D11_PixelShader_Textures[] = {
    0x43425844, 0x6299b59f, 0x155258f2, 0x873ab86a, 0xfcbb6dcd, 0x00000001,
    0x00000330, 0x00000006, 0x00000038, 0x000000c0, 0x0000015c, 0x000001d8,
    0x00000288, 0x000002fc, 0x396e6f41, 0x00000080, 0x00000080, 0xffff0200,
    0x00000058, 0x00000028, 0x00280000, 0x00280000, 0x00280000, 0x00240001,
    0x00280000, 0x00000000, 0xffff0200, 0x0200001f, 0x80000000, 0xb0030000,
    0x0200001f, 0x80000000, 0xb00f0001, 0x0200001f, 0x90000000, 0xa00f0800,
    0x03000042, 0x800f0000, 0xb0e40000, 0xa0e40800, 0x03000005, 0x800f0000,
    0x80e40000, 0xb0e40001, 0x02000001, 0x800f0800, 0x80e40000, 0x0000ffff,
    0x52444853, 0x00000094, 0x00000040, 0x00000025, 0x0300005a, 0x00106000,
    0x00000000, 0x04001858, 0x00107000, 0x00000000, 0x00005555, 0x03001062,
    0x00101032, 0x00000001, 0x03001062, 0x001010f2, 0x00000002, 0x03000065,
    0x001020f2, 0x00000000, 0x02000068, 0x00000001, 0x09000045, 0x001000f2,
    0x00000000, 0x00101046, 0x00000001, 0x00107e46, 0x00000000, 0x00106000,
    0x00000000, 0x07000038, 0x001020f2, 0x00000000, 0x00100e46, 0x00000000,
    0x00101e46, 0x00000002, 0x0100003e, 0x54415453, 0x00000074, 0x00000003,
    0x00000001, 0x00000000, 0x00000003, 0x00000001, 0x00000000, 0x00000000,
    0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x46454452, 0x000000a8,
    0x00000000, 0x00000000, 0x00000002, 0x0000001c, 0xffff0400, 0x00000100,
    0x00000072, 0x0000005c, 0x00000003, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000001, 0x00000001, 0x00000067, 0x00000002, 0x00000005,
    0x00000004, 0xffffffff, 0x00000000, 0x00000001, 0x0000000d, 0x53656874,
    0x6c706d61, 0x74007265, 0x65546568, 0x72757478, 0x694d0065, 0x736f7263,
    0x2074666f, 0x20295228, 0x4c534c48, 0x61685320, 0x20726564, 0x706d6f43,
    0x72656c69, 0x332e3920, 0x32392e30, 0x312e3030, 0x34383336, 0xababab00,
    0x4e475349, 0x0000006c, 0x00000003, 0x00000008, 0x00000050, 0x00000000,
    0x00000001, 0x00000003, 0x00000000, 0x0000000f, 0x0000005c, 0x00000000,
    0x00000000, 0x00000003, 0x00000001, 0x00000303, 0x00000065, 0x00000000,
    0x00000000, 0x00000003, 0x00000002, 0x00000f0f, 0x505f5653, 0x5449534f,
    0x004e4f49, 0x43584554, 0x44524f4f, 0x4c4f4300, 0xab00524f, 0x4e47534f,
    0x0000002c, 0x00000001, 0x00000008, 0x00000020, 0x00000000, 0x00000000,
    0x00000003, 0x00000000, 0x0000000f, 0x545f5653, 0x45475241, 0xabab0054
};
#elif defined(D3D11_USE_SHADER_MODEL_4_0_level_9_3)
static const DWORD D3D11_PixelShader_Textures[] = {
    0x43425844, 0x5876569a, 0x01b6c87e, 0x8447454f, 0xc7f3ef10, 0x00000001,
    0x00000330, 0x00000006, 0x00000038, 0x000000c0, 0x0000015c, 0x000001d8,
    0x00000288, 0x000002fc, 0x396e6f41, 0x00000080, 0x00000080, 0xffff0200,
    0x00000058, 0x00000028, 0x00280000, 0x00280000, 0x00280000, 0x00240001,
    0x00280000, 0x00000000, 0xffff0201, 0x0200001f, 0x80000000, 0xb0030000,
    0x0200001f, 0x80000000, 0xb00f0001, 0x0200001f, 0x90000000, 0xa00f0800,
    0x03000042, 0x800f0000, 0xb0e40000, 0xa0e40800, 0x03000005, 0x800f0000,
    0x80e40000, 0xb0e40001, 0x02000001, 0x800f0800, 0x80e40000, 0x0000ffff,
    0x52444853, 0x00000094, 0x00000040, 0x00000025, 0x0300005a, 0x00106000,
    0x00000000, 0x04001858, 0x00107000, 0x00000000, 0x00005555, 0x03001062,
    0x00101032, 0x00000001, 0x03001062, 0x001010f2, 0x00000002, 0x03000065,
    0x001020f2, 0x00000000, 0x02000068, 0x00000001, 0x09000045, 0x001000f2,
    0x00000000, 0x00101046, 0x00000001, 0x00107e46, 0x00000000, 0x00106000,
    0x00000000, 0x07000038, 0x001020f2, 0x00000000, 0x00100e46, 0x00000000,
    0x00101e46, 0x00000002, 0x0100003e, 0x54415453, 0x00000074, 0x00000003,
    0x00000001, 0x00000000, 0x00000003, 0x00000001, 0x00000000, 0x00000000,
    0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x46454452, 0x000000a8,
    0x00000000, 0x00000000, 0x00000002, 0x0000001c, 0xffff0400, 0x00000100,
    0x00000072, 0x0000005c, 0x00000003, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000001, 0x00000001, 0x00000067, 0x00000002, 0x00000005,
    0x00000004, 0xffffffff, 0x00000000, 0x00000001, 0x0000000d, 0x53656874,
    0x6c706d61, 0x74007265, 0x65546568, 0x72757478, 0x694d0065, 0x736f7263,
    0x2074666f, 0x20295228, 0x4c534c48, 0x61685320, 0x20726564, 0x706d6f43,
    0x72656c69, 0x332e3920, 0x32392e30, 0x312e3030, 0x34383336, 0xababab00,
    0x4e475349, 0x0000006c, 0x00000003, 0x00000008, 0x00000050, 0x00000000,
    0x00000001, 0x00000003, 0x00000000, 0x0000000f, 0x0000005c, 0x00000000,
    0x00000000, 0x00000003, 0x00000001, 0x00000303, 0x00000065, 0x00000000,
    0x00000000, 0x00000003, 0x00000002, 0x00000f0f, 0x505f5653, 0x5449534f,
    0x004e4f49, 0x43584554, 0x44524f4f, 0x4c4f4300, 0xab00524f, 0x4e47534f,
    0x0000002c, 0x00000001, 0x00000008, 0x00000020, 0x00000000, 0x00000000,
    0x00000003, 0x00000000, 0x0000000f, 0x545f5653, 0x45475241, 0xabab0054
};
#else
#error "An appropriate 'textures' pixel shader is not defined"
#endif

/* The yuv-rendering pixel shader:

    --- D3D11_PixelShader_YUV_JPEG.hlsl ---
    Texture2D theTextureY : register(t0);
    Texture2D theTextureU : register(t1);
    Texture2D theTextureV : register(t2);
    SamplerState theSampler : register(s0);

    struct PixelShaderInput
    {
        float4 pos : SV_POSITION;
        float2 tex : TEXCOORD0;
        float4 color : COLOR0;
    };

    float4 main(PixelShaderInput input) : SV_TARGET
    {
        const float3 offset = {0.0, -0.501960814, -0.501960814};
        const float3 Rcoeff = {1.0000,  0.0000,  1.4020};
        const float3 Gcoeff = {1.0000, -0.3441, -0.7141};
        const float3 Bcoeff = {1.0000,  1.7720,  0.0000};

        float4 Output;

        float3 yuv;
        yuv.x = theTextureY.Sample(theSampler, input.tex).r;
        yuv.y = theTextureU.Sample(theSampler, input.tex).r;
        yuv.z = theTextureV.Sample(theSampler, input.tex).r;

        yuv += offset;
        Output.r = dot(yuv, Rcoeff);
        Output.g = dot(yuv, Gcoeff);
        Output.b = dot(yuv, Bcoeff);
        Output.a = 1.0f;

        return Output * input.color;
    }

*/
#if defined(D3D11_USE_SHADER_MODEL_4_0_level_9_1)
static const DWORD D3D11_PixelShader_YUV_JPEG[] = {
    0x43425844, 0x10359e9c, 0x92c3d2c4, 0x00bf0cd5, 0x5ce8c499, 0x00000001,
    0x000005e8, 0x00000006, 0x00000038, 0x000001dc, 0x000003bc, 0x00000438,
    0x00000540, 0x000005b4, 0x396e6f41, 0x0000019c, 0x0000019c, 0xffff0200,
    0x0000016c, 0x00000030, 0x00300000, 0x00300000, 0x00300000, 0x00240003,
    0x00300000, 0x00000000, 0x00010001, 0x00020002, 0xffff0200, 0x05000051,
    0xa00f0000, 0x00000000, 0xbf008081, 0xbf008081, 0x3f800000, 0x05000051,
    0xa00f0001, 0x3f800000, 0x3fb374bc, 0x00000000, 0x00000000, 0x05000051,
    0xa00f0002, 0x3f800000, 0xbeb02de0, 0xbf36cf42, 0x00000000, 0x05000051,
    0xa00f0003, 0x3f800000, 0x3fe2d0e5, 0x00000000, 0x00000000, 0x0200001f,
    0x80000000, 0xb0030000, 0x0200001f, 0x80000000, 0xb00f0001, 0x0200001f,
    0x90000000, 0xa00f0800, 0x0200001f, 0x90000000, 0xa00f0801, 0x0200001f,
    0x90000000, 0xa00f0802, 0x03000042, 0x800f0000, 0xb0e40000, 0xa0e40800,
    0x03000042, 0x800f0001, 0xb0e40000, 0xa0e40801, 0x03000042, 0x800f0002,
    0xb0e40000, 0xa0e40802, 0x02000001, 0x80020000, 0x80000001, 0x02000001,
    0x80040000, 0x80000002, 0x03000002, 0x80070000, 0x80e40000, 0xa0e40000,
    0x03000005, 0x80080000, 0x80000000, 0xa0000001, 0x04000004, 0x80010001,
    0x80aa0000, 0xa0550001, 0x80ff0000, 0x03000008, 0x80020001, 0x80e40000,
    0xa0e40002, 0x0400005a, 0x80040001, 0x80e40000, 0xa0e40003, 0xa0aa0003,
    0x02000001, 0x80080001, 0xa0ff0000, 0x03000005, 0x800f0000, 0x80e40001,
    0xb0e40001, 0x02000001, 0x800f0800, 0x80e40000, 0x0000ffff, 0x52444853,
    0x000001d8, 0x00000040, 0x00000076, 0x0300005a, 0x00106000, 0x00000000,
    0x04001858, 0x00107000, 0x00000000, 0x00005555, 0x04001858, 0x00107000,
    0x00000001, 0x00005555, 0x04001858, 0x00107000, 0x00000002, 0x00005555,
    0x03001062, 0x00101032, 0x00000001, 0x03001062, 0x001010f2, 0x00000002,
    0x03000065, 0x001020f2, 0x00000000, 0x02000068, 0x00000002, 0x09000045,
    0x001000f2, 0x00000000, 0x00101046, 0x00000001, 0x00107e46, 0x00000000,
    0x00106000, 0x00000000, 0x09000045, 0x001000f2, 0x00000001, 0x00101046,
    0x00000001, 0x00107e46, 0x00000001, 0x00106000, 0x00000000, 0x05000036,
    0x00100022, 0x00000000, 0x0010000a, 0x00000001, 0x09000045, 0x001000f2,
    0x00000001, 0x00101046, 0x00000001, 0x00107e46, 0x00000002, 0x00106000,
    0x00000000, 0x05000036, 0x00100042, 0x00000000, 0x0010000a, 0x00000001,
    0x0a000000, 0x00100072, 0x00000000, 0x00100246, 0x00000000, 0x00004002,
    0x00000000, 0xbf008081, 0xbf008081, 0x00000000, 0x0a00000f, 0x00100012,
    0x00000001, 0x00100086, 0x00000000, 0x00004002, 0x3f800000, 0x3fb374bc,
    0x00000000, 0x00000000, 0x0a000010, 0x00100022, 0x00000001, 0x00100246,
    0x00000000, 0x00004002, 0x3f800000, 0xbeb02de0, 0xbf36cf42, 0x00000000,
    0x0a00000f, 0x00100042, 0x00000001, 0x00100046, 0x00000000, 0x00004002,
    0x3f800000, 0x3fe2d0e5, 0x00000000, 0x00000000, 0x05000036, 0x00100082,
    0x00000001, 0x00004001, 0x3f800000, 0x07000038, 0x001020f2, 0x00000000,
    0x00100e46, 0x00000001, 0x00101e46, 0x00000002, 0x0100003e, 0x54415453,
    0x00000074, 0x0000000c, 0x00000002, 0x00000000, 0x00000003, 0x00000005,
    0x00000000, 0x00000000, 0x00000001, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000003, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000003, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x46454452, 0x00000100, 0x00000000, 0x00000000, 0x00000004, 0x0000001c,
    0xffff0400, 0x00000100, 0x000000cb, 0x0000009c, 0x00000003, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000001, 0x00000001, 0x000000a7,
    0x00000002, 0x00000005, 0x00000004, 0xffffffff, 0x00000000, 0x00000001,
    0x0000000d, 0x000000b3, 0x00000002, 0x00000005, 0x00000004, 0xffffffff,
    0x00000001, 0x00000001, 0x0000000d, 0x000000bf, 0x00000002, 0x00000005,
    0x00000004, 0xffffffff, 0x00000002, 0x00000001, 0x0000000d, 0x53656874,
    0x6c706d61, 0x74007265, 0x65546568, 0x72757478, 0x74005965, 0x65546568,
    0x72757478, 0x74005565, 0x65546568, 0x72757478, 0x4d005665, 0x6f726369,
    0x74666f73, 0x29522820, 0x534c4820, 0x6853204c, 0x72656461, 0x6d6f4320,
    0x656c6970, 0x2e362072, 0x36392e33, 0x312e3030, 0x34383336, 0xababab00,
    0x4e475349, 0x0000006c, 0x00000003, 0x00000008, 0x00000050, 0x00000000,
    0x00000001, 0x00000003, 0x00000000, 0x0000000f, 0x0000005c, 0x00000000,
    0x00000000, 0x00000003, 0x00000001, 0x00000303, 0x00000065, 0x00000000,
    0x00000000, 0x00000003, 0x00000002, 0x00000f0f, 0x505f5653, 0x5449534f,
    0x004e4f49, 0x43584554, 0x44524f4f, 0x4c4f4300, 0xab00524f, 0x4e47534f,
    0x0000002c, 0x00000001, 0x00000008, 0x00000020, 0x00000000, 0x00000000,
    0x00000003, 0x00000000, 0x0000000f, 0x545f5653, 0x45475241, 0xabab0054
};
#elif defined(D3D11_USE_SHADER_MODEL_4_0_level_9_3)
static const DWORD D3D11_PixelShader_YUV_JPEG[] = {
    0x43425844, 0x616d6673, 0x83174178, 0x15aac25d, 0x2a340487, 0x00000001,
    0x000005c0, 0x00000006, 0x00000038, 0x000001b4, 0x00000394, 0x00000410,
    0x00000518, 0x0000058c, 0x396e6f41, 0x00000174, 0x00000174, 0xffff0200,
    0x00000144, 0x00000030, 0x00300000, 0x00300000, 0x00300000, 0x00240003,
    0x00300000, 0x00000000, 0x00010001, 0x00020002, 0xffff0201, 0x05000051,
    0xa00f0000, 0x00000000, 0xbf008081, 0x3f800000, 0x3fb374bc, 0x05000051,
    0xa00f0001, 0x3f800000, 0xbeb02de0, 0xbf36cf42, 0x00000000, 0x05000051,
    0xa00f0002, 0x3f800000, 0x3fe2d0e5, 0x00000000, 0x00000000, 0x0200001f,
    0x80000000, 0xb0030000, 0x0200001f, 0x80000000, 0xb00f0001, 0x0200001f,
    0x90000000, 0xa00f0800, 0x0200001f, 0x90000000, 0xa00f0801, 0x0200001f,
    0x90000000, 0xa00f0802, 0x03000042, 0x800f0000, 0xb0e40000, 0xa0e40801,
    0x03000042, 0x800f0001, 0xb0e40000, 0xa0e40800, 0x02000001, 0x80020001,
    0x80000000, 0x03000042, 0x800f0000, 0xb0e40000, 0xa0e40802, 0x02000001,
    0x80040001, 0x80000000, 0x03000002, 0x80070000, 0x80e40001, 0xa0d40000,
    0x0400005a, 0x80010001, 0x80e80000, 0xa0ee0000, 0xa0000000, 0x03000008,
    0x80020001, 0x80e40000, 0xa0e40001, 0x0400005a, 0x80040001, 0x80e40000,
    0xa0e40002, 0xa0aa0002, 0x02000001, 0x80080001, 0xa0aa0000, 0x03000005,
    0x800f0000, 0x80e40001, 0xb0e40001, 0x02000001, 0x800f0800, 0x80e40000,
    0x0000ffff, 0x52444853, 0x000001d8, 0x00000040, 0x00000076, 0x0300005a,
    0x00106000, 0x00000000, 0x04001858, 0x00107000, 0x00000000, 0x00005555,
    0x04001858, 0x00107000, 0x00000001, 0x00005555, 0x04001858, 0x00107000,
    0x00000002, 0x00005555, 0x03001062, 0x00101032, 0x00000001, 0x03001062,
    0x001010f2, 0x00000002, 0x03000065, 0x001020f2, 0x00000000, 0x02000068,
    0x00000002, 0x09000045, 0x001000f2, 0x00000000, 0x00101046, 0x00000001,
    0x00107e46, 0x00000000, 0x00106000, 0x00000000, 0x09000045, 0x001000f2,
    0x00000001, 0x00101046, 0x00000001, 0x00107e46, 0x00000001, 0x00106000,
    0x00000000, 0x05000036, 0x00100022, 0x00000000, 0x0010000a, 0x00000001,
    0x09000045, 0x001000f2, 0x00000001, 0x00101046, 0x00000001, 0x00107e46,
    0x00000002, 0x00106000, 0x00000000, 0x05000036, 0x00100042, 0x00000000,
    0x0010000a, 0x00000001, 0x0a000000, 0x00100072, 0x00000000, 0x00100246,
    0x00000000, 0x00004002, 0x00000000, 0xbf008081, 0xbf008081, 0x00000000,
    0x0a00000f, 0x00100012, 0x00000001, 0x00100086, 0x00000000, 0x00004002,
    0x3f800000, 0x3fb374bc, 0x00000000, 0x00000000, 0x0a000010, 0x00100022,
    0x00000001, 0x00100246, 0x00000000, 0x00004002, 0x3f800000, 0xbeb02de0,
    0xbf36cf42, 0x00000000, 0x0a00000f, 0x00100042, 0x00000001, 0x00100046,
    0x00000000, 0x00004002, 0x3f800000, 0x3fe2d0e5, 0x00000000, 0x00000000,
    0x05000036, 0x00100082, 0x00000001, 0x00004001, 0x3f800000, 0x07000038,
    0x001020f2, 0x00000000, 0x00100e46, 0x00000001, 0x00101e46, 0x00000002,
    0x0100003e, 0x54415453, 0x00000074, 0x0000000c, 0x00000002, 0x00000000,
    0x00000003, 0x00000005, 0x00000000, 0x00000000, 0x00000001, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000003,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000003, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x46454452, 0x00000100, 0x00000000, 0x00000000,
    0x00000004, 0x0000001c, 0xffff0400, 0x00000100, 0x000000cb, 0x0000009c,
    0x00000003, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000001,
    0x00000001, 0x000000a7, 0x00000002, 0x00000005, 0x00000004, 0xffffffff,
    0x00000000, 0x00000001, 0x0000000d, 0x000000b3, 0x00000002, 0x00000005,
    0x00000004, 0xffffffff, 0x00000001, 0x00000001, 0x0000000d, 0x000000bf,
    0x00000002, 0x00000005, 0x00000004, 0xffffffff, 0x00000002, 0x00000001,
    0x0000000d, 0x53656874, 0x6c706d61, 0x74007265, 0x65546568, 0x72757478,
    0x74005965, 0x65546568, 0x72757478, 0x74005565, 0x65546568, 0x72757478,
    0x4d005665, 0x6f726369, 0x74666f73, 0x29522820, 0x534c4820, 0x6853204c,
    0x72656461, 0x6d6f4320, 0x656c6970, 0x2e362072, 0x36392e33, 0x312e3030,
    0x34383336, 0xababab00, 0x4e475349, 0x0000006c, 0x00000003, 0x00000008,
    0x00000050, 0x00000000, 0x00000001, 0x00000003, 0x00000000, 0x0000000f,
    0x0000005c, 0x00000000, 0x00000000, 0x00000003, 0x00000001, 0x00000303,
    0x00000065, 0x00000000, 0x00000000, 0x00000003, 0x00000002, 0x00000f0f,
    0x505f5653, 0x5449534f, 0x004e4f49, 0x43584554, 0x44524f4f, 0x4c4f4300,
    0xab00524f, 0x4e47534f, 0x0000002c, 0x00000001, 0x00000008, 0x00000020,
    0x00000000, 0x00000000, 0x00000003, 0x00000000, 0x0000000f, 0x545f5653,
    0x45475241, 0xabab0054
};
#else
#error "An appropriate 'yuv' pixel shader is not defined."
#endif

/* The yuv-rendering pixel shader:

    --- D3D11_PixelShader_YUV_BT601.hlsl ---
    Texture2D theTextureY : register(t0);
    Texture2D theTextureU : register(t1);
    Texture2D theTextureV : register(t2);
    SamplerState theSampler : register(s0);

    struct PixelShaderInput
    {
        float4 pos : SV_POSITION;
        float2 tex : TEXCOORD0;
        float4 color : COLOR0;
    };

    float4 main(PixelShaderInput input) : SV_TARGET
    {
        const float3 offset = {-0.0627451017, -0.501960814, -0.501960814};
        const float3 Rcoeff = {1.1644,  0.0000,  1.5960};
        const float3 Gcoeff = {1.1644, -0.3918, -0.8130};
        const float3 Bcoeff = {1.1644,  2.0172,  0.0000};

        float4 Output;

        float3 yuv;
        yuv.x = theTextureY.Sample(theSampler, input.tex).r;
        yuv.y = theTextureU.Sample(theSampler, input.tex).r;
        yuv.z = theTextureV.Sample(theSampler, input.tex).r;

        yuv += offset;
        Output.r = dot(yuv, Rcoeff);
        Output.g = dot(yuv, Gcoeff);
        Output.b = dot(yuv, Bcoeff);
        Output.a = 1.0f;

        return Output * input.color;
    }

*/
#if defined(D3D11_USE_SHADER_MODEL_4_0_level_9_1)
static const DWORD D3D11_PixelShader_YUV_BT601[] = {
    0x43425844, 0x628ec838, 0xbe9cec6a, 0xc9ee10bb, 0x63283218, 0x00000001,
    0x000005e8, 0x00000006, 0x00000038, 0x000001dc, 0x000003bc, 0x00000438,
    0x00000540, 0x000005b4, 0x396e6f41, 0x0000019c, 0x0000019c, 0xffff0200,
    0x0000016c, 0x00000030, 0x00300000, 0x00300000, 0x00300000, 0x00240003,
    0x00300000, 0x00000000, 0x00010001, 0x00020002, 0xffff0200, 0x05000051,
    0xa00f0000, 0xbd808081, 0xbf008081, 0xbf008081, 0x3f800000, 0x05000051,
    0xa00f0001, 0x3f950b0f, 0x3fcc49ba, 0x00000000, 0x00000000, 0x05000051,
    0xa00f0002, 0x3f950b0f, 0xbec89a02, 0xbf5020c5, 0x00000000, 0x05000051,
    0xa00f0003, 0x3f950b0f, 0x400119ce, 0x00000000, 0x00000000, 0x0200001f,
    0x80000000, 0xb0030000, 0x0200001f, 0x80000000, 0xb00f0001, 0x0200001f,
    0x90000000, 0xa00f0800, 0x0200001f, 0x90000000, 0xa00f0801, 0x0200001f,
    0x90000000, 0xa00f0802, 0x03000042, 0x800f0000, 0xb0e40000, 0xa0e40800,
    0x03000042, 0x800f0001, 0xb0e40000, 0xa0e40801, 0x03000042, 0x800f0002,
    0xb0e40000, 0xa0e40802, 0x02000001, 0x80020000, 0x80000001, 0x02000001,
    0x80040000, 0x80000002, 0x03000002, 0x80070000, 0x80e40000, 0xa0e40000,
    0x03000005, 0x80080000, 0x80000000, 0xa0000001, 0x04000004, 0x80010001,
    0x80aa0000, 0xa0550001, 0x80ff0000, 0x03000008, 0x80020001, 0x80e40000,
    0xa0e40002, 0x0400005a, 0x80040001, 0x80e40000, 0xa0e40003, 0xa0aa0003,
    0x02000001, 0x80080001, 0xa0ff0000, 0x03000005, 0x800f0000, 0x80e40001,
    0xb0e40001, 0x02000001, 0x800f0800, 0x80e40000, 0x0000ffff, 0x52444853,
    0x000001d8, 0x00000040, 0x00000076, 0x0300005a, 0x00106000, 0x00000000,
    0x04001858, 0x00107000, 0x00000000, 0x00005555, 0x04001858, 0x00107000,
    0x00000001, 0x00005555, 0x04001858, 0x00107000, 0x00000002, 0x00005555,
    0x03001062, 0x00101032, 0x00000001, 0x03001062, 0x001010f2, 0x00000002,
    0x03000065, 0x001020f2, 0x00000000, 0x02000068, 0x00000002, 0x09000045,
    0x001000f2, 0x00000000, 0x00101046, 0x00000001, 0x00107e46, 0x00000000,
    0x00106000, 0x00000000, 0x09000045, 0x001000f2, 0x00000001, 0x00101046,
    0x00000001, 0x00107e46, 0x00000001, 0x00106000, 0x00000000, 0x05000036,
    0x00100022, 0x00000000, 0x0010000a, 0x00000001, 0x09000045, 0x001000f2,
    0x00000001, 0x00101046, 0x00000001, 0x00107e46, 0x00000002, 0x00106000,
    0x00000000, 0x05000036, 0x00100042, 0x00000000, 0x0010000a, 0x00000001,
    0x0a000000, 0x00100072, 0x00000000, 0x00100246, 0x00000000, 0x00004002,
    0xbd808081, 0xbf008081, 0xbf008081, 0x00000000, 0x0a00000f, 0x00100012,
    0x00000001, 0x00100086, 0x00000000, 0x00004002, 0x3f950b0f, 0x3fcc49ba,
    0x00000000, 0x00000000, 0x0a000010, 0x00100022, 0x00000001, 0x00100246,
    0x00000000, 0x00004002, 0x3f950b0f, 0xbec89a02, 0xbf5020c5, 0x00000000,
    0x0a00000f, 0x00100042, 0x00000001, 0x00100046, 0x00000000, 0x00004002,
    0x3f950b0f, 0x400119ce, 0x00000000, 0x00000000, 0x05000036, 0x00100082,
    0x00000001, 0x00004001, 0x3f800000, 0x07000038, 0x001020f2, 0x00000000,
    0x00100e46, 0x00000001, 0x00101e46, 0x00000002, 0x0100003e, 0x54415453,
    0x00000074, 0x0000000c, 0x00000002, 0x00000000, 0x00000003, 0x00000005,
    0x00000000, 0x00000000, 0x00000001, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000003, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000003, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x46454452, 0x00000100, 0x00000000, 0x00000000, 0x00000004, 0x0000001c,
    0xffff0400, 0x00000100, 0x000000cb, 0x0000009c, 0x00000003, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000001, 0x00000001, 0x000000a7,
    0x00000002, 0x00000005, 0x00000004, 0xffffffff, 0x00000000, 0x00000001,
    0x0000000d, 0x000000b3, 0x00000002, 0x00000005, 0x00000004, 0xffffffff,
    0x00000001, 0x00000001, 0x0000000d, 0x000000bf, 0x00000002, 0x00000005,
    0x00000004, 0xffffffff, 0x00000002, 0x00000001, 0x0000000d, 0x53656874,
    0x6c706d61, 0x74007265, 0x65546568, 0x72757478, 0x74005965, 0x65546568,
    0x72757478, 0x74005565, 0x65546568, 0x72757478, 0x4d005665, 0x6f726369,
    0x74666f73, 0x29522820, 0x534c4820, 0x6853204c, 0x72656461, 0x6d6f4320,
    0x656c6970, 0x2e362072, 0x36392e33, 0x312e3030, 0x34383336, 0xababab00,
    0x4e475349, 0x0000006c, 0x00000003, 0x00000008, 0x00000050, 0x00000000,
    0x00000001, 0x00000003, 0x00000000, 0x0000000f, 0x0000005c, 0x00000000,
    0x00000000, 0x00000003, 0x00000001, 0x00000303, 0x00000065, 0x00000000,
    0x00000000, 0x00000003, 0x00000002, 0x00000f0f, 0x505f5653, 0x5449534f,
    0x004e4f49, 0x43584554, 0x44524f4f, 0x4c4f4300, 0xab00524f, 0x4e47534f,
    0x0000002c, 0x00000001, 0x00000008, 0x00000020, 0x00000000, 0x00000000,
    0x00000003, 0x00000000, 0x0000000f, 0x545f5653, 0x45475241, 0xabab0054
};
#elif defined(D3D11_USE_SHADER_MODEL_4_0_level_9_3)
static const DWORD D3D11_PixelShader_YUV_BT601[] = {
    0x43425844, 0x692b159b, 0xf58723cc, 0xf4ceac9e, 0x35eec738, 0x00000001,
    0x000005c0, 0x00000006, 0x00000038, 0x000001b4, 0x00000394, 0x00000410,
    0x00000518, 0x0000058c, 0x396e6f41, 0x00000174, 0x00000174, 0xffff0200,
    0x00000144, 0x00000030, 0x00300000, 0x00300000, 0x00300000, 0x00240003,
    0x00300000, 0x00000000, 0x00010001, 0x00020002, 0xffff0201, 0x05000051,
    0xa00f0000, 0xbd808081, 0xbf008081, 0x3f800000, 0x00000000, 0x05000051,
    0xa00f0001, 0x3f950b0f, 0x3fcc49ba, 0x00000000, 0x400119ce, 0x05000051,
    0xa00f0002, 0x3f950b0f, 0xbec89a02, 0xbf5020c5, 0x00000000, 0x0200001f,
    0x80000000, 0xb0030000, 0x0200001f, 0x80000000, 0xb00f0001, 0x0200001f,
    0x90000000, 0xa00f0800, 0x0200001f, 0x90000000, 0xa00f0801, 0x0200001f,
    0x90000000, 0xa00f0802, 0x03000042, 0x800f0000, 0xb0e40000, 0xa0e40801,
    0x03000042, 0x800f0001, 0xb0e40000, 0xa0e40800, 0x02000001, 0x80020001,
    0x80000000, 0x03000042, 0x800f0000, 0xb0e40000, 0xa0e40802, 0x02000001,
    0x80040001, 0x80000000, 0x03000002, 0x80070000, 0x80e40001, 0xa0d40000,
    0x0400005a, 0x80010001, 0x80e80000, 0xa0e40001, 0xa0aa0001, 0x03000008,
    0x80020001, 0x80e40000, 0xa0e40002, 0x0400005a, 0x80040001, 0x80e40000,
    0xa0ec0001, 0xa0aa0001, 0x02000001, 0x80080001, 0xa0aa0000, 0x03000005,
    0x800f0000, 0x80e40001, 0xb0e40001, 0x02000001, 0x800f0800, 0x80e40000,
    0x0000ffff, 0x52444853, 0x000001d8, 0x00000040, 0x00000076, 0x0300005a,
    0x00106000, 0x00000000, 0x04001858, 0x00107000, 0x00000000, 0x00005555,
    0x04001858, 0x00107000, 0x00000001, 0x00005555, 0x04001858, 0x00107000,
    0x00000002, 0x00005555, 0x03001062, 0x00101032, 0x00000001, 0x03001062,
    0x001010f2, 0x00000002, 0x03000065, 0x001020f2, 0x00000000, 0x02000068,
    0x00000002, 0x09000045, 0x001000f2, 0x00000000, 0x00101046, 0x00000001,
    0x00107e46, 0x00000000, 0x00106000, 0x00000000, 0x09000045, 0x001000f2,
    0x00000001, 0x00101046, 0x00000001, 0x00107e46, 0x00000001, 0x00106000,
    0x00000000, 0x05000036, 0x00100022, 0x00000000, 0x0010000a, 0x00000001,
    0x09000045, 0x001000f2, 0x00000001, 0x00101046, 0x00000001, 0x00107e46,
    0x00000002, 0x00106000, 0x00000000, 0x05000036, 0x00100042, 0x00000000,
    0x0010000a, 0x00000001, 0x0a000000, 0x00100072, 0x00000000, 0x00100246,
    0x00000000, 0x00004002, 0xbd808081, 0xbf008081, 0xbf008081, 0x00000000,
    0x0a00000f, 0x00100012, 0x00000001, 0x00100086, 0x00000000, 0x00004002,
    0x3f950b0f, 0x3fcc49ba, 0x00000000, 0x00000000, 0x0a000010, 0x00100022,
    0x00000001, 0x00100246, 0x00000000, 0x00004002, 0x3f950b0f, 0xbec89a02,
    0xbf5020c5, 0x00000000, 0x0a00000f, 0x00100042, 0x00000001, 0x00100046,
    0x00000000, 0x00004002, 0x3f950b0f, 0x400119ce, 0x00000000, 0x00000000,
    0x05000036, 0x00100082, 0x00000001, 0x00004001, 0x3f800000, 0x07000038,
    0x001020f2, 0x00000000, 0x00100e46, 0x00000001, 0x00101e46, 0x00000002,
    0x0100003e, 0x54415453, 0x00000074, 0x0000000c, 0x00000002, 0x00000000,
    0x00000003, 0x00000005, 0x00000000, 0x00000000, 0x00000001, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000003,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000003, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x46454452, 0x00000100, 0x00000000, 0x00000000,
    0x00000004, 0x0000001c, 0xffff0400, 0x00000100, 0x000000cb, 0x0000009c,
    0x00000003, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000001,
    0x00000001, 0x000000a7, 0x00000002, 0x00000005, 0x00000004, 0xffffffff,
    0x00000000, 0x00000001, 0x0000000d, 0x000000b3, 0x00000002, 0x00000005,
    0x00000004, 0xffffffff, 0x00000001, 0x00000001, 0x0000000d, 0x000000bf,
    0x00000002, 0x00000005, 0x00000004, 0xffffffff, 0x00000002, 0x00000001,
    0x0000000d, 0x53656874, 0x6c706d61, 0x74007265, 0x65546568, 0x72757478,
    0x74005965, 0x65546568, 0x72757478, 0x74005565, 0x65546568, 0x72757478,
    0x4d005665, 0x6f726369, 0x74666f73, 0x29522820, 0x534c4820, 0x6853204c,
    0x72656461, 0x6d6f4320, 0x656c6970, 0x2e362072, 0x36392e33, 0x312e3030,
    0x34383336, 0xababab00, 0x4e475349, 0x0000006c, 0x00000003, 0x00000008,
    0x00000050, 0x00000000, 0x00000001, 0x00000003, 0x00000000, 0x0000000f,
    0x0000005c, 0x00000000, 0x00000000, 0x00000003, 0x00000001, 0x00000303,
    0x00000065, 0x00000000, 0x00000000, 0x00000003, 0x00000002, 0x00000f0f,
    0x505f5653, 0x5449534f, 0x004e4f49, 0x43584554, 0x44524f4f, 0x4c4f4300,
    0xab00524f, 0x4e47534f, 0x0000002c, 0x00000001, 0x00000008, 0x00000020,
    0x00000000, 0x00000000, 0x00000003, 0x00000000, 0x0000000f, 0x545f5653,
    0x45475241, 0xabab0054
};
#else
#error "An appropriate 'yuv' pixel shader is not defined."
#endif

/* The yuv-rendering pixel shader:

    --- D3D11_PixelShader_YUV_BT709.hlsl ---
    Texture2D theTextureY : register(t0);
    Texture2D theTextureU : register(t1);
    Texture2D theTextureV : register(t2);
    SamplerState theSampler : register(s0);

    struct PixelShaderInput
    {
        float4 pos : SV_POSITION;
        float2 tex : TEXCOORD0;
        float4 color : COLOR0;
    };

    float4 main(PixelShaderInput input) : SV_TARGET
    {
        const float3 offset = {-0.0627451017, -0.501960814, -0.501960814};
        const float3 Rcoeff = {1.1644,  0.0000,  1.7927};
        const float3 Gcoeff = {1.1644, -0.2132, -0.5329};
        const float3 Bcoeff = {1.1644,  2.1124,  0.0000};

        float4 Output;

        float3 yuv;
        yuv.x = theTextureY.Sample(theSampler, input.tex).r;
        yuv.y = theTextureU.Sample(theSampler, input.tex).r;
        yuv.z = theTextureV.Sample(theSampler, input.tex).r;

        yuv += offset;
        Output.r = dot(yuv, Rcoeff);
        Output.g = dot(yuv, Gcoeff);
        Output.b = dot(yuv, Bcoeff);
        Output.a = 1.0f;

        return Output * input.color;
    }

*/
#if defined(D3D11_USE_SHADER_MODEL_4_0_level_9_1)
static const DWORD D3D11_PixelShader_YUV_BT709[] = {
    0x43425844, 0x5045fa84, 0xc2908cce, 0x278dacc3, 0xd4276f8f, 0x00000001,
    0x000005e8, 0x00000006, 0x00000038, 0x000001dc, 0x000003bc, 0x00000438,
    0x00000540, 0x000005b4, 0x396e6f41, 0x0000019c, 0x0000019c, 0xffff0200,
    0x0000016c, 0x00000030, 0x00300000, 0x00300000, 0x00300000, 0x00240003,
    0x00300000, 0x00000000, 0x00010001, 0x00020002, 0xffff0200, 0x05000051,
    0xa00f0000, 0xbd808081, 0xbf008081, 0xbf008081, 0x3f800000, 0x05000051,
    0xa00f0001, 0x3f950b0f, 0x3fe57732, 0x00000000, 0x00000000, 0x05000051,
    0xa00f0002, 0x3f950b0f, 0xbe5a511a, 0xbf086c22, 0x00000000, 0x05000051,
    0xa00f0003, 0x3f950b0f, 0x40073190, 0x00000000, 0x00000000, 0x0200001f,
    0x80000000, 0xb0030000, 0x0200001f, 0x80000000, 0xb00f0001, 0x0200001f,
    0x90000000, 0xa00f0800, 0x0200001f, 0x90000000, 0xa00f0801, 0x0200001f,
    0x90000000, 0xa00f0802, 0x03000042, 0x800f0000, 0xb0e40000, 0xa0e40800,
    0x03000042, 0x800f0001, 0xb0e40000, 0xa0e40801, 0x03000042, 0x800f0002,
    0xb0e40000, 0xa0e40802, 0x02000001, 0x80020000, 0x80000001, 0x02000001,
    0x80040000, 0x80000002, 0x03000002, 0x80070000, 0x80e40000, 0xa0e40000,
    0x03000005, 0x80080000, 0x80000000, 0xa0000001, 0x04000004, 0x80010001,
    0x80aa0000, 0xa0550001, 0x80ff0000, 0x03000008, 0x80020001, 0x80e40000,
    0xa0e40002, 0x0400005a, 0x80040001, 0x80e40000, 0xa0e40003, 0xa0aa0003,
    0x02000001, 0x80080001, 0xa0ff0000, 0x03000005, 0x800f0000, 0x80e40001,
    0xb0e40001, 0x02000001, 0x800f0800, 0x80e40000, 0x0000ffff, 0x52444853,
    0x000001d8, 0x00000040, 0x00000076, 0x0300005a, 0x00106000, 0x00000000,
    0x04001858, 0x00107000, 0x00000000, 0x00005555, 0x04001858, 0x00107000,
    0x00000001, 0x00005555, 0x04001858, 0x00107000, 0x00000002, 0x00005555,
    0x03001062, 0x00101032, 0x00000001, 0x03001062, 0x001010f2, 0x00000002,
    0x03000065, 0x001020f2, 0x00000000, 0x02000068, 0x00000002, 0x09000045,
    0x001000f2, 0x00000000, 0x00101046, 0x00000001, 0x00107e46, 0x00000000,
    0x00106000, 0x00000000, 0x09000045, 0x001000f2, 0x00000001, 0x00101046,
    0x00000001, 0x00107e46, 0x00000001, 0x00106000, 0x00000000, 0x05000036,
    0x00100022, 0x00000000, 0x0010000a, 0x00000001, 0x09000045, 0x001000f2,
    0x00000001, 0x00101046, 0x00000001, 0x00107e46, 0x00000002, 0x00106000,
    0x00000000, 0x05000036, 0x00100042, 0x00000000, 0x0010000a, 0x00000001,
    0x0a000000, 0x00100072, 0x00000000, 0x00100246, 0x00000000, 0x00004002,
    0xbd808081, 0xbf008081, 0xbf008081, 0x00000000, 0x0a00000f, 0x00100012,
    0x00000001, 0x00100086, 0x00000000, 0x00004002, 0x3f950b0f, 0x3fe57732,
    0x00000000, 0x00000000, 0x0a000010, 0x00100022, 0x00000001, 0x00100246,
    0x00000000, 0x00004002, 0x3f950b0f, 0xbe5a511a, 0xbf086c22, 0x00000000,
    0x0a00000f, 0x00100042, 0x00000001, 0x00100046, 0x00000000, 0x00004002,
    0x3f950b0f, 0x40073190, 0x00000000, 0x00000000, 0x05000036, 0x00100082,
    0x00000001, 0x00004001, 0x3f800000, 0x07000038, 0x001020f2, 0x00000000,
    0x00100e46, 0x00000001, 0x00101e46, 0x00000002, 0x0100003e, 0x54415453,
    0x00000074, 0x0000000c, 0x00000002, 0x00000000, 0x00000003, 0x00000005,
    0x00000000, 0x00000000, 0x00000001, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000003, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000003, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x46454452, 0x00000100, 0x00000000, 0x00000000, 0x00000004, 0x0000001c,
    0xffff0400, 0x00000100, 0x000000cb, 0x0000009c, 0x00000003, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000001, 0x00000001, 0x000000a7,
    0x00000002, 0x00000005, 0x00000004, 0xffffffff, 0x00000000, 0x00000001,
    0x0000000d, 0x000000b3, 0x00000002, 0x00000005, 0x00000004, 0xffffffff,
    0x00000001, 0x00000001, 0x0000000d, 0x000000bf, 0x00000002, 0x00000005,
    0x00000004, 0xffffffff, 0x00000002, 0x00000001, 0x0000000d, 0x53656874,
    0x6c706d61, 0x74007265, 0x65546568, 0x72757478, 0x74005965, 0x65546568,
    0x72757478, 0x74005565, 0x65546568, 0x72757478, 0x4d005665, 0x6f726369,
    0x74666f73, 0x29522820, 0x534c4820, 0x6853204c, 0x72656461, 0x6d6f4320,
    0x656c6970, 0x2e362072, 0x36392e33, 0x312e3030, 0x34383336, 0xababab00,
    0x4e475349, 0x0000006c, 0x00000003, 0x00000008, 0x00000050, 0x00000000,
    0x00000001, 0x00000003, 0x00000000, 0x0000000f, 0x0000005c, 0x00000000,
    0x00000000, 0x00000003, 0x00000001, 0x00000303, 0x00000065, 0x00000000,
    0x00000000, 0x00000003, 0x00000002, 0x00000f0f, 0x505f5653, 0x5449534f,
    0x004e4f49, 0x43584554, 0x44524f4f, 0x4c4f4300, 0xab00524f, 0x4e47534f,
    0x0000002c, 0x00000001, 0x00000008, 0x00000020, 0x00000000, 0x00000000,
    0x00000003, 0x00000000, 0x0000000f, 0x545f5653, 0x45475241, 0xabab0054
};
#elif defined(D3D11_USE_SHADER_MODEL_4_0_level_9_3)
static const DWORD D3D11_PixelShader_YUV_BT709[] = {
    0x43425844, 0x72d13260, 0xf6c36f65, 0x8b9b28f5, 0x5010733c, 0x00000001,
    0x000005c0, 0x00000006, 0x00000038, 0x000001b4, 0x00000394, 0x00000410,
    0x00000518, 0x0000058c, 0x396e6f41, 0x00000174, 0x00000174, 0xffff0200,
    0x00000144, 0x00000030, 0x00300000, 0x00300000, 0x00300000, 0x00240003,
    0x00300000, 0x00000000, 0x00010001, 0x00020002, 0xffff0201, 0x05000051,
    0xa00f0000, 0xbd808081, 0xbf008081, 0x3f800000, 0x00000000, 0x05000051,
    0xa00f0001, 0x3f950b0f, 0x3fe57732, 0x00000000, 0x40073190, 0x05000051,
    0xa00f0002, 0x3f950b0f, 0xbe5a511a, 0xbf086c22, 0x00000000, 0x0200001f,
    0x80000000, 0xb0030000, 0x0200001f, 0x80000000, 0xb00f0001, 0x0200001f,
    0x90000000, 0xa00f0800, 0x0200001f, 0x90000000, 0xa00f0801, 0x0200001f,
    0x90000000, 0xa00f0802, 0x03000042, 0x800f0000, 0xb0e40000, 0xa0e40801,
    0x03000042, 0x800f0001, 0xb0e40000, 0xa0e40800, 0x02000001, 0x80020001,
    0x80000000, 0x03000042, 0x800f0000, 0xb0e40000, 0xa0e40802, 0x02000001,
    0x80040001, 0x80000000, 0x03000002, 0x80070000, 0x80e40001, 0xa0d40000,
    0x0400005a, 0x80010001, 0x80e80000, 0xa0e40001, 0xa0aa0001, 0x03000008,
    0x80020001, 0x80e40000, 0xa0e40002, 0x0400005a, 0x80040001, 0x80e40000,
    0xa0ec0001, 0xa0aa0001, 0x02000001, 0x80080001, 0xa0aa0000, 0x03000005,
    0x800f0000, 0x80e40001, 0xb0e40001, 0x02000001, 0x800f0800, 0x80e40000,
    0x0000ffff, 0x52444853, 0x000001d8, 0x00000040, 0x00000076, 0x0300005a,
    0x00106000, 0x00000000, 0x04001858, 0x00107000, 0x00000000, 0x00005555,
    0x04001858, 0x00107000, 0x00000001, 0x00005555, 0x04001858, 0x00107000,
    0x00000002, 0x00005555, 0x03001062, 0x00101032, 0x00000001, 0x03001062,
    0x001010f2, 0x00000002, 0x03000065, 0x001020f2, 0x00000000, 0x02000068,
    0x00000002, 0x09000045, 0x001000f2, 0x00000000, 0x00101046, 0x00000001,
    0x00107e46, 0x00000000, 0x00106000, 0x00000000, 0x09000045, 0x001000f2,
    0x00000001, 0x00101046, 0x00000001, 0x00107e46, 0x00000001, 0x00106000,
    0x00000000, 0x05000036, 0x00100022, 0x00000000, 0x0010000a, 0x00000001,
    0x09000045, 0x001000f2, 0x00000001, 0x00101046, 0x00000001, 0x00107e46,
    0x00000002, 0x00106000, 0x00000000, 0x05000036, 0x00100042, 0x00000000,
    0x0010000a, 0x00000001, 0x0a000000, 0x00100072, 0x00000000, 0x00100246,
    0x00000000, 0x00004002, 0xbd808081, 0xbf008081, 0xbf008081, 0x00000000,
    0x0a00000f, 0x00100012, 0x00000001, 0x00100086, 0x00000000, 0x00004002,
    0x3f950b0f, 0x3fe57732, 0x00000000, 0x00000000, 0x0a000010, 0x00100022,
    0x00000001, 0x00100246, 0x00000000, 0x00004002, 0x3f950b0f, 0xbe5a511a,
    0xbf086c22, 0x00000000, 0x0a00000f, 0x00100042, 0x00000001, 0x00100046,
    0x00000000, 0x00004002, 0x3f950b0f, 0x40073190, 0x00000000, 0x00000000,
    0x05000036, 0x00100082, 0x00000001, 0x00004001, 0x3f800000, 0x07000038,
    0x001020f2, 0x00000000, 0x00100e46, 0x00000001, 0x00101e46, 0x00000002,
    0x0100003e, 0x54415453, 0x00000074, 0x0000000c, 0x00000002, 0x00000000,
    0x00000003, 0x00000005, 0x00000000, 0x00000000, 0x00000001, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000003,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000003, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x46454452, 0x00000100, 0x00000000, 0x00000000,
    0x00000004, 0x0000001c, 0xffff0400, 0x00000100, 0x000000cb, 0x0000009c,
    0x00000003, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000001,
    0x00000001, 0x000000a7, 0x00000002, 0x00000005, 0x00000004, 0xffffffff,
    0x00000000, 0x00000001, 0x0000000d, 0x000000b3, 0x00000002, 0x00000005,
    0x00000004, 0xffffffff, 0x00000001, 0x00000001, 0x0000000d, 0x000000bf,
    0x00000002, 0x00000005, 0x00000004, 0xffffffff, 0x00000002, 0x00000001,
    0x0000000d, 0x53656874, 0x6c706d61, 0x74007265, 0x65546568, 0x72757478,
    0x74005965, 0x65546568, 0x72757478, 0x74005565, 0x65546568, 0x72757478,
    0x4d005665, 0x6f726369, 0x74666f73, 0x29522820, 0x534c4820, 0x6853204c,
    0x72656461, 0x6d6f4320, 0x656c6970, 0x2e362072, 0x36392e33, 0x312e3030,
    0x34383336, 0xababab00, 0x4e475349, 0x0000006c, 0x00000003, 0x00000008,
    0x00000050, 0x00000000, 0x00000001, 0x00000003, 0x00000000, 0x0000000f,
    0x0000005c, 0x00000000, 0x00000000, 0x00000003, 0x00000001, 0x00000303,
    0x00000065, 0x00000000, 0x00000000, 0x00000003, 0x00000002, 0x00000f0f,
    0x505f5653, 0x5449534f, 0x004e4f49, 0x43584554, 0x44524f4f, 0x4c4f4300,
    0xab00524f, 0x4e47534f, 0x0000002c, 0x00000001, 0x00000008, 0x00000020,
    0x00000000, 0x00000000, 0x00000003, 0x00000000, 0x0000000f, 0x545f5653,
    0x45475241, 0xabab0054
};
#else
#error "An appropriate 'yuv' pixel shader is not defined."
#endif

/* The yuv-rendering pixel shader:

    --- D3D11_PixelShader_NV12_JPEG.hlsl ---
    Texture2D theTextureY : register(t0);
    Texture2D theTextureUV : register(t1);
    SamplerState theSampler : register(s0);

    struct PixelShaderInput
    {
        float4 pos : SV_POSITION;
        float2 tex : TEXCOORD0;
        float4 color : COLOR0;
    };

    float4 main(PixelShaderInput input) : SV_TARGET
    {
        const float3 offset = {0.0, -0.501960814, -0.501960814};
        const float3 Rcoeff = {1.0000,  0.0000,  1.4020};
        const float3 Gcoeff = {1.0000, -0.3441, -0.7141};
        const float3 Bcoeff = {1.0000,  1.7720,  0.0000};

        float4 Output;

        float3 yuv;
        yuv.x = theTextureY.Sample(theSampler, input.tex).r;
        yuv.yz = theTextureUV.Sample(theSampler, input.tex).rg;

        yuv += offset;
        Output.r = dot(yuv, Rcoeff);
        Output.g = dot(yuv, Gcoeff);
        Output.b = dot(yuv, Bcoeff);
        Output.a = 1.0f;

        return Output * input.color;
    }

*/
#if defined(D3D11_USE_SHADER_MODEL_4_0_level_9_1)
static const DWORD D3D11_PixelShader_NV12_JPEG[] = {
    0x43425844, 0x8fb9c77a, 0xe9e39686, 0x62b0e0e9, 0xd2bf8183, 0x00000001,
    0x00000548, 0x00000006, 0x00000038, 0x000001b0, 0x00000348, 0x000003c4,
    0x000004a0, 0x00000514, 0x396e6f41, 0x00000170, 0x00000170, 0xffff0200,
    0x00000144, 0x0000002c, 0x002c0000, 0x002c0000, 0x002c0000, 0x00240002,
    0x002c0000, 0x00000000, 0x00010001, 0xffff0200, 0x05000051, 0xa00f0000,
    0x00000000, 0xbf008081, 0xbf008081, 0x3f800000, 0x05000051, 0xa00f0001,
    0x3f800000, 0x3fb374bc, 0x00000000, 0x00000000, 0x05000051, 0xa00f0002,
    0x3f800000, 0xbeb02de0, 0xbf36cf42, 0x00000000, 0x05000051, 0xa00f0003,
    0x3f800000, 0x3fe2d0e5, 0x00000000, 0x00000000, 0x0200001f, 0x80000000,
    0xb0030000, 0x0200001f, 0x80000000, 0xb00f0001, 0x0200001f, 0x90000000,
    0xa00f0800, 0x0200001f, 0x90000000, 0xa00f0801, 0x03000042, 0x800f0000,
    0xb0e40000, 0xa0e40800, 0x03000042, 0x800f0001, 0xb0e40000, 0xa0e40801,
    0x02000001, 0x80060000, 0x80d20001, 0x03000002, 0x80070000, 0x80e40000,
    0xa0e40000, 0x03000005, 0x80080000, 0x80000000, 0xa0000001, 0x04000004,
    0x80010001, 0x80aa0000, 0xa0550001, 0x80ff0000, 0x03000008, 0x80020001,
    0x80e40000, 0xa0e40002, 0x0400005a, 0x80040001, 0x80e40000, 0xa0e40003,
    0xa0aa0003, 0x02000001, 0x80080001, 0xa0ff0000, 0x03000005, 0x800f0000,
    0x80e40001, 0xb0e40001, 0x02000001, 0x800f0800, 0x80e40000, 0x0000ffff,
    0x52444853, 0x00000190, 0x00000040, 0x00000064, 0x0300005a, 0x00106000,
    0x00000000, 0x04001858, 0x00107000, 0x00000000, 0x00005555, 0x04001858,
    0x00107000, 0x00000001, 0x00005555, 0x03001062, 0x00101032, 0x00000001,
    0x03001062, 0x001010f2, 0x00000002, 0x03000065, 0x001020f2, 0x00000000,
    0x02000068, 0x00000002, 0x09000045, 0x001000f2, 0x00000000, 0x00101046,
    0x00000001, 0x00107e46, 0x00000000, 0x00106000, 0x00000000, 0x09000045,
    0x001000f2, 0x00000001, 0x00101046, 0x00000001, 0x00107e46, 0x00000001,
    0x00106000, 0x00000000, 0x05000036, 0x00100062, 0x00000000, 0x00100106,
    0x00000001, 0x0a000000, 0x00100072, 0x00000000, 0x00100246, 0x00000000,
    0x00004002, 0x00000000, 0xbf008081, 0xbf008081, 0x00000000, 0x0a00000f,
    0x00100012, 0x00000001, 0x00100086, 0x00000000, 0x00004002, 0x3f800000,
    0x3fb374bc, 0x00000000, 0x00000000, 0x0a000010, 0x00100022, 0x00000001,
    0x00100246, 0x00000000, 0x00004002, 0x3f800000, 0xbeb02de0, 0xbf36cf42,
    0x00000000, 0x0a00000f, 0x00100042, 0x00000001, 0x00100046, 0x00000000,
    0x00004002, 0x3f800000, 0x3fe2d0e5, 0x00000000, 0x00000000, 0x05000036,
    0x00100082, 0x00000001, 0x00004001, 0x3f800000, 0x07000038, 0x001020f2,
    0x00000000, 0x00100e46, 0x00000001, 0x00101e46, 0x00000002, 0x0100003e,
    0x54415453, 0x00000074, 0x0000000a, 0x00000002, 0x00000000, 0x00000003,
    0x00000005, 0x00000000, 0x00000000, 0x00000001, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000002, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000002, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x46454452, 0x000000d4, 0x00000000, 0x00000000, 0x00000003,
    0x0000001c, 0xffff0400, 0x00000100, 0x000000a0, 0x0000007c, 0x00000003,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000001, 0x00000001,
    0x00000087, 0x00000002, 0x00000005, 0x00000004, 0xffffffff, 0x00000000,
    0x00000001, 0x0000000d, 0x00000093, 0x00000002, 0x00000005, 0x00000004,
    0xffffffff, 0x00000001, 0x00000001, 0x0000000d, 0x53656874, 0x6c706d61,
    0x74007265, 0x65546568, 0x72757478, 0x74005965, 0x65546568, 0x72757478,
    0x00565565, 0x7263694d, 0x666f736f, 0x52282074, 0x4c482029, 0x53204c53,
    0x65646168, 0x6f432072, 0x6c69706d, 0x36207265, 0x392e332e, 0x2e303036,
    0x38333631, 0xabab0034, 0x4e475349, 0x0000006c, 0x00000003, 0x00000008,
    0x00000050, 0x00000000, 0x00000001, 0x00000003, 0x00000000, 0x0000000f,
    0x0000005c, 0x00000000, 0x00000000, 0x00000003, 0x00000001, 0x00000303,
    0x00000065, 0x00000000, 0x00000000, 0x00000003, 0x00000002, 0x00000f0f,
    0x505f5653, 0x5449534f, 0x004e4f49, 0x43584554, 0x44524f4f, 0x4c4f4300,
    0xab00524f, 0x4e47534f, 0x0000002c, 0x00000001, 0x00000008, 0x00000020,
    0x00000000, 0x00000000, 0x00000003, 0x00000000, 0x0000000f, 0x545f5653,
    0x45475241, 0xabab0054
};
#elif defined(D3D11_USE_SHADER_MODEL_4_0_level_9_3)
static const DWORD D3D11_PixelShader_NV12_JPEG[] = {
    0x43425844, 0xe33e5d8b, 0x1b5f6461, 0x1afee99f, 0xcc345c04, 0x00000001,
    0x00000520, 0x00000006, 0x00000038, 0x00000188, 0x00000320, 0x0000039c,
    0x00000478, 0x000004ec, 0x396e6f41, 0x00000148, 0x00000148, 0xffff0200,
    0x0000011c, 0x0000002c, 0x002c0000, 0x002c0000, 0x002c0000, 0x00240002,
    0x002c0000, 0x00000000, 0x00010001, 0xffff0201, 0x05000051, 0xa00f0000,
    0x00000000, 0xbf008081, 0x3f800000, 0x3fb374bc, 0x05000051, 0xa00f0001,
    0x3f800000, 0xbeb02de0, 0xbf36cf42, 0x00000000, 0x05000051, 0xa00f0002,
    0x3f800000, 0x3fe2d0e5, 0x00000000, 0x00000000, 0x0200001f, 0x80000000,
    0xb0030000, 0x0200001f, 0x80000000, 0xb00f0001, 0x0200001f, 0x90000000,
    0xa00f0800, 0x0200001f, 0x90000000, 0xa00f0801, 0x03000042, 0x800f0000,
    0xb0e40000, 0xa0e40801, 0x03000042, 0x800f0001, 0xb0e40000, 0xa0e40800,
    0x02000001, 0x80060001, 0x80d00000, 0x03000002, 0x80070000, 0x80e40001,
    0xa0d40000, 0x0400005a, 0x80010001, 0x80e80000, 0xa0ee0000, 0xa0000000,
    0x03000008, 0x80020001, 0x80e40000, 0xa0e40001, 0x0400005a, 0x80040001,
    0x80e40000, 0xa0e40002, 0xa0aa0002, 0x02000001, 0x80080001, 0xa0aa0000,
    0x03000005, 0x800f0000, 0x80e40001, 0xb0e40001, 0x02000001, 0x800f0800,
    0x80e40000, 0x0000ffff, 0x52444853, 0x00000190, 0x00000040, 0x00000064,
    0x0300005a, 0x00106000, 0x00000000, 0x04001858, 0x00107000, 0x00000000,
    0x00005555, 0x04001858, 0x00107000, 0x00000001, 0x00005555, 0x03001062,
    0x00101032, 0x00000001, 0x03001062, 0x001010f2, 0x00000002, 0x03000065,
    0x001020f2, 0x00000000, 0x02000068, 0x00000002, 0x09000045, 0x001000f2,
    0x00000000, 0x00101046, 0x00000001, 0x00107e46, 0x00000000, 0x00106000,
    0x00000000, 0x09000045, 0x001000f2, 0x00000001, 0x00101046, 0x00000001,
    0x00107e46, 0x00000001, 0x00106000, 0x00000000, 0x05000036, 0x00100062,
    0x00000000, 0x00100106, 0x00000001, 0x0a000000, 0x00100072, 0x00000000,
    0x00100246, 0x00000000, 0x00004002, 0x00000000, 0xbf008081, 0xbf008081,
    0x00000000, 0x0a00000f, 0x00100012, 0x00000001, 0x00100086, 0x00000000,
    0x00004002, 0x3f800000, 0x3fb374bc, 0x00000000, 0x00000000, 0x0a000010,
    0x00100022, 0x00000001, 0x00100246, 0x00000000, 0x00004002, 0x3f800000,
    0xbeb02de0, 0xbf36cf42, 0x00000000, 0x0a00000f, 0x00100042, 0x00000001,
    0x00100046, 0x00000000, 0x00004002, 0x3f800000, 0x3fe2d0e5, 0x00000000,
    0x00000000, 0x05000036, 0x00100082, 0x00000001, 0x00004001, 0x3f800000,
    0x07000038, 0x001020f2, 0x00000000, 0x00100e46, 0x00000001, 0x00101e46,
    0x00000002, 0x0100003e, 0x54415453, 0x00000074, 0x0000000a, 0x00000002,
    0x00000000, 0x00000003, 0x00000005, 0x00000000, 0x00000000, 0x00000001,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000002, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000002,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x46454452, 0x000000d4, 0x00000000,
    0x00000000, 0x00000003, 0x0000001c, 0xffff0400, 0x00000100, 0x000000a0,
    0x0000007c, 0x00000003, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000001, 0x00000001, 0x00000087, 0x00000002, 0x00000005, 0x00000004,
    0xffffffff, 0x00000000, 0x00000001, 0x0000000d, 0x00000093, 0x00000002,
    0x00000005, 0x00000004, 0xffffffff, 0x00000001, 0x00000001, 0x0000000d,
    0x53656874, 0x6c706d61, 0x74007265, 0x65546568, 0x72757478, 0x74005965,
    0x65546568, 0x72757478, 0x00565565, 0x7263694d, 0x666f736f, 0x52282074,
    0x4c482029, 0x53204c53, 0x65646168, 0x6f432072, 0x6c69706d, 0x36207265,
    0x392e332e, 0x2e303036, 0x38333631, 0xabab0034, 0x4e475349, 0x0000006c,
    0x00000003, 0x00000008, 0x00000050, 0x00000000, 0x00000001, 0x00000003,
    0x00000000, 0x0000000f, 0x0000005c, 0x00000000, 0x00000000, 0x00000003,
    0x00000001, 0x00000303, 0x00000065, 0x00000000, 0x00000000, 0x00000003,
    0x00000002, 0x00000f0f, 0x505f5653, 0x5449534f, 0x004e4f49, 0x43584554,
    0x44524f4f, 0x4c4f4300, 0xab00524f, 0x4e47534f, 0x0000002c, 0x00000001,
    0x00000008, 0x00000020, 0x00000000, 0x00000000, 0x00000003, 0x00000000,
    0x0000000f, 0x545f5653, 0x45475241, 0xabab0054
};
#else
#error "An appropriate 'yuv' pixel shader is not defined."
#endif

/* The yuv-rendering pixel shader:

    --- D3D11_PixelShader_NV12_BT601.hlsl ---
    Texture2D theTextureY : register(t0);
    Texture2D theTextureUV : register(t1);
    SamplerState theSampler : register(s0);

    struct PixelShaderInput
    {
        float4 pos : SV_POSITION;
        float2 tex : TEXCOORD0;
        float4 color : COLOR0;
    };

    float4 main(PixelShaderInput input) : SV_TARGET
    {
        const float3 offset = {-0.0627451017, -0.501960814, -0.501960814};
        const float3 Rcoeff = {1.1644,  0.0000,  1.5960};
        const float3 Gcoeff = {1.1644, -0.3918, -0.8130};
        const float3 Bcoeff = {1.1644,  2.0172,  0.0000};

        float4 Output;

        float3 yuv;
        yuv.x = theTextureY.Sample(theSampler, input.tex).r;
        yuv.yz = theTextureUV.Sample(theSampler, input.tex).rg;

        yuv += offset;
        Output.r = dot(yuv, Rcoeff);
        Output.g = dot(yuv, Gcoeff);
        Output.b = dot(yuv, Bcoeff);
        Output.a = 1.0f;

        return Output * input.color;
    }

*/
#if defined(D3D11_USE_SHADER_MODEL_4_0_level_9_1)
static const DWORD D3D11_PixelShader_NV12_BT601[] = {
    0x43425844, 0xd1d24a0c, 0x337c447a, 0x22b55cff, 0xb5c9c74b, 0x00000001,
    0x00000548, 0x00000006, 0x00000038, 0x000001b0, 0x00000348, 0x000003c4,
    0x000004a0, 0x00000514, 0x396e6f41, 0x00000170, 0x00000170, 0xffff0200,
    0x00000144, 0x0000002c, 0x002c0000, 0x002c0000, 0x002c0000, 0x00240002,
    0x002c0000, 0x00000000, 0x00010001, 0xffff0200, 0x05000051, 0xa00f0000,
    0xbd808081, 0xbf008081, 0xbf008081, 0x3f800000, 0x05000051, 0xa00f0001,
    0x3f950b0f, 0x3fcc49ba, 0x00000000, 0x00000000, 0x05000051, 0xa00f0002,
    0x3f950b0f, 0xbec89a02, 0xbf5020c5, 0x00000000, 0x05000051, 0xa00f0003,
    0x3f950b0f, 0x400119ce, 0x00000000, 0x00000000, 0x0200001f, 0x80000000,
    0xb0030000, 0x0200001f, 0x80000000, 0xb00f0001, 0x0200001f, 0x90000000,
    0xa00f0800, 0x0200001f, 0x90000000, 0xa00f0801, 0x03000042, 0x800f0000,
    0xb0e40000, 0xa0e40800, 0x03000042, 0x800f0001, 0xb0e40000, 0xa0e40801,
    0x02000001, 0x80060000, 0x80d20001, 0x03000002, 0x80070000, 0x80e40000,
    0xa0e40000, 0x03000005, 0x80080000, 0x80000000, 0xa0000001, 0x04000004,
    0x80010001, 0x80aa0000, 0xa0550001, 0x80ff0000, 0x03000008, 0x80020001,
    0x80e40000, 0xa0e40002, 0x0400005a, 0x80040001, 0x80e40000, 0xa0e40003,
    0xa0aa0003, 0x02000001, 0x80080001, 0xa0ff0000, 0x03000005, 0x800f0000,
    0x80e40001, 0xb0e40001, 0x02000001, 0x800f0800, 0x80e40000, 0x0000ffff,
    0x52444853, 0x00000190, 0x00000040, 0x00000064, 0x0300005a, 0x00106000,
    0x00000000, 0x04001858, 0x00107000, 0x00000000, 0x00005555, 0x04001858,
    0x00107000, 0x00000001, 0x00005555, 0x03001062, 0x00101032, 0x00000001,
    0x03001062, 0x001010f2, 0x00000002, 0x03000065, 0x001020f2, 0x00000000,
    0x02000068, 0x00000002, 0x09000045, 0x001000f2, 0x00000000, 0x00101046,
    0x00000001, 0x00107e46, 0x00000000, 0x00106000, 0x00000000, 0x09000045,
    0x001000f2, 0x00000001, 0x00101046, 0x00000001, 0x00107e46, 0x00000001,
    0x00106000, 0x00000000, 0x05000036, 0x00100062, 0x00000000, 0x00100106,
    0x00000001, 0x0a000000, 0x00100072, 0x00000000, 0x00100246, 0x00000000,
    0x00004002, 0xbd808081, 0xbf008081, 0xbf008081, 0x00000000, 0x0a00000f,
    0x00100012, 0x00000001, 0x00100086, 0x00000000, 0x00004002, 0x3f950b0f,
    0x3fcc49ba, 0x00000000, 0x00000000, 0x0a000010, 0x00100022, 0x00000001,
    0x00100246, 0x00000000, 0x00004002, 0x3f950b0f, 0xbec89a02, 0xbf5020c5,
    0x00000000, 0x0a00000f, 0x00100042, 0x00000001, 0x00100046, 0x00000000,
    0x00004002, 0x3f950b0f, 0x400119ce, 0x00000000, 0x00000000, 0x05000036,
    0x00100082, 0x00000001, 0x00004001, 0x3f800000, 0x07000038, 0x001020f2,
    0x00000000, 0x00100e46, 0x00000001, 0x00101e46, 0x00000002, 0x0100003e,
    0x54415453, 0x00000074, 0x0000000a, 0x00000002, 0x00000000, 0x00000003,
    0x00000005, 0x00000000, 0x00000000, 0x00000001, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000002, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000002, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x46454452, 0x000000d4, 0x00000000, 0x00000000, 0x00000003,
    0x0000001c, 0xffff0400, 0x00000100, 0x000000a0, 0x0000007c, 0x00000003,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000001, 0x00000001,
    0x00000087, 0x00000002, 0x00000005, 0x00000004, 0xffffffff, 0x00000000,
    0x00000001, 0x0000000d, 0x00000093, 0x00000002, 0x00000005, 0x00000004,
    0xffffffff, 0x00000001, 0x00000001, 0x0000000d, 0x53656874, 0x6c706d61,
    0x74007265, 0x65546568, 0x72757478, 0x74005965, 0x65546568, 0x72757478,
    0x00565565, 0x7263694d, 0x666f736f, 0x52282074, 0x4c482029, 0x53204c53,
    0x65646168, 0x6f432072, 0x6c69706d, 0x36207265, 0x392e332e, 0x2e303036,
    0x38333631, 0xabab0034, 0x4e475349, 0x0000006c, 0x00000003, 0x00000008,
    0x00000050, 0x00000000, 0x00000001, 0x00000003, 0x00000000, 0x0000000f,
    0x0000005c, 0x00000000, 0x00000000, 0x00000003, 0x00000001, 0x00000303,
    0x00000065, 0x00000000, 0x00000000, 0x00000003, 0x00000002, 0x00000f0f,
    0x505f5653, 0x5449534f, 0x004e4f49, 0x43584554, 0x44524f4f, 0x4c4f4300,
    0xab00524f, 0x4e47534f, 0x0000002c, 0x00000001, 0x00000008, 0x00000020,
    0x00000000, 0x00000000, 0x00000003, 0x00000000, 0x0000000f, 0x545f5653,
    0x45475241, 0xabab0054
};
#elif defined(D3D11_USE_SHADER_MODEL_4_0_level_9_3)
static const DWORD D3D11_PixelShader_NV12_BT601[] = {
    0x43425844, 0x84b8b692, 0x589b9edd, 0x51ef2f0b, 0xf7247962, 0x00000001,
    0x00000520, 0x00000006, 0x00000038, 0x00000188, 0x00000320, 0x0000039c,
    0x00000478, 0x000004ec, 0x396e6f41, 0x00000148, 0x00000148, 0xffff0200,
    0x0000011c, 0x0000002c, 0x002c0000, 0x002c0000, 0x002c0000, 0x00240002,
    0x002c0000, 0x00000000, 0x00010001, 0xffff0201, 0x05000051, 0xa00f0000,
    0xbd808081, 0xbf008081, 0x3f800000, 0x00000000, 0x05000051, 0xa00f0001,
    0x3f950b0f, 0x3fcc49ba, 0x00000000, 0x400119ce, 0x05000051, 0xa00f0002,
    0x3f950b0f, 0xbec89a02, 0xbf5020c5, 0x00000000, 0x0200001f, 0x80000000,
    0xb0030000, 0x0200001f, 0x80000000, 0xb00f0001, 0x0200001f, 0x90000000,
    0xa00f0800, 0x0200001f, 0x90000000, 0xa00f0801, 0x03000042, 0x800f0000,
    0xb0e40000, 0xa0e40801, 0x03000042, 0x800f0001, 0xb0e40000, 0xa0e40800,
    0x02000001, 0x80060001, 0x80d00000, 0x03000002, 0x80070000, 0x80e40001,
    0xa0d40000, 0x0400005a, 0x80010001, 0x80e80000, 0xa0e40001, 0xa0aa0001,
    0x03000008, 0x80020001, 0x80e40000, 0xa0e40002, 0x0400005a, 0x80040001,
    0x80e40000, 0xa0ec0001, 0xa0aa0001, 0x02000001, 0x80080001, 0xa0aa0000,
    0x03000005, 0x800f0000, 0x80e40001, 0xb0e40001, 0x02000001, 0x800f0800,
    0x80e40000, 0x0000ffff, 0x52444853, 0x00000190, 0x00000040, 0x00000064,
    0x0300005a, 0x00106000, 0x00000000, 0x04001858, 0x00107000, 0x00000000,
    0x00005555, 0x04001858, 0x00107000, 0x00000001, 0x00005555, 0x03001062,
    0x00101032, 0x00000001, 0x03001062, 0x001010f2, 0x00000002, 0x03000065,
    0x001020f2, 0x00000000, 0x02000068, 0x00000002, 0x09000045, 0x001000f2,
    0x00000000, 0x00101046, 0x00000001, 0x00107e46, 0x00000000, 0x00106000,
    0x00000000, 0x09000045, 0x001000f2, 0x00000001, 0x00101046, 0x00000001,
    0x00107e46, 0x00000001, 0x00106000, 0x00000000, 0x05000036, 0x00100062,
    0x00000000, 0x00100106, 0x00000001, 0x0a000000, 0x00100072, 0x00000000,
    0x00100246, 0x00000000, 0x00004002, 0xbd808081, 0xbf008081, 0xbf008081,
    0x00000000, 0x0a00000f, 0x00100012, 0x00000001, 0x00100086, 0x00000000,
    0x00004002, 0x3f950b0f, 0x3fcc49ba, 0x00000000, 0x00000000, 0x0a000010,
    0x00100022, 0x00000001, 0x00100246, 0x00000000, 0x00004002, 0x3f950b0f,
    0xbec89a02, 0xbf5020c5, 0x00000000, 0x0a00000f, 0x00100042, 0x00000001,
    0x00100046, 0x00000000, 0x00004002, 0x3f950b0f, 0x400119ce, 0x00000000,
    0x00000000, 0x05000036, 0x00100082, 0x00000001, 0x00004001, 0x3f800000,
    0x07000038, 0x001020f2, 0x00000000, 0x00100e46, 0x00000001, 0x00101e46,
    0x00000002, 0x0100003e, 0x54415453, 0x00000074, 0x0000000a, 0x00000002,
    0x00000000, 0x00000003, 0x00000005, 0x00000000, 0x00000000, 0x00000001,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000002, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000002,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x46454452, 0x000000d4, 0x00000000,
    0x00000000, 0x00000003, 0x0000001c, 0xffff0400, 0x00000100, 0x000000a0,
    0x0000007c, 0x00000003, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000001, 0x00000001, 0x00000087, 0x00000002, 0x00000005, 0x00000004,
    0xffffffff, 0x00000000, 0x00000001, 0x0000000d, 0x00000093, 0x00000002,
    0x00000005, 0x00000004, 0xffffffff, 0x00000001, 0x00000001, 0x0000000d,
    0x53656874, 0x6c706d61, 0x74007265, 0x65546568, 0x72757478, 0x74005965,
    0x65546568, 0x72757478, 0x00565565, 0x7263694d, 0x666f736f, 0x52282074,
    0x4c482029, 0x53204c53, 0x65646168, 0x6f432072, 0x6c69706d, 0x36207265,
    0x392e332e, 0x2e303036, 0x38333631, 0xabab0034, 0x4e475349, 0x0000006c,
    0x00000003, 0x00000008, 0x00000050, 0x00000000, 0x00000001, 0x00000003,
    0x00000000, 0x0000000f, 0x0000005c, 0x00000000, 0x00000000, 0x00000003,
    0x00000001, 0x00000303, 0x00000065, 0x00000000, 0x00000000, 0x00000003,
    0x00000002, 0x00000f0f, 0x505f5653, 0x5449534f, 0x004e4f49, 0x43584554,
    0x44524f4f, 0x4c4f4300, 0xab00524f, 0x4e47534f, 0x0000002c, 0x00000001,
    0x00000008, 0x00000020, 0x00000000, 0x00000000, 0x00000003, 0x00000000,
    0x0000000f, 0x545f5653, 0x45475241, 0xabab0054
};
#else
#error "An appropriate 'yuv' pixel shader is not defined."
#endif

/* The yuv-rendering pixel shader:

    --- D3D11_PixelShader_NV12_BT709.hlsl ---
    Texture2D theTextureY : register(t0);
    Texture2D theTextureUV : register(t1);
    SamplerState theSampler : register(s0);

    struct PixelShaderInput
    {
        float4 pos : SV_POSITION;
        float2 tex : TEXCOORD0;
        float4 color : COLOR0;
    };

    float4 main(PixelShaderInput input) : SV_TARGET
    {
        const float3 offset = {-0.0627451017, -0.501960814, -0.501960814};
        const float3 Rcoeff = {1.1644,  0.0000,  1.7927};
        const float3 Gcoeff = {1.1644, -0.2132, -0.5329};
        const float3 Bcoeff = {1.1644,  2.1124,  0.0000};

        float4 Output;

        float3 yuv;
        yuv.x = theTextureY.Sample(theSampler, input.tex).r;
        yuv.yz = theTextureUV.Sample(theSampler, input.tex).rg;

        yuv += offset;
        Output.r = dot(yuv, Rcoeff);
        Output.g = dot(yuv, Gcoeff);
        Output.b = dot(yuv, Bcoeff);
        Output.a = 1.0f;

        return Output * input.color;
    }

*/
#if defined(D3D11_USE_SHADER_MODEL_4_0_level_9_1)
static const DWORD D3D11_PixelShader_NV12_BT709[] = {
    0x43425844, 0x40d1b8d5, 0xaf4b78b5, 0x907fd0b5, 0xa2d23686, 0x00000001,
    0x00000548, 0x00000006, 0x00000038, 0x000001b0, 0x00000348, 0x000003c4,
    0x000004a0, 0x00000514, 0x396e6f41, 0x00000170, 0x00000170, 0xffff0200,
    0x00000144, 0x0000002c, 0x002c0000, 0x002c0000, 0x002c0000, 0x00240002,
    0x002c0000, 0x00000000, 0x00010001, 0xffff0200, 0x05000051, 0xa00f0000,
    0xbd808081, 0xbf008081, 0xbf008081, 0x3f800000, 0x05000051, 0xa00f0001,
    0x3f950b0f, 0x3fe57732, 0x00000000, 0x00000000, 0x05000051, 0xa00f0002,
    0x3f950b0f, 0xbe5a511a, 0xbf086c22, 0x00000000, 0x05000051, 0xa00f0003,
    0x3f950b0f, 0x40073190, 0x00000000, 0x00000000, 0x0200001f, 0x80000000,
    0xb0030000, 0x0200001f, 0x80000000, 0xb00f0001, 0x0200001f, 0x90000000,
    0xa00f0800, 0x0200001f, 0x90000000, 0xa00f0801, 0x03000042, 0x800f0000,
    0xb0e40000, 0xa0e40800, 0x03000042, 0x800f0001, 0xb0e40000, 0xa0e40801,
    0x02000001, 0x80060000, 0x80d20001, 0x03000002, 0x80070000, 0x80e40000,
    0xa0e40000, 0x03000005, 0x80080000, 0x80000000, 0xa0000001, 0x04000004,
    0x80010001, 0x80aa0000, 0xa0550001, 0x80ff0000, 0x03000008, 0x80020001,
    0x80e40000, 0xa0e40002, 0x0400005a, 0x80040001, 0x80e40000, 0xa0e40003,
    0xa0aa0003, 0x02000001, 0x80080001, 0xa0ff0000, 0x03000005, 0x800f0000,
    0x80e40001, 0xb0e40001, 0x02000001, 0x800f0800, 0x80e40000, 0x0000ffff,
    0x52444853, 0x00000190, 0x00000040, 0x00000064, 0x0300005a, 0x00106000,
    0x00000000, 0x04001858, 0x00107000, 0x00000000, 0x00005555, 0x04001858,
    0x00107000, 0x00000001, 0x00005555, 0x03001062, 0x00101032, 0x00000001,
    0x03001062, 0x001010f2, 0x00000002, 0x03000065, 0x001020f2, 0x00000000,
    0x02000068, 0x00000002, 0x09000045, 0x001000f2, 0x00000000, 0x00101046,
    0x00000001, 0x00107e46, 0x00000000, 0x00106000, 0x00000000, 0x09000045,
    0x001000f2, 0x00000001, 0x00101046, 0x00000001, 0x00107e46, 0x00000001,
    0x00106000, 0x00000000, 0x05000036, 0x00100062, 0x00000000, 0x00100106,
    0x00000001, 0x0a000000, 0x00100072, 0x00000000, 0x00100246, 0x00000000,
    0x00004002, 0xbd808081, 0xbf008081, 0xbf008081, 0x00000000, 0x0a00000f,
    0x00100012, 0x00000001, 0x00100086, 0x00000000, 0x00004002, 0x3f950b0f,
    0x3fe57732, 0x00000000, 0x00000000, 0x0a000010, 0x00100022, 0x00000001,
    0x00100246, 0x00000000, 0x00004002, 0x3f950b0f, 0xbe5a511a, 0xbf086c22,
    0x00000000, 0x0a00000f, 0x00100042, 0x00000001, 0x00100046, 0x00000000,
    0x00004002, 0x3f950b0f, 0x40073190, 0x00000000, 0x00000000, 0x05000036,
    0x00100082, 0x00000001, 0x00004001, 0x3f800000, 0x07000038, 0x001020f2,
    0x00000000, 0x00100e46, 0x00000001, 0x00101e46, 0x00000002, 0x0100003e,
    0x54415453, 0x00000074, 0x0000000a, 0x00000002, 0x00000000, 0x00000003,
    0x00000005, 0x00000000, 0x00000000, 0x00000001, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000002, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000002, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x46454452, 0x000000d4, 0x00000000, 0x00000000, 0x00000003,
    0x0000001c, 0xffff0400, 0x00000100, 0x000000a0, 0x0000007c, 0x00000003,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000001, 0x00000001,
    0x00000087, 0x00000002, 0x00000005, 0x00000004, 0xffffffff, 0x00000000,
    0x00000001, 0x0000000d, 0x00000093, 0x00000002, 0x00000005, 0x00000004,
    0xffffffff, 0x00000001, 0x00000001, 0x0000000d, 0x53656874, 0x6c706d61,
    0x74007265, 0x65546568, 0x72757478, 0x74005965, 0x65546568, 0x72757478,
    0x00565565, 0x7263694d, 0x666f736f, 0x52282074, 0x4c482029, 0x53204c53,
    0x65646168, 0x6f432072, 0x6c69706d, 0x36207265, 0x392e332e, 0x2e303036,
    0x38333631, 0xabab0034, 0x4e475349, 0x0000006c, 0x00000003, 0x00000008,
    0x00000050, 0x00000000, 0x00000001, 0x00000003, 0x00000000, 0x0000000f,
    0x0000005c, 0x00000000, 0x00000000, 0x00000003, 0x00000001, 0x00000303,
    0x00000065, 0x00000000, 0x00000000, 0x00000003, 0x00000002, 0x00000f0f,
    0x505f5653, 0x5449534f, 0x004e4f49, 0x43584554, 0x44524f4f, 0x4c4f4300,
    0xab00524f, 0x4e47534f, 0x0000002c, 0x00000001, 0x00000008, 0x00000020,
    0x00000000, 0x00000000, 0x00000003, 0x00000000, 0x0000000f, 0x545f5653,
    0x45475241, 0xabab0054
};
#elif defined(D3D11_USE_SHADER_MODEL_4_0_level_9_3)
static const DWORD D3D11_PixelShader_NV12_BT709[] = {
    0x43425844, 0xa3bba187, 0x71b6afa9, 0x15998682, 0x2d545cae, 0x00000001,
    0x00000520, 0x00000006, 0x00000038, 0x00000188, 0x00000320, 0x0000039c,
    0x00000478, 0x000004ec, 0x396e6f41, 0x00000148, 0x00000148, 0xffff0200,
    0x0000011c, 0x0000002c, 0x002c0000, 0x002c0000, 0x002c0000, 0x00240002,
    0x002c0000, 0x00000000, 0x00010001, 0xffff0201, 0x05000051, 0xa00f0000,
    0xbd808081, 0xbf008081, 0x3f800000, 0x00000000, 0x05000051, 0xa00f0001,
    0x3f950b0f, 0x3fe57732, 0x00000000, 0x40073190, 0x05000051, 0xa00f0002,
    0x3f950b0f, 0xbe5a511a, 0xbf086c22, 0x00000000, 0x0200001f, 0x80000000,
    0xb0030000, 0x0200001f, 0x80000000, 0xb00f0001, 0x0200001f, 0x90000000,
    0xa00f0800, 0x0200001f, 0x90000000, 0xa00f0801, 0x03000042, 0x800f0000,
    0xb0e40000, 0xa0e40801, 0x03000042, 0x800f0001, 0xb0e40000, 0xa0e40800,
    0x02000001, 0x80060001, 0x80d00000, 0x03000002, 0x80070000, 0x80e40001,
    0xa0d40000, 0x0400005a, 0x80010001, 0x80e80000, 0xa0e40001, 0xa0aa0001,
    0x03000008, 0x80020001, 0x80e40000, 0xa0e40002, 0x0400005a, 0x80040001,
    0x80e40000, 0xa0ec0001, 0xa0aa0001, 0x02000001, 0x80080001, 0xa0aa0000,
    0x03000005, 0x800f0000, 0x80e40001, 0xb0e40001, 0x02000001, 0x800f0800,
    0x80e40000, 0x0000ffff, 0x52444853, 0x00000190, 0x00000040, 0x00000064,
    0x0300005a, 0x00106000, 0x00000000, 0x04001858, 0x00107000, 0x00000000,
    0x00005555, 0x04001858, 0x00107000, 0x00000001, 0x00005555, 0x03001062,
    0x00101032, 0x00000001, 0x03001062, 0x001010f2, 0x00000002, 0x03000065,
    0x001020f2, 0x00000000, 0x02000068, 0x00000002, 0x09000045, 0x001000f2,
    0x00000000, 0x00101046, 0x00000001, 0x00107e46, 0x00000000, 0x00106000,
    0x00000000, 0x09000045, 0x001000f2, 0x00000001, 0x00101046, 0x00000001,
    0x00107e46, 0x00000001, 0x00106000, 0x00000000, 0x05000036, 0x00100062,
    0x00000000, 0x00100106, 0x00000001, 0x0a000000, 0x00100072, 0x00000000,
    0x00100246, 0x00000000, 0x00004002, 0xbd808081, 0xbf008081, 0xbf008081,
    0x00000000, 0x0a00000f, 0x00100012, 0x00000001, 0x00100086, 0x00000000,
    0x00004002, 0x3f950b0f, 0x3fe57732, 0x00000000, 0x00000000, 0x0a000010,
    0x00100022, 0x00000001, 0x00100246, 0x00000000, 0x00004002, 0x3f950b0f,
    0xbe5a511a, 0xbf086c22, 0x00000000, 0x0a00000f, 0x00100042, 0x00000001,
    0x00100046, 0x00000000, 0x00004002, 0x3f950b0f, 0x40073190, 0x00000000,
    0x00000000, 0x05000036, 0x00100082, 0x00000001, 0x00004001, 0x3f800000,
    0x07000038, 0x001020f2, 0x00000000, 0x00100e46, 0x00000001, 0x00101e46,
    0x00000002, 0x0100003e, 0x54415453, 0x00000074, 0x0000000a, 0x00000002,
    0x00000000, 0x00000003, 0x00000005, 0x00000000, 0x00000000, 0x00000001,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000002, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000002,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x46454452, 0x000000d4, 0x00000000,
    0x00000000, 0x00000003, 0x0000001c, 0xffff0400, 0x00000100, 0x000000a0,
    0x0000007c, 0x00000003, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000001, 0x00000001, 0x00000087, 0x00000002, 0x00000005, 0x00000004,
    0xffffffff, 0x00000000, 0x00000001, 0x0000000d, 0x00000093, 0x00000002,
    0x00000005, 0x00000004, 0xffffffff, 0x00000001, 0x00000001, 0x0000000d,
    0x53656874, 0x6c706d61, 0x74007265, 0x65546568, 0x72757478, 0x74005965,
    0x65546568, 0x72757478, 0x00565565, 0x7263694d, 0x666f736f, 0x52282074,
    0x4c482029, 0x53204c53, 0x65646168, 0x6f432072, 0x6c69706d, 0x36207265,
    0x392e332e, 0x2e303036, 0x38333631, 0xabab0034, 0x4e475349, 0x0000006c,
    0x00000003, 0x00000008, 0x00000050, 0x00000000, 0x00000001, 0x00000003,
    0x00000000, 0x0000000f, 0x0000005c, 0x00000000, 0x00000000, 0x00000003,
    0x00000001, 0x00000303, 0x00000065, 0x00000000, 0x00000000, 0x00000003,
    0x00000002, 0x00000f0f, 0x505f5653, 0x5449534f, 0x004e4f49, 0x43584554,
    0x44524f4f, 0x4c4f4300, 0xab00524f, 0x4e47534f, 0x0000002c, 0x00000001,
    0x00000008, 0x00000020, 0x00000000, 0x00000000, 0x00000003, 0x00000000,
    0x0000000f, 0x545f5653, 0x45475241, 0xabab0054
};
#else
#error "An appropriate 'yuv' pixel shader is not defined."
#endif

/* The yuv-rendering pixel shader:

    --- D3D11_PixelShader_NV21_JPEG.hlsl ---
    Texture2D theTextureY : register(t0);
    Texture2D theTextureUV : register(t1);
    SamplerState theSampler : register(s0);

    struct PixelShaderInput
    {
        float4 pos : SV_POSITION;
        float2 tex : TEXCOORD0;
        float4 color : COLOR0;
    };

    float4 main(PixelShaderInput input) : SV_TARGET
    {
        const float3 offset = {0.0, -0.501960814, -0.501960814};
        const float3 Rcoeff = {1.0000,  0.0000,  1.4020};
        const float3 Gcoeff = {1.0000, -0.3441, -0.7141};
        const float3 Bcoeff = {1.0000,  1.7720,  0.0000};

        float4 Output;

        float3 yuv;
        yuv.x = theTextureY.Sample(theSampler, input.tex).r;
        yuv.yz = theTextureUV.Sample(theSampler, input.tex).gr;

        yuv += offset;
        Output.r = dot(yuv, Rcoeff);
        Output.g = dot(yuv, Gcoeff);
        Output.b = dot(yuv, Bcoeff);
        Output.a = 1.0f;

        return Output * input.color;
    }

*/
#if defined(D3D11_USE_SHADER_MODEL_4_0_level_9_1)
static const DWORD D3D11_PixelShader_NV21_JPEG[] = {
    0x43425844, 0x9c41f579, 0xfd1019d8, 0x7c27e3ae, 0x52e3a5ff, 0x00000001,
    0x00000554, 0x00000006, 0x00000038, 0x000001bc, 0x00000354, 0x000003d0,
    0x000004ac, 0x00000520, 0x396e6f41, 0x0000017c, 0x0000017c, 0xffff0200,
    0x00000150, 0x0000002c, 0x002c0000, 0x002c0000, 0x002c0000, 0x00240002,
    0x002c0000, 0x00000000, 0x00010001, 0xffff0200, 0x05000051, 0xa00f0000,
    0x00000000, 0xbf008081, 0xbf008081, 0x3f800000, 0x05000051, 0xa00f0001,
    0x3f800000, 0x3fb374bc, 0x00000000, 0x00000000, 0x05000051, 0xa00f0002,
    0x3f800000, 0xbeb02de0, 0xbf36cf42, 0x00000000, 0x05000051, 0xa00f0003,
    0x3f800000, 0x3fe2d0e5, 0x00000000, 0x00000000, 0x0200001f, 0x80000000,
    0xb0030000, 0x0200001f, 0x80000000, 0xb00f0001, 0x0200001f, 0x90000000,
    0xa00f0800, 0x0200001f, 0x90000000, 0xa00f0801, 0x03000042, 0x800f0000,
    0xb0e40000, 0xa0e40800, 0x03000042, 0x800f0001, 0xb0e40000, 0xa0e40801,
    0x02000001, 0x80020000, 0x80550001, 0x02000001, 0x80040000, 0x80000001,
    0x03000002, 0x80070000, 0x80e40000, 0xa0e40000, 0x03000005, 0x80080000,
    0x80000000, 0xa0000001, 0x04000004, 0x80010001, 0x80aa0000, 0xa0550001,
    0x80ff0000, 0x03000008, 0x80020001, 0x80e40000, 0xa0e40002, 0x0400005a,
    0x80040001, 0x80e40000, 0xa0e40003, 0xa0aa0003, 0x02000001, 0x80080001,
    0xa0ff0000, 0x03000005, 0x800f0000, 0x80e40001, 0xb0e40001, 0x02000001,
    0x800f0800, 0x80e40000, 0x0000ffff, 0x52444853, 0x00000190, 0x00000040,
    0x00000064, 0x0300005a, 0x00106000, 0x00000000, 0x04001858, 0x00107000,
    0x00000000, 0x00005555, 0x04001858, 0x00107000, 0x00000001, 0x00005555,
    0x03001062, 0x00101032, 0x00000001, 0x03001062, 0x001010f2, 0x00000002,
    0x03000065, 0x001020f2, 0x00000000, 0x02000068, 0x00000002, 0x09000045,
    0x001000f2, 0x00000000, 0x00101046, 0x00000001, 0x00107e46, 0x00000000,
    0x00106000, 0x00000000, 0x09000045, 0x001000f2, 0x00000001, 0x00101046,
    0x00000001, 0x00107e46, 0x00000001, 0x00106000, 0x00000000, 0x05000036,
    0x00100062, 0x00000000, 0x00100456, 0x00000001, 0x0a000000, 0x00100072,
    0x00000000, 0x00100246, 0x00000000, 0x00004002, 0x00000000, 0xbf008081,
    0xbf008081, 0x00000000, 0x0a00000f, 0x00100012, 0x00000001, 0x00100086,
    0x00000000, 0x00004002, 0x3f800000, 0x3fb374bc, 0x00000000, 0x00000000,
    0x0a000010, 0x00100022, 0x00000001, 0x00100246, 0x00000000, 0x00004002,
    0x3f800000, 0xbeb02de0, 0xbf36cf42, 0x00000000, 0x0a00000f, 0x00100042,
    0x00000001, 0x00100046, 0x00000000, 0x00004002, 0x3f800000, 0x3fe2d0e5,
    0x00000000, 0x00000000, 0x05000036, 0x00100082, 0x00000001, 0x00004001,
    0x3f800000, 0x07000038, 0x001020f2, 0x00000000, 0x00100e46, 0x00000001,
    0x00101e46, 0x00000002, 0x0100003e, 0x54415453, 0x00000074, 0x0000000a,
    0x00000002, 0x00000000, 0x00000003, 0x00000005, 0x00000000, 0x00000000,
    0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000002, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000002, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x46454452, 0x000000d4,
    0x00000000, 0x00000000, 0x00000003, 0x0000001c, 0xffff0400, 0x00000100,
    0x000000a0, 0x0000007c, 0x00000003, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000001, 0x00000001, 0x00000087, 0x00000002, 0x00000005,
    0x00000004, 0xffffffff, 0x00000000, 0x00000001, 0x0000000d, 0x00000093,
    0x00000002, 0x00000005, 0x00000004, 0xffffffff, 0x00000001, 0x00000001,
    0x0000000d, 0x53656874, 0x6c706d61, 0x74007265, 0x65546568, 0x72757478,
    0x74005965, 0x65546568, 0x72757478, 0x00565565, 0x7263694d, 0x666f736f,
    0x52282074, 0x4c482029, 0x53204c53, 0x65646168, 0x6f432072, 0x6c69706d,
    0x36207265, 0x392e332e, 0x2e303036, 0x38333631, 0xabab0034, 0x4e475349,
    0x0000006c, 0x00000003, 0x00000008, 0x00000050, 0x00000000, 0x00000001,
    0x00000003, 0x00000000, 0x0000000f, 0x0000005c, 0x00000000, 0x00000000,
    0x00000003, 0x00000001, 0x00000303, 0x00000065, 0x00000000, 0x00000000,
    0x00000003, 0x00000002, 0x00000f0f, 0x505f5653, 0x5449534f, 0x004e4f49,
    0x43584554, 0x44524f4f, 0x4c4f4300, 0xab00524f, 0x4e47534f, 0x0000002c,
    0x00000001, 0x00000008, 0x00000020, 0x00000000, 0x00000000, 0x00000003,
    0x00000000, 0x0000000f, 0x545f5653, 0x45475241, 0xabab0054
};
#elif defined(D3D11_USE_SHADER_MODEL_4_0_level_9_3)
static const DWORD D3D11_PixelShader_NV21_JPEG[] = {
    0x43425844, 0x5705ccc9, 0xeb57571d, 0x8ce556e0, 0x2adef743, 0x00000001,
    0x00000520, 0x00000006, 0x00000038, 0x00000188, 0x00000320, 0x0000039c,
    0x00000478, 0x000004ec, 0x396e6f41, 0x00000148, 0x00000148, 0xffff0200,
    0x0000011c, 0x0000002c, 0x002c0000, 0x002c0000, 0x002c0000, 0x00240002,
    0x002c0000, 0x00000000, 0x00010001, 0xffff0201, 0x05000051, 0xa00f0000,
    0x00000000, 0xbf008081, 0x3f800000, 0x3fb374bc, 0x05000051, 0xa00f0001,
    0x3f800000, 0xbeb02de0, 0xbf36cf42, 0x00000000, 0x05000051, 0xa00f0002,
    0x3f800000, 0x3fe2d0e5, 0x00000000, 0x00000000, 0x0200001f, 0x80000000,
    0xb0030000, 0x0200001f, 0x80000000, 0xb00f0001, 0x0200001f, 0x90000000,
    0xa00f0800, 0x0200001f, 0x90000000, 0xa00f0801, 0x03000042, 0x800f0000,
    0xb0e40000, 0xa0e40801, 0x03000042, 0x800f0001, 0xb0e40000, 0xa0e40800,
    0x02000001, 0x80060001, 0x80c40000, 0x03000002, 0x80070000, 0x80e40001,
    0xa0d40000, 0x0400005a, 0x80010001, 0x80e80000, 0xa0ee0000, 0xa0000000,
    0x03000008, 0x80020001, 0x80e40000, 0xa0e40001, 0x0400005a, 0x80040001,
    0x80e40000, 0xa0e40002, 0xa0aa0002, 0x02000001, 0x80080001, 0xa0aa0000,
    0x03000005, 0x800f0000, 0x80e40001, 0xb0e40001, 0x02000001, 0x800f0800,
    0x80e40000, 0x0000ffff, 0x52444853, 0x00000190, 0x00000040, 0x00000064,
    0x0300005a, 0x00106000, 0x00000000, 0x04001858, 0x00107000, 0x00000000,
    0x00005555, 0x04001858, 0x00107000, 0x00000001, 0x00005555, 0x03001062,
    0x00101032, 0x00000001, 0x03001062, 0x001010f2, 0x00000002, 0x03000065,
    0x001020f2, 0x00000000, 0x02000068, 0x00000002, 0x09000045, 0x001000f2,
    0x00000000, 0x00101046, 0x00000001, 0x00107e46, 0x00000000, 0x00106000,
    0x00000000, 0x09000045, 0x001000f2, 0x00000001, 0x00101046, 0x00000001,
    0x00107e46, 0x00000001, 0x00106000, 0x00000000, 0x05000036, 0x00100062,
    0x00000000, 0x00100456, 0x00000001, 0x0a000000, 0x00100072, 0x00000000,
    0x00100246, 0x00000000, 0x00004002, 0x00000000, 0xbf008081, 0xbf008081,
    0x00000000, 0x0a00000f, 0x00100012, 0x00000001, 0x00100086, 0x00000000,
    0x00004002, 0x3f800000, 0x3fb374bc, 0x00000000, 0x00000000, 0x0a000010,
    0x00100022, 0x00000001, 0x00100246, 0x00000000, 0x00004002, 0x3f800000,
    0xbeb02de0, 0xbf36cf42, 0x00000000, 0x0a00000f, 0x00100042, 0x00000001,
    0x00100046, 0x00000000, 0x00004002, 0x3f800000, 0x3fe2d0e5, 0x00000000,
    0x00000000, 0x05000036, 0x00100082, 0x00000001, 0x00004001, 0x3f800000,
    0x07000038, 0x001020f2, 0x00000000, 0x00100e46, 0x00000001, 0x00101e46,
    0x00000002, 0x0100003e, 0x54415453, 0x00000074, 0x0000000a, 0x00000002,
    0x00000000, 0x00000003, 0x00000005, 0x00000000, 0x00000000, 0x00000001,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000002, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000002,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x46454452, 0x000000d4, 0x00000000,
    0x00000000, 0x00000003, 0x0000001c, 0xffff0400, 0x00000100, 0x000000a0,
    0x0000007c, 0x00000003, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000001, 0x00000001, 0x00000087, 0x00000002, 0x00000005, 0x00000004,
    0xffffffff, 0x00000000, 0x00000001, 0x0000000d, 0x00000093, 0x00000002,
    0x00000005, 0x00000004, 0xffffffff, 0x00000001, 0x00000001, 0x0000000d,
    0x53656874, 0x6c706d61, 0x74007265, 0x65546568, 0x72757478, 0x74005965,
    0x65546568, 0x72757478, 0x00565565, 0x7263694d, 0x666f736f, 0x52282074,
    0x4c482029, 0x53204c53, 0x65646168, 0x6f432072, 0x6c69706d, 0x36207265,
    0x392e332e, 0x2e303036, 0x38333631, 0xabab0034, 0x4e475349, 0x0000006c,
    0x00000003, 0x00000008, 0x00000050, 0x00000000, 0x00000001, 0x00000003,
    0x00000000, 0x0000000f, 0x0000005c, 0x00000000, 0x00000000, 0x00000003,
    0x00000001, 0x00000303, 0x00000065, 0x00000000, 0x00000000, 0x00000003,
    0x00000002, 0x00000f0f, 0x505f5653, 0x5449534f, 0x004e4f49, 0x43584554,
    0x44524f4f, 0x4c4f4300, 0xab00524f, 0x4e47534f, 0x0000002c, 0x00000001,
    0x00000008, 0x00000020, 0x00000000, 0x00000000, 0x00000003, 0x00000000,
    0x0000000f, 0x545f5653, 0x45475241, 0xabab0054
};
#else
#error "An appropriate 'yuv' pixel shader is not defined."
#endif

/* The yuv-rendering pixel shader:

    --- D3D11_PixelShader_NV21_BT601.hlsl ---
    Texture2D theTextureY : register(t0);
    Texture2D theTextureUV : register(t1);
    SamplerState theSampler : register(s0);

    struct PixelShaderInput
    {
        float4 pos : SV_POSITION;
        float2 tex : TEXCOORD0;
        float4 color : COLOR0;
    };

    float4 main(PixelShaderInput input) : SV_TARGET
    {
        const float3 offset = {-0.0627451017, -0.501960814, -0.501960814};
        const float3 Rcoeff = {1.1644,  0.0000,  1.5960};
        const float3 Gcoeff = {1.1644, -0.3918, -0.8130};
        const float3 Bcoeff = {1.1644,  2.0172,  0.0000};

        float4 Output;

        float3 yuv;
        yuv.x = theTextureY.Sample(theSampler, input.tex).r;
        yuv.yz = theTextureUV.Sample(theSampler, input.tex).gr;

        yuv += offset;
        Output.r = dot(yuv, Rcoeff);
        Output.g = dot(yuv, Gcoeff);
        Output.b = dot(yuv, Bcoeff);
        Output.a = 1.0f;

        return Output * input.color;
    }

*/
#if defined(D3D11_USE_SHADER_MODEL_4_0_level_9_1)
static const DWORD D3D11_PixelShader_NV21_BT601[] = {
    0x43425844, 0x7fc6cfdc, 0xba87a4ff, 0xa72685a6, 0xa051b38c, 0x00000001,
    0x00000554, 0x00000006, 0x00000038, 0x000001bc, 0x00000354, 0x000003d0,
    0x000004ac, 0x00000520, 0x396e6f41, 0x0000017c, 0x0000017c, 0xffff0200,
    0x00000150, 0x0000002c, 0x002c0000, 0x002c0000, 0x002c0000, 0x00240002,
    0x002c0000, 0x00000000, 0x00010001, 0xffff0200, 0x05000051, 0xa00f0000,
    0xbd808081, 0xbf008081, 0xbf008081, 0x3f800000, 0x05000051, 0xa00f0001,
    0x3f950b0f, 0x3fcc49ba, 0x00000000, 0x00000000, 0x05000051, 0xa00f0002,
    0x3f950b0f, 0xbec89a02, 0xbf5020c5, 0x00000000, 0x05000051, 0xa00f0003,
    0x3f950b0f, 0x400119ce, 0x00000000, 0x00000000, 0x0200001f, 0x80000000,
    0xb0030000, 0x0200001f, 0x80000000, 0xb00f0001, 0x0200001f, 0x90000000,
    0xa00f0800, 0x0200001f, 0x90000000, 0xa00f0801, 0x03000042, 0x800f0000,
    0xb0e40000, 0xa0e40800, 0x03000042, 0x800f0001, 0xb0e40000, 0xa0e40801,
    0x02000001, 0x80020000, 0x80550001, 0x02000001, 0x80040000, 0x80000001,
    0x03000002, 0x80070000, 0x80e40000, 0xa0e40000, 0x03000005, 0x80080000,
    0x80000000, 0xa0000001, 0x04000004, 0x80010001, 0x80aa0000, 0xa0550001,
    0x80ff0000, 0x03000008, 0x80020001, 0x80e40000, 0xa0e40002, 0x0400005a,
    0x80040001, 0x80e40000, 0xa0e40003, 0xa0aa0003, 0x02000001, 0x80080001,
    0xa0ff0000, 0x03000005, 0x800f0000, 0x80e40001, 0xb0e40001, 0x02000001,
    0x800f0800, 0x80e40000, 0x0000ffff, 0x52444853, 0x00000190, 0x00000040,
    0x00000064, 0x0300005a, 0x00106000, 0x00000000, 0x04001858, 0x00107000,
    0x00000000, 0x00005555, 0x04001858, 0x00107000, 0x00000001, 0x00005555,
    0x03001062, 0x00101032, 0x00000001, 0x03001062, 0x001010f2, 0x00000002,
    0x03000065, 0x001020f2, 0x00000000, 0x02000068, 0x00000002, 0x09000045,
    0x001000f2, 0x00000000, 0x00101046, 0x00000001, 0x00107e46, 0x00000000,
    0x00106000, 0x00000000, 0x09000045, 0x001000f2, 0x00000001, 0x00101046,
    0x00000001, 0x00107e46, 0x00000001, 0x00106000, 0x00000000, 0x05000036,
    0x00100062, 0x00000000, 0x00100456, 0x00000001, 0x0a000000, 0x00100072,
    0x00000000, 0x00100246, 0x00000000, 0x00004002, 0xbd808081, 0xbf008081,
    0xbf008081, 0x00000000, 0x0a00000f, 0x00100012, 0x00000001, 0x00100086,
    0x00000000, 0x00004002, 0x3f950b0f, 0x3fcc49ba, 0x00000000, 0x00000000,
    0x0a000010, 0x00100022, 0x00000001, 0x00100246, 0x00000000, 0x00004002,
    0x3f950b0f, 0xbec89a02, 0xbf5020c5, 0x00000000, 0x0a00000f, 0x00100042,
    0x00000001, 0x00100046, 0x00000000, 0x00004002, 0x3f950b0f, 0x400119ce,
    0x00000000, 0x00000000, 0x05000036, 0x00100082, 0x00000001, 0x00004001,
    0x3f800000, 0x07000038, 0x001020f2, 0x00000000, 0x00100e46, 0x00000001,
    0x00101e46, 0x00000002, 0x0100003e, 0x54415453, 0x00000074, 0x0000000a,
    0x00000002, 0x00000000, 0x00000003, 0x00000005, 0x00000000, 0x00000000,
    0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000002, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000002, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x46454452, 0x000000d4,
    0x00000000, 0x00000000, 0x00000003, 0x0000001c, 0xffff0400, 0x00000100,
    0x000000a0, 0x0000007c, 0x00000003, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000001, 0x00000001, 0x00000087, 0x00000002, 0x00000005,
    0x00000004, 0xffffffff, 0x00000000, 0x00000001, 0x0000000d, 0x00000093,
    0x00000002, 0x00000005, 0x00000004, 0xffffffff, 0x00000001, 0x00000001,
    0x0000000d, 0x53656874, 0x6c706d61, 0x74007265, 0x65546568, 0x72757478,
    0x74005965, 0x65546568, 0x72757478, 0x00565565, 0x7263694d, 0x666f736f,
    0x52282074, 0x4c482029, 0x53204c53, 0x65646168, 0x6f432072, 0x6c69706d,
    0x36207265, 0x392e332e, 0x2e303036, 0x38333631, 0xabab0034, 0x4e475349,
    0x0000006c, 0x00000003, 0x00000008, 0x00000050, 0x00000000, 0x00000001,
    0x00000003, 0x00000000, 0x0000000f, 0x0000005c, 0x00000000, 0x00000000,
    0x00000003, 0x00000001, 0x00000303, 0x00000065, 0x00000000, 0x00000000,
    0x00000003, 0x00000002, 0x00000f0f, 0x505f5653, 0x5449534f, 0x004e4f49,
    0x43584554, 0x44524f4f, 0x4c4f4300, 0xab00524f, 0x4e47534f, 0x0000002c,
    0x00000001, 0x00000008, 0x00000020, 0x00000000, 0x00000000, 0x00000003,
    0x00000000, 0x0000000f, 0x545f5653, 0x45475241, 0xabab0054
};
#elif defined(D3D11_USE_SHADER_MODEL_4_0_level_9_3)
static const DWORD D3D11_PixelShader_NV21_BT601[] = {
    0x43425844, 0x1e92bca4, 0xfeb04e20, 0x3f4226b1, 0xc89c58ad, 0x00000001,
    0x00000520, 0x00000006, 0x00000038, 0x00000188, 0x00000320, 0x0000039c,
    0x00000478, 0x000004ec, 0x396e6f41, 0x00000148, 0x00000148, 0xffff0200,
    0x0000011c, 0x0000002c, 0x002c0000, 0x002c0000, 0x002c0000, 0x00240002,
    0x002c0000, 0x00000000, 0x00010001, 0xffff0201, 0x05000051, 0xa00f0000,
    0xbd808081, 0xbf008081, 0x3f800000, 0x00000000, 0x05000051, 0xa00f0001,
    0x3f950b0f, 0x3fcc49ba, 0x00000000, 0x400119ce, 0x05000051, 0xa00f0002,
    0x3f950b0f, 0xbec89a02, 0xbf5020c5, 0x00000000, 0x0200001f, 0x80000000,
    0xb0030000, 0x0200001f, 0x80000000, 0xb00f0001, 0x0200001f, 0x90000000,
    0xa00f0800, 0x0200001f, 0x90000000, 0xa00f0801, 0x03000042, 0x800f0000,
    0xb0e40000, 0xa0e40801, 0x03000042, 0x800f0001, 0xb0e40000, 0xa0e40800,
    0x02000001, 0x80060001, 0x80c40000, 0x03000002, 0x80070000, 0x80e40001,
    0xa0d40000, 0x0400005a, 0x80010001, 0x80e80000, 0xa0e40001, 0xa0aa0001,
    0x03000008, 0x80020001, 0x80e40000, 0xa0e40002, 0x0400005a, 0x80040001,
    0x80e40000, 0xa0ec0001, 0xa0aa0001, 0x02000001, 0x80080001, 0xa0aa0000,
    0x03000005, 0x800f0000, 0x80e40001, 0xb0e40001, 0x02000001, 0x800f0800,
    0x80e40000, 0x0000ffff, 0x52444853, 0x00000190, 0x00000040, 0x00000064,
    0x0300005a, 0x00106000, 0x00000000, 0x04001858, 0x00107000, 0x00000000,
    0x00005555, 0x04001858, 0x00107000, 0x00000001, 0x00005555, 0x03001062,
    0x00101032, 0x00000001, 0x03001062, 0x001010f2, 0x00000002, 0x03000065,
    0x001020f2, 0x00000000, 0x02000068, 0x00000002, 0x09000045, 0x001000f2,
    0x00000000, 0x00101046, 0x00000001, 0x00107e46, 0x00000000, 0x00106000,
    0x00000000, 0x09000045, 0x001000f2, 0x00000001, 0x00101046, 0x00000001,
    0x00107e46, 0x00000001, 0x00106000, 0x00000000, 0x05000036, 0x00100062,
    0x00000000, 0x00100456, 0x00000001, 0x0a000000, 0x00100072, 0x00000000,
    0x00100246, 0x00000000, 0x00004002, 0xbd808081, 0xbf008081, 0xbf008081,
    0x00000000, 0x0a00000f, 0x00100012, 0x00000001, 0x00100086, 0x00000000,
    0x00004002, 0x3f950b0f, 0x3fcc49ba, 0x00000000, 0x00000000, 0x0a000010,
    0x00100022, 0x00000001, 0x00100246, 0x00000000, 0x00004002, 0x3f950b0f,
    0xbec89a02, 0xbf5020c5, 0x00000000, 0x0a00000f, 0x00100042, 0x00000001,
    0x00100046, 0x00000000, 0x00004002, 0x3f950b0f, 0x400119ce, 0x00000000,
    0x00000000, 0x05000036, 0x00100082, 0x00000001, 0x00004001, 0x3f800000,
    0x07000038, 0x001020f2, 0x00000000, 0x00100e46, 0x00000001, 0x00101e46,
    0x00000002, 0x0100003e, 0x54415453, 0x00000074, 0x0000000a, 0x00000002,
    0x00000000, 0x00000003, 0x00000005, 0x00000000, 0x00000000, 0x00000001,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000002, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000002,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x46454452, 0x000000d4, 0x00000000,
    0x00000000, 0x00000003, 0x0000001c, 0xffff0400, 0x00000100, 0x000000a0,
    0x0000007c, 0x00000003, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000001, 0x00000001, 0x00000087, 0x00000002, 0x00000005, 0x00000004,
    0xffffffff, 0x00000000, 0x00000001, 0x0000000d, 0x00000093, 0x00000002,
    0x00000005, 0x00000004, 0xffffffff, 0x00000001, 0x00000001, 0x0000000d,
    0x53656874, 0x6c706d61, 0x74007265, 0x65546568, 0x72757478, 0x74005965,
    0x65546568, 0x72757478, 0x00565565, 0x7263694d, 0x666f736f, 0x52282074,
    0x4c482029, 0x53204c53, 0x65646168, 0x6f432072, 0x6c69706d, 0x36207265,
    0x392e332e, 0x2e303036, 0x38333631, 0xabab0034, 0x4e475349, 0x0000006c,
    0x00000003, 0x00000008, 0x00000050, 0x00000000, 0x00000001, 0x00000003,
    0x00000000, 0x0000000f, 0x0000005c, 0x00000000, 0x00000000, 0x00000003,
    0x00000001, 0x00000303, 0x00000065, 0x00000000, 0x00000000, 0x00000003,
    0x00000002, 0x00000f0f, 0x505f5653, 0x5449534f, 0x004e4f49, 0x43584554,
    0x44524f4f, 0x4c4f4300, 0xab00524f, 0x4e47534f, 0x0000002c, 0x00000001,
    0x00000008, 0x00000020, 0x00000000, 0x00000000, 0x00000003, 0x00000000,
    0x0000000f, 0x545f5653, 0x45475241, 0xabab0054
};
#else
#error "An appropriate 'yuv' pixel shader is not defined."
#endif

/* The yuv-rendering pixel shader:

    --- D3D11_PixelShader_NV21_BT709.hlsl ---
    Texture2D theTextureY : register(t0);
    Texture2D theTextureUV : register(t1);
    SamplerState theSampler : register(s0);

    struct PixelShaderInput
    {
        float4 pos : SV_POSITION;
        float2 tex : TEXCOORD0;
        float4 color : COLOR0;
    };

    float4 main(PixelShaderInput input) : SV_TARGET
    {
        const float3 offset = {-0.0627451017, -0.501960814, -0.501960814};
        const float3 Rcoeff = {1.1644,  0.0000,  1.7927};
        const float3 Gcoeff = {1.1644, -0.2132, -0.5329};
        const float3 Bcoeff = {1.1644,  2.1124,  0.0000};

        float4 Output;

        float3 yuv;
        yuv.x = theTextureY.Sample(theSampler, input.tex).r;
        yuv.yz = theTextureUV.Sample(theSampler, input.tex).gr;

        yuv += offset;
        Output.r = dot(yuv, Rcoeff);
        Output.g = dot(yuv, Gcoeff);
        Output.b = dot(yuv, Bcoeff);
        Output.a = 1.0f;

        return Output * input.color;
    }

*/
#if defined(D3D11_USE_SHADER_MODEL_4_0_level_9_1)
static const DWORD D3D11_PixelShader_NV21_BT709[] = {
    0x43425844, 0x754ba6c4, 0xe321a747, 0x23680787, 0x6bb1bdcc, 0x00000001,
    0x00000554, 0x00000006, 0x00000038, 0x000001bc, 0x00000354, 0x000003d0,
    0x000004ac, 0x00000520, 0x396e6f41, 0x0000017c, 0x0000017c, 0xffff0200,
    0x00000150, 0x0000002c, 0x002c0000, 0x002c0000, 0x002c0000, 0x00240002,
    0x002c0000, 0x00000000, 0x00010001, 0xffff0200, 0x05000051, 0xa00f0000,
    0xbd808081, 0xbf008081, 0xbf008081, 0x3f800000, 0x05000051, 0xa00f0001,
    0x3f950b0f, 0x3fe57732, 0x00000000, 0x00000000, 0x05000051, 0xa00f0002,
    0x3f950b0f, 0xbe5a511a, 0xbf086c22, 0x00000000, 0x05000051, 0xa00f0003,
    0x3f950b0f, 0x40073190, 0x00000000, 0x00000000, 0x0200001f, 0x80000000,
    0xb0030000, 0x0200001f, 0x80000000, 0xb00f0001, 0x0200001f, 0x90000000,
    0xa00f0800, 0x0200001f, 0x90000000, 0xa00f0801, 0x03000042, 0x800f0000,
    0xb0e40000, 0xa0e40800, 0x03000042, 0x800f0001, 0xb0e40000, 0xa0e40801,
    0x02000001, 0x80020000, 0x80550001, 0x02000001, 0x80040000, 0x80000001,
    0x03000002, 0x80070000, 0x80e40000, 0xa0e40000, 0x03000005, 0x80080000,
    0x80000000, 0xa0000001, 0x04000004, 0x80010001, 0x80aa0000, 0xa0550001,
    0x80ff0000, 0x03000008, 0x80020001, 0x80e40000, 0xa0e40002, 0x0400005a,
    0x80040001, 0x80e40000, 0xa0e40003, 0xa0aa0003, 0x02000001, 0x80080001,
    0xa0ff0000, 0x03000005, 0x800f0000, 0x80e40001, 0xb0e40001, 0x02000001,
    0x800f0800, 0x80e40000, 0x0000ffff, 0x52444853, 0x00000190, 0x00000040,
    0x00000064, 0x0300005a, 0x00106000, 0x00000000, 0x04001858, 0x00107000,
    0x00000000, 0x00005555, 0x04001858, 0x00107000, 0x00000001, 0x00005555,
    0x03001062, 0x00101032, 0x00000001, 0x03001062, 0x001010f2, 0x00000002,
    0x03000065, 0x001020f2, 0x00000000, 0x02000068, 0x00000002, 0x09000045,
    0x001000f2, 0x00000000, 0x00101046, 0x00000001, 0x00107e46, 0x00000000,
    0x00106000, 0x00000000, 0x09000045, 0x001000f2, 0x00000001, 0x00101046,
    0x00000001, 0x00107e46, 0x00000001, 0x00106000, 0x00000000, 0x05000036,
    0x00100062, 0x00000000, 0x00100456, 0x00000001, 0x0a000000, 0x00100072,
    0x00000000, 0x00100246, 0x00000000, 0x00004002, 0xbd808081, 0xbf008081,
    0xbf008081, 0x00000000, 0x0a00000f, 0x00100012, 0x00000001, 0x00100086,
    0x00000000, 0x00004002, 0x3f950b0f, 0x3fe57732, 0x00000000, 0x00000000,
    0x0a000010, 0x00100022, 0x00000001, 0x00100246, 0x00000000, 0x00004002,
    0x3f950b0f, 0xbe5a511a, 0xbf086c22, 0x00000000, 0x0a00000f, 0x00100042,
    0x00000001, 0x00100046, 0x00000000, 0x00004002, 0x3f950b0f, 0x40073190,
    0x00000000, 0x00000000, 0x05000036, 0x00100082, 0x00000001, 0x00004001,
    0x3f800000, 0x07000038, 0x001020f2, 0x00000000, 0x00100e46, 0x00000001,
    0x00101e46, 0x00000002, 0x0100003e, 0x54415453, 0x00000074, 0x0000000a,
    0x00000002, 0x00000000, 0x00000003, 0x00000005, 0x00000000, 0x00000000,
    0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000002, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000002, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x46454452, 0x000000d4,
    0x00000000, 0x00000000, 0x00000003, 0x0000001c, 0xffff0400, 0x00000100,
    0x000000a0, 0x0000007c, 0x00000003, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000001, 0x00000001, 0x00000087, 0x00000002, 0x00000005,
    0x00000004, 0xffffffff, 0x00000000, 0x00000001, 0x0000000d, 0x00000093,
    0x00000002, 0x00000005, 0x00000004, 0xffffffff, 0x00000001, 0x00000001,
    0x0000000d, 0x53656874, 0x6c706d61, 0x74007265, 0x65546568, 0x72757478,
    0x74005965, 0x65546568, 0x72757478, 0x00565565, 0x7263694d, 0x666f736f,
    0x52282074, 0x4c482029, 0x53204c53, 0x65646168, 0x6f432072, 0x6c69706d,
    0x36207265, 0x392e332e, 0x2e303036, 0x38333631, 0xabab0034, 0x4e475349,
    0x0000006c, 0x00000003, 0x00000008, 0x00000050, 0x00000000, 0x00000001,
    0x00000003, 0x00000000, 0x0000000f, 0x0000005c, 0x00000000, 0x00000000,
    0x00000003, 0x00000001, 0x00000303, 0x00000065, 0x00000000, 0x00000000,
    0x00000003, 0x00000002, 0x00000f0f, 0x505f5653, 0x5449534f, 0x004e4f49,
    0x43584554, 0x44524f4f, 0x4c4f4300, 0xab00524f, 0x4e47534f, 0x0000002c,
    0x00000001, 0x00000008, 0x00000020, 0x00000000, 0x00000000, 0x00000003,
    0x00000000, 0x0000000f, 0x545f5653, 0x45475241, 0xabab0054
};
#elif defined(D3D11_USE_SHADER_MODEL_4_0_level_9_3)
static const DWORD D3D11_PixelShader_NV21_BT709[] = {
    0x43425844, 0xb6219b20, 0xb71bbcf7, 0xf361cc45, 0xc4d5f5be, 0x00000001,
    0x00000520, 0x00000006, 0x00000038, 0x00000188, 0x00000320, 0x0000039c,
    0x00000478, 0x000004ec, 0x396e6f41, 0x00000148, 0x00000148, 0xffff0200,
    0x0000011c, 0x0000002c, 0x002c0000, 0x002c0000, 0x002c0000, 0x00240002,
    0x002c0000, 0x00000000, 0x00010001, 0xffff0201, 0x05000051, 0xa00f0000,
    0xbd808081, 0xbf008081, 0x3f800000, 0x00000000, 0x05000051, 0xa00f0001,
    0x3f950b0f, 0x3fe57732, 0x00000000, 0x40073190, 0x05000051, 0xa00f0002,
    0x3f950b0f, 0xbe5a511a, 0xbf086c22, 0x00000000, 0x0200001f, 0x80000000,
    0xb0030000, 0x0200001f, 0x80000000, 0xb00f0001, 0x0200001f, 0x90000000,
    0xa00f0800, 0x0200001f, 0x90000000, 0xa00f0801, 0x03000042, 0x800f0000,
    0xb0e40000, 0xa0e40801, 0x03000042, 0x800f0001, 0xb0e40000, 0xa0e40800,
    0x02000001, 0x80060001, 0x80c40000, 0x03000002, 0x80070000, 0x80e40001,
    0xa0d40000, 0x0400005a, 0x80010001, 0x80e80000, 0xa0e40001, 0xa0aa0001,
    0x03000008, 0x80020001, 0x80e40000, 0xa0e40002, 0x0400005a, 0x80040001,
    0x80e40000, 0xa0ec0001, 0xa0aa0001, 0x02000001, 0x80080001, 0xa0aa0000,
    0x03000005, 0x800f0000, 0x80e40001, 0xb0e40001, 0x02000001, 0x800f0800,
    0x80e40000, 0x0000ffff, 0x52444853, 0x00000190, 0x00000040, 0x00000064,
    0x0300005a, 0x00106000, 0x00000000, 0x04001858, 0x00107000, 0x00000000,
    0x00005555, 0x04001858, 0x00107000, 0x00000001, 0x00005555, 0x03001062,
    0x00101032, 0x00000001, 0x03001062, 0x001010f2, 0x00000002, 0x03000065,
    0x001020f2, 0x00000000, 0x02000068, 0x00000002, 0x09000045, 0x001000f2,
    0x00000000, 0x00101046, 0x00000001, 0x00107e46, 0x00000000, 0x00106000,
    0x00000000, 0x09000045, 0x001000f2, 0x00000001, 0x00101046, 0x00000001,
    0x00107e46, 0x00000001, 0x00106000, 0x00000000, 0x05000036, 0x00100062,
    0x00000000, 0x00100456, 0x00000001, 0x0a000000, 0x00100072, 0x00000000,
    0x00100246, 0x00000000, 0x00004002, 0xbd808081, 0xbf008081, 0xbf008081,
    0x00000000, 0x0a00000f, 0x00100012, 0x00000001, 0x00100086, 0x00000000,
    0x00004002, 0x3f950b0f, 0x3fe57732, 0x00000000, 0x00000000, 0x0a000010,
    0x00100022, 0x00000001, 0x00100246, 0x00000000, 0x00004002, 0x3f950b0f,
    0xbe5a511a, 0xbf086c22, 0x00000000, 0x0a00000f, 0x00100042, 0x00000001,
    0x00100046, 0x00000000, 0x00004002, 0x3f950b0f, 0x40073190, 0x00000000,
    0x00000000, 0x05000036, 0x00100082, 0x00000001, 0x00004001, 0x3f800000,
    0x07000038, 0x001020f2, 0x00000000, 0x00100e46, 0x00000001, 0x00101e46,
    0x00000002, 0x0100003e, 0x54415453, 0x00000074, 0x0000000a, 0x00000002,
    0x00000000, 0x00000003, 0x00000005, 0x00000000, 0x00000000, 0x00000001,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000002, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000002,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x46454452, 0x000000d4, 0x00000000,
    0x00000000, 0x00000003, 0x0000001c, 0xffff0400, 0x00000100, 0x000000a0,
    0x0000007c, 0x00000003, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000001, 0x00000001, 0x00000087, 0x00000002, 0x00000005, 0x00000004,
    0xffffffff, 0x00000000, 0x00000001, 0x0000000d, 0x00000093, 0x00000002,
    0x00000005, 0x00000004, 0xffffffff, 0x00000001, 0x00000001, 0x0000000d,
    0x53656874, 0x6c706d61, 0x74007265, 0x65546568, 0x72757478, 0x74005965,
    0x65546568, 0x72757478, 0x00565565, 0x7263694d, 0x666f736f, 0x52282074,
    0x4c482029, 0x53204c53, 0x65646168, 0x6f432072, 0x6c69706d, 0x36207265,
    0x392e332e, 0x2e303036, 0x38333631, 0xabab0034, 0x4e475349, 0x0000006c,
    0x00000003, 0x00000008, 0x00000050, 0x00000000, 0x00000001, 0x00000003,
    0x00000000, 0x0000000f, 0x0000005c, 0x00000000, 0x00000000, 0x00000003,
    0x00000001, 0x00000303, 0x00000065, 0x00000000, 0x00000000, 0x00000003,
    0x00000002, 0x00000f0f, 0x505f5653, 0x5449534f, 0x004e4f49, 0x43584554,
    0x44524f4f, 0x4c4f4300, 0xab00524f, 0x4e47534f, 0x0000002c, 0x00000001,
    0x00000008, 0x00000020, 0x00000000, 0x00000000, 0x00000003, 0x00000000,
    0x0000000f, 0x545f5653, 0x45475241, 0xabab0054
};
#else
#error "An appropriate 'yuv' pixel shader is not defined."
#endif

/* The sole vertex shader:

   --- D3D11_VertexShader.hlsl ---
   #pragma pack_matrix( row_major )

   cbuffer VertexShaderConstants : register(b0)
   {
       matrix model;
       matrix projectionAndView;
   };

   struct VertexShaderInput
   {
       float3 pos : POSITION;
       float2 tex : TEXCOORD0;
       float4 color : COLOR0;
   };

   struct VertexShaderOutput
   {
       float4 pos : SV_POSITION;
       float2 tex : TEXCOORD0;
       float4 color : COLOR0;
   };

   VertexShaderOutput main(VertexShaderInput input)
   {
       VertexShaderOutput output;
       float4 pos = float4(input.pos, 1.0f);

       // Transform the vertex position into projected space.
       pos = mul(pos, model);
       pos = mul(pos, projectionAndView);
       output.pos = pos;

       // Pass through texture coordinates and color values without transformation
       output.tex = input.tex;
       output.color = input.color;

       return output;
   }
*/
#if defined(D3D11_USE_SHADER_MODEL_4_0_level_9_1)
static const DWORD D3D11_VertexShader[] = {
    0x43425844, 0x62dfae5f, 0x3e8bd8df, 0x9ec97127, 0x5044eefb, 0x00000001,
    0x00000598, 0x00000006, 0x00000038, 0x0000016c, 0x00000334, 0x000003b0,
    0x000004b4, 0x00000524, 0x396e6f41, 0x0000012c, 0x0000012c, 0xfffe0200,
    0x000000f8, 0x00000034, 0x00240001, 0x00300000, 0x00300000, 0x00240000,
    0x00300001, 0x00000000, 0x00010008, 0x00000000, 0x00000000, 0xfffe0200,
    0x0200001f, 0x80000005, 0x900f0000, 0x0200001f, 0x80010005, 0x900f0001,
    0x0200001f, 0x80020005, 0x900f0002, 0x03000005, 0x800f0000, 0x90550000,
    0xa0e40002, 0x04000004, 0x800f0000, 0x90000000, 0xa0e40001, 0x80e40000,
    0x04000004, 0x800f0000, 0x90aa0000, 0xa0e40003, 0x80e40000, 0x03000002,
    0x800f0000, 0x80e40000, 0xa0e40004, 0x03000005, 0x800f0001, 0x80550000,
    0xa0e40006, 0x04000004, 0x800f0001, 0x80000000, 0xa0e40005, 0x80e40001,
    0x04000004, 0x800f0001, 0x80aa0000, 0xa0e40007, 0x80e40001, 0x04000004,
    0x800f0000, 0x80ff0000, 0xa0e40008, 0x80e40001, 0x04000004, 0xc0030000,
    0x80ff0000, 0xa0e40000, 0x80e40000, 0x02000001, 0xc00c0000, 0x80e40000,
    0x02000001, 0xe0030000, 0x90e40001, 0x02000001, 0xe00f0001, 0x90e40002,
    0x0000ffff, 0x52444853, 0x000001c0, 0x00010040, 0x00000070, 0x04000059,
    0x00208e46, 0x00000000, 0x00000008, 0x0300005f, 0x00101072, 0x00000000,
    0x0300005f, 0x00101032, 0x00000001, 0x0300005f, 0x001010f2, 0x00000002,
    0x04000067, 0x001020f2, 0x00000000, 0x00000001, 0x03000065, 0x00102032,
    0x00000001, 0x03000065, 0x001020f2, 0x00000002, 0x02000068, 0x00000002,
    0x08000038, 0x001000f2, 0x00000000, 0x00101556, 0x00000000, 0x00208e46,
    0x00000000, 0x00000001, 0x0a000032, 0x001000f2, 0x00000000, 0x00101006,
    0x00000000, 0x00208e46, 0x00000000, 0x00000000, 0x00100e46, 0x00000000,
    0x0a000032, 0x001000f2, 0x00000000, 0x00101aa6, 0x00000000, 0x00208e46,
    0x00000000, 0x00000002, 0x00100e46, 0x00000000, 0x08000000, 0x001000f2,
    0x00000000, 0x00100e46, 0x00000000, 0x00208e46, 0x00000000, 0x00000003,
    0x08000038, 0x001000f2, 0x00000001, 0x00100556, 0x00000000, 0x00208e46,
    0x00000000, 0x00000005, 0x0a000032, 0x001000f2, 0x00000001, 0x00100006,
    0x00000000, 0x00208e46, 0x00000000, 0x00000004, 0x00100e46, 0x00000001,
    0x0a000032, 0x001000f2, 0x00000001, 0x00100aa6, 0x00000000, 0x00208e46,
    0x00000000, 0x00000006, 0x00100e46, 0x00000001, 0x0a000032, 0x001020f2,
    0x00000000, 0x00100ff6, 0x00000000, 0x00208e46, 0x00000000, 0x00000007,
    0x00100e46, 0x00000001, 0x05000036, 0x00102032, 0x00000001, 0x00101046,
    0x00000001, 0x05000036, 0x001020f2, 0x00000002, 0x00101e46, 0x00000002,
    0x0100003e, 0x54415453, 0x00000074, 0x0000000b, 0x00000002, 0x00000000,
    0x00000006, 0x00000003, 0x00000000, 0x00000000, 0x00000001, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000003, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x46454452, 0x000000fc, 0x00000001, 0x00000054,
    0x00000001, 0x0000001c, 0xfffe0400, 0x00000100, 0x000000c6, 0x0000003c,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000001,
    0x00000001, 0x74726556, 0x68537865, 0x72656461, 0x736e6f43, 0x746e6174,
    0xabab0073, 0x0000003c, 0x00000002, 0x0000006c, 0x00000080, 0x00000000,
    0x00000000, 0x0000009c, 0x00000000, 0x00000040, 0x00000002, 0x000000a4,
    0x00000000, 0x000000b4, 0x00000040, 0x00000040, 0x00000002, 0x000000a4,
    0x00000000, 0x65646f6d, 0xabab006c, 0x00030002, 0x00040004, 0x00000000,
    0x00000000, 0x6a6f7270, 0x69746365, 0x6e416e6f, 0x65695664, 0x694d0077,
    0x736f7263, 0x2074666f, 0x20295228, 0x4c534c48, 0x61685320, 0x20726564,
    0x706d6f43, 0x72656c69, 0x332e3920, 0x32392e30, 0x312e3030, 0x34383336,
    0xababab00, 0x4e475349, 0x00000068, 0x00000003, 0x00000008, 0x00000050,
    0x00000000, 0x00000000, 0x00000003, 0x00000000, 0x00000707, 0x00000059,
    0x00000000, 0x00000000, 0x00000003, 0x00000001, 0x00000303, 0x00000062,
    0x00000000, 0x00000000, 0x00000003, 0x00000002, 0x00000f0f, 0x49534f50,
    0x4e4f4954, 0x58455400, 0x524f4f43, 0x4f430044, 0x00524f4c, 0x4e47534f,
    0x0000006c, 0x00000003, 0x00000008, 0x00000050, 0x00000000, 0x00000001,
    0x00000003, 0x00000000, 0x0000000f, 0x0000005c, 0x00000000, 0x00000000,
    0x00000003, 0x00000001, 0x00000c03, 0x00000065, 0x00000000, 0x00000000,
    0x00000003, 0x00000002, 0x0000000f, 0x505f5653, 0x5449534f, 0x004e4f49,
    0x43584554, 0x44524f4f, 0x4c4f4300, 0xab00524f
};
#elif defined(D3D11_USE_SHADER_MODEL_4_0_level_9_3)
static const DWORD D3D11_VertexShader[] = {
    0x43425844, 0x01a24e41, 0x696af551, 0x4b2a87d1, 0x82ea03f6, 0x00000001,
    0x00000598, 0x00000006, 0x00000038, 0x0000016c, 0x00000334, 0x000003b0,
    0x000004b4, 0x00000524, 0x396e6f41, 0x0000012c, 0x0000012c, 0xfffe0200,
    0x000000f8, 0x00000034, 0x00240001, 0x00300000, 0x00300000, 0x00240000,
    0x00300001, 0x00000000, 0x00010008, 0x00000000, 0x00000000, 0xfffe0201,
    0x0200001f, 0x80000005, 0x900f0000, 0x0200001f, 0x80010005, 0x900f0001,
    0x0200001f, 0x80020005, 0x900f0002, 0x03000005, 0x800f0000, 0x90550000,
    0xa0e40002, 0x04000004, 0x800f0000, 0x90000000, 0xa0e40001, 0x80e40000,
    0x04000004, 0x800f0000, 0x90aa0000, 0xa0e40003, 0x80e40000, 0x03000002,
    0x800f0000, 0x80e40000, 0xa0e40004, 0x03000005, 0x800f0001, 0x80550000,
    0xa0e40006, 0x04000004, 0x800f0001, 0x80000000, 0xa0e40005, 0x80e40001,
    0x04000004, 0x800f0001, 0x80aa0000, 0xa0e40007, 0x80e40001, 0x04000004,
    0x800f0000, 0x80ff0000, 0xa0e40008, 0x80e40001, 0x04000004, 0xc0030000,
    0x80ff0000, 0xa0e40000, 0x80e40000, 0x02000001, 0xc00c0000, 0x80e40000,
    0x02000001, 0xe0030000, 0x90e40001, 0x02000001, 0xe00f0001, 0x90e40002,
    0x0000ffff, 0x52444853, 0x000001c0, 0x00010040, 0x00000070, 0x04000059,
    0x00208e46, 0x00000000, 0x00000008, 0x0300005f, 0x00101072, 0x00000000,
    0x0300005f, 0x00101032, 0x00000001, 0x0300005f, 0x001010f2, 0x00000002,
    0x04000067, 0x001020f2, 0x00000000, 0x00000001, 0x03000065, 0x00102032,
    0x00000001, 0x03000065, 0x001020f2, 0x00000002, 0x02000068, 0x00000002,
    0x08000038, 0x001000f2, 0x00000000, 0x00101556, 0x00000000, 0x00208e46,
    0x00000000, 0x00000001, 0x0a000032, 0x001000f2, 0x00000000, 0x00101006,
    0x00000000, 0x00208e46, 0x00000000, 0x00000000, 0x00100e46, 0x00000000,
    0x0a000032, 0x001000f2, 0x00000000, 0x00101aa6, 0x00000000, 0x00208e46,
    0x00000000, 0x00000002, 0x00100e46, 0x00000000, 0x08000000, 0x001000f2,
    0x00000000, 0x00100e46, 0x00000000, 0x00208e46, 0x00000000, 0x00000003,
    0x08000038, 0x001000f2, 0x00000001, 0x00100556, 0x00000000, 0x00208e46,
    0x00000000, 0x00000005, 0x0a000032, 0x001000f2, 0x00000001, 0x00100006,
    0x00000000, 0x00208e46, 0x00000000, 0x00000004, 0x00100e46, 0x00000001,
    0x0a000032, 0x001000f2, 0x00000001, 0x00100aa6, 0x00000000, 0x00208e46,
    0x00000000, 0x00000006, 0x00100e46, 0x00000001, 0x0a000032, 0x001020f2,
    0x00000000, 0x00100ff6, 0x00000000, 0x00208e46, 0x00000000, 0x00000007,
    0x00100e46, 0x00000001, 0x05000036, 0x00102032, 0x00000001, 0x00101046,
    0x00000001, 0x05000036, 0x001020f2, 0x00000002, 0x00101e46, 0x00000002,
    0x0100003e, 0x54415453, 0x00000074, 0x0000000b, 0x00000002, 0x00000000,
    0x00000006, 0x00000003, 0x00000000, 0x00000000, 0x00000001, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000003, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x46454452, 0x000000fc, 0x00000001, 0x00000054,
    0x00000001, 0x0000001c, 0xfffe0400, 0x00000100, 0x000000c6, 0x0000003c,
    0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000001,
    0x00000001, 0x74726556, 0x68537865, 0x72656461, 0x736e6f43, 0x746e6174,
    0xabab0073, 0x0000003c, 0x00000002, 0x0000006c, 0x00000080, 0x00000000,
    0x00000000, 0x0000009c, 0x00000000, 0x00000040, 0x00000002, 0x000000a4,
    0x00000000, 0x000000b4, 0x00000040, 0x00000040, 0x00000002, 0x000000a4,
    0x00000000, 0x65646f6d, 0xabab006c, 0x00030002, 0x00040004, 0x00000000,
    0x00000000, 0x6a6f7270, 0x69746365, 0x6e416e6f, 0x65695664, 0x694d0077,
    0x736f7263, 0x2074666f, 0x20295228, 0x4c534c48, 0x61685320, 0x20726564,
    0x706d6f43, 0x72656c69, 0x332e3920, 0x32392e30, 0x312e3030, 0x34383336,
    0xababab00, 0x4e475349, 0x00000068, 0x00000003, 0x00000008, 0x00000050,
    0x00000000, 0x00000000, 0x00000003, 0x00000000, 0x00000707, 0x00000059,
    0x00000000, 0x00000000, 0x00000003, 0x00000001, 0x00000303, 0x00000062,
    0x00000000, 0x00000000, 0x00000003, 0x00000002, 0x00000f0f, 0x49534f50,
    0x4e4f4954, 0x58455400, 0x524f4f43, 0x4f430044, 0x00524f4c, 0x4e47534f,
    0x0000006c, 0x00000003, 0x00000008, 0x00000050, 0x00000000, 0x00000001,
    0x00000003, 0x00000000, 0x0000000f, 0x0000005c, 0x00000000, 0x00000000,
    0x00000003, 0x00000001, 0x00000c03, 0x00000065, 0x00000000, 0x00000000,
    0x00000003, 0x00000002, 0x0000000f, 0x505f5653, 0x5449534f, 0x004e4f49,
    0x43584554, 0x44524f4f, 0x4c4f4300, 0xab00524f
};
#else
#error "An appropriate vertex shader is not defined."
#endif

static struct
{
    const void *shader_data;
    SIZE_T shader_size;
} D3D11_shaders[] = {
    { D3D11_PixelShader_Colors, sizeof(D3D11_PixelShader_Colors) },
    { D3D11_PixelShader_Textures, sizeof(D3D11_PixelShader_Textures) },
    { D3D11_PixelShader_YUV_JPEG, sizeof(D3D11_PixelShader_YUV_JPEG) },
    { D3D11_PixelShader_YUV_BT601, sizeof(D3D11_PixelShader_YUV_BT601) },
    { D3D11_PixelShader_YUV_BT709, sizeof(D3D11_PixelShader_YUV_BT709) },
    { D3D11_PixelShader_NV12_JPEG, sizeof(D3D11_PixelShader_NV12_JPEG) },
    { D3D11_PixelShader_NV12_BT601, sizeof(D3D11_PixelShader_NV12_BT601) },
    { D3D11_PixelShader_NV12_BT709, sizeof(D3D11_PixelShader_NV12_BT709) },
    { D3D11_PixelShader_NV21_JPEG, sizeof(D3D11_PixelShader_NV21_JPEG) },
    { D3D11_PixelShader_NV21_BT601, sizeof(D3D11_PixelShader_NV21_BT601) },
    { D3D11_PixelShader_NV21_BT709, sizeof(D3D11_PixelShader_NV21_BT709) },
};

int D3D11_CreateVertexShader(ID3D11Device1 *d3dDevice, ID3D11VertexShader **vertexShader, ID3D11InputLayout **inputLayout)
{
    /* Declare how the input layout for SDL's vertex shader will be setup: */
    const D3D11_INPUT_ELEMENT_DESC vertexDesc[] = 
    {
        { "POSITION", 0, DXGI_FORMAT_R32G32B32_FLOAT, 0, 0, D3D11_INPUT_PER_VERTEX_DATA, 0 },
        { "TEXCOORD", 0, DXGI_FORMAT_R32G32_FLOAT, 0, 12, D3D11_INPUT_PER_VERTEX_DATA, 0 },
        { "COLOR", 0, DXGI_FORMAT_R32G32B32A32_FLOAT, 0, 20, D3D11_INPUT_PER_VERTEX_DATA, 0 },
    };
    HRESULT result;

    /* Load in SDL's one and only vertex shader: */
    result = ID3D11Device_CreateVertexShader(d3dDevice,
        D3D11_VertexShader,
        sizeof(D3D11_VertexShader),
        NULL,
        vertexShader
        );
    if (FAILED(result)) {
        return WIN_SetErrorFromHRESULT(SDL_COMPOSE_ERROR("ID3D11Device1::CreateVertexShader"), result);
    }

    /* Create an input layout for SDL's vertex shader: */
    result = ID3D11Device_CreateInputLayout(d3dDevice,
        vertexDesc,
        ARRAYSIZE(vertexDesc),
        D3D11_VertexShader,
        sizeof(D3D11_VertexShader),
        inputLayout
        );
    if (FAILED(result)) {
        return WIN_SetErrorFromHRESULT(SDL_COMPOSE_ERROR("ID3D11Device1::CreateInputLayout"), result);
    }
    return 0;
}

int D3D11_CreatePixelShader(ID3D11Device1 *d3dDevice, D3D11_Shader shader, ID3D11PixelShader **pixelShader)
{
    HRESULT result;

    result = ID3D11Device_CreatePixelShader(d3dDevice,
        D3D11_shaders[shader].shader_data,
        D3D11_shaders[shader].shader_size,
        NULL,
        pixelShader
        );
    if (FAILED(result)) {
        return WIN_SetErrorFromHRESULT(SDL_COMPOSE_ERROR("ID3D11Device1::CreatePixelShader"), result);
    }
    return 0;
}

#endif /* SDL_VIDEO_RENDER_D3D11 && !SDL_RENDER_DISABLED */

/* vi: set ts=4 sw=4 expandtab: */
