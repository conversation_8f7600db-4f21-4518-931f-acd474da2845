﻿<?xml version="1.0" encoding="utf-8"?>
<Package xmlns="http://schemas.microsoft.com/appx/2010/manifest">

  <Identity Name="ca3178f1-b2b2-43bf-97dd-28ee1b7d32c5"
            Publisher="CN=David"
            Version="1.0.0.0" />

  <Properties>
    <DisplayName>testthread_VS2012_WinRT</DisplayName>
    <PublisherDisplayName>David</PublisherDisplayName>
    <Logo>Assets\StoreLogo.png</Logo>
  </Properties>

  <Prerequisites>
    <OSMinVersion>6.2.1</OSMinVersion>
    <OSMaxVersionTested>6.2.1</OSMaxVersionTested>
  </Prerequisites>

  <Resources>
    <Resource Language="x-generate"/>
  </Resources>

  <Applications>
    <Application Id="App"
        Executable="$targetnametoken$.exe"
        EntryPoint="testthread_VS2012_WinRT.App">
        <VisualElements
            DisplayName="testthread_VS2012_WinRT"
            Logo="Assets\Logo.png"
            SmallLogo="Assets\SmallLogo.png"
            Description="testthread_VS2012_WinRT"
            ForegroundText="light"
            BackgroundColor="#464646">
            <DefaultTile ShowName="allLogos" />
            <SplashScreen Image="Assets\SplashScreen.png" />
        </VisualElements>
    </Application>
  </Applications>
  <Capabilities>
    <Capability Name="internetClient" />
  </Capabilities>
</Package>