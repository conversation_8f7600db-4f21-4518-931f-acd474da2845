﻿<?xml version="1.0" encoding="utf-8"?>
<Package xmlns="http://schemas.microsoft.com/appx/2010/manifest">

  <Identity Name="1702c480-cf09-431e-a5e4-b21b283ec818"
            Publisher="CN=David"
            Version="1.0.0.0" />

  <Properties>
    <DisplayName>loopwave_VS2012_WinRT</DisplayName>
    <PublisherDisplayName>David</PublisherDisplayName>
    <Logo>Assets\StoreLogo.png</Logo>
  </Properties>

  <Prerequisites>
    <OSMinVersion>6.2.1</OSMinVersion>
    <OSMaxVersionTested>6.2.1</OSMaxVersionTested>
  </Prerequisites>

  <Resources>
    <Resource Language="x-generate"/>
  </Resources>

  <Applications>
    <Application Id="App"
        Executable="$targetnametoken$.exe"
        EntryPoint="loopwave_VS2012_WinRT.App">
        <VisualElements
            DisplayName="loopwave_VS2012_WinRT"
            Logo="Assets\Logo.png"
            SmallLogo="Assets\SmallLogo.png"
            Description="loopwave_VS2012_WinRT"
            ForegroundText="light"
            BackgroundColor="#464646">
            <DefaultTile ShowName="allLogos" />
            <SplashScreen Image="Assets\SplashScreen.png" />
        </VisualElements>
    </Application>
  </Applications>
  <Capabilities>
    <Capability Name="internetClient" />
  </Capabilities>
</Package>