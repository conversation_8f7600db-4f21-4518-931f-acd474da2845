﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{d54aa32d-ba0b-491f-ac04-c9b87dd4bc69}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{6945cfdb-2dd6-4f78-bbcb-f899c2a86e4a}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\include\begin_code.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\close_code.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_assert.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_atomic.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_audio.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_blendmode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_clipboard.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_config.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_config_minimal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_config_winrt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_copying.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_cpuinfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_egl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_endian.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_error.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_events.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_filesystem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_haptic.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_hints.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_input.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_joystick.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_keyboard.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_keycode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_loadso.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_log.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_main.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_mouse.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_mutex.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_name.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_opengles2.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_pixels.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_platform.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_power.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_quit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_rect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_render.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_revision.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_rwops.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_scancode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_shape.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_stdinc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_surface.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_system.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_syswm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_thread.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_timer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_touch.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_types.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_version.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_video.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\joystick\SDL_gamecontrollerdb.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\audio\disk\SDL_diskaudio.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\audio\dummy\SDL_dummyaudio.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\audio\SDL_audiodev_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\audio\SDL_audio_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\audio\SDL_sysaudio.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\audio\SDL_wave.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\audio\wasapi\SDL_wasapi.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\core\windows\SDL_windows.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\core\winrt\SDL_winrtapp_common.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\core\winrt\SDL_winrtapp_direct3d.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\core\winrt\SDL_winrtapp_xaml.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\dynapi\SDL_dynapi.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\dynapi\SDL_dynapi_overrides.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\dynapi\SDL_dynapi_procs.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\events\blank_cursor.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\events\default_cursor.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\events\SDL_clipboardevents_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\events\SDL_dropevents_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\events\SDL_events_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\events\SDL_keyboard_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\events\SDL_mouse_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\events\SDL_sysevents.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\events\SDL_touch_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\events\SDL_windowevents_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\haptic\SDL_haptic_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\haptic\SDL_syshaptic.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\joystick\SDL_joystick_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\joystick\SDL_sysjoystick.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\render\direct3d11\SDL_render_winrt.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\render\opengles2\SDL_gles2funcs.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\render\opengles2\SDL_shaders_gles2.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\render\SDL_d3dmath.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\render\SDL_sysrender.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\render\SDL_yuv_sw_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\render\software\SDL_blendfillrect.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\render\software\SDL_blendline.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\render\software\SDL_blendpoint.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\render\software\SDL_draw.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\render\software\SDL_drawline.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\render\software\SDL_drawpoint.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\render\software\SDL_render_sw_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\render\software\SDL_rotate.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\SDL_assert_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\SDL_error_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\SDL_fatal.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\SDL_hints_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\SDL_internal.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\thread\SDL_systhread.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\thread\SDL_thread_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\timer\SDL_timer_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\video\dummy\SDL_nullevents_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\video\dummy\SDL_nullframebuffer_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\video\dummy\SDL_nullvideo.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\video\SDL_blit.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\video\SDL_blit_auto.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\video\SDL_blit_copy.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\video\SDL_blit_slow.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\video\SDL_egl_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\video\SDL_pixels_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\video\SDL_rect_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\video\SDL_RLEaccel_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\video\SDL_shape_internals.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\video\SDL_sysvideo.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\video\winrt\SDL_winrtevents_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\video\winrt\SDL_winrtmessagebox.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\video\winrt\SDL_winrtmouse_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\video\winrt\SDL_winrtopengles.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\video\winrt\SDL_winrtvideo_cpp.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\joystick\windows\SDL_xinputjoystick_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\thread\windows\SDL_systhread_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\video\winrt\SDL_winrtgamebar_cpp.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\SDL_dataqueue.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\video\SDL_yuv_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\video\yuv2rgb\yuv_rgb.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\render\direct3d11\SDL_shaders_d3d11.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\sensor\SDL_sensor_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\sensor\SDL_syssensor.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\sensor\dummy\SDL_dummysensor.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SDL_sensor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\src\events\SDL_displayevents_c.h">
      <Filter>Source Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\src\atomic\SDL_atomic.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\atomic\SDL_spinlock.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\audio\disk\SDL_diskaudio.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\audio\dummy\SDL_dummyaudio.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\audio\SDL_audio.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\audio\SDL_audiocvt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\audio\SDL_audiodev.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\audio\SDL_audiotypecvt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\audio\SDL_mixer.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\audio\SDL_wave.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\audio\wasapi\SDL_wasapi.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\audio\wasapi\SDL_wasapi_winrt.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\core\windows\SDL_windows.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\core\winrt\SDL_winrtapp_common.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\core\winrt\SDL_winrtapp_direct3d.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\core\winrt\SDL_winrtapp_xaml.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\cpuinfo\SDL_cpuinfo.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\dynapi\SDL_dynapi.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\events\SDL_clipboardevents.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\events\SDL_dropevents.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\events\SDL_events.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\events\SDL_gesture.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\events\SDL_keyboard.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\events\SDL_mouse.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\events\SDL_quit.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\events\SDL_touch.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\events\SDL_windowevents.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\filesystem\winrt\SDL_sysfilesystem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\file\SDL_rwops.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\haptic\dummy\SDL_syshaptic.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\haptic\SDL_haptic.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\joystick\dummy\SDL_sysjoystick.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\joystick\SDL_gamecontroller.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\joystick\SDL_joystick.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\loadso\windows\SDL_sysloadso.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\power\SDL_power.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\power\winrt\SDL_syspower.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\render\direct3d11\SDL_render_d3d11.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\render\direct3d11\SDL_render_winrt.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\render\opengles2\SDL_render_gles2.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\render\opengles2\SDL_shaders_gles2.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\render\SDL_d3dmath.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\render\SDL_render.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\render\SDL_yuv_sw.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\render\software\SDL_blendfillrect.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\render\software\SDL_blendline.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\render\software\SDL_blendpoint.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\render\software\SDL_drawline.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\render\software\SDL_drawpoint.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\render\software\SDL_render_sw.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\render\software\SDL_rotate.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\SDL.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\SDL_assert.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\SDL_error.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\SDL_hints.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\SDL_log.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\stdlib\SDL_getenv.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\stdlib\SDL_iconv.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\stdlib\SDL_malloc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\stdlib\SDL_qsort.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\stdlib\SDL_stdlib.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\stdlib\SDL_string.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\thread\SDL_thread.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\timer\SDL_timer.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\timer\windows\SDL_systimer.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\dummy\SDL_nullevents.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\dummy\SDL_nullframebuffer.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\dummy\SDL_nullvideo.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\SDL_blit.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\SDL_blit_0.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\SDL_blit_1.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\SDL_blit_A.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\SDL_blit_auto.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\SDL_blit_copy.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\SDL_blit_N.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\SDL_blit_slow.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\SDL_bmp.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\SDL_clipboard.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\SDL_egl.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\SDL_fillrect.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\SDL_pixels.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\SDL_rect.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\SDL_RLEaccel.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\SDL_shape.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\SDL_stretch.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\SDL_surface.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\SDL_video.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\winrt\SDL_winrtevents.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\winrt\SDL_winrtkeyboard.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\winrt\SDL_winrtmessagebox.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\winrt\SDL_winrtmouse.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\winrt\SDL_winrtopengles.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\winrt\SDL_winrtpointerinput.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\winrt\SDL_winrtvideo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\joystick\windows\SDL_xinputjoystick.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\thread\windows\SDL_sysmutex.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\thread\windows\SDL_syssem.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\thread\windows\SDL_systhread.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\thread\windows\SDL_systls.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\thread\generic\SDL_syscond.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\winrt\SDL_winrtgamebar.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\SDL_dataqueue.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\SDL_yuv.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\video\yuv2rgb\yuv_rgb.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\render\direct3d11\SDL_shaders_d3d11.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\sensor\SDL_sensor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\sensor\dummy\SDL_dummysensor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\src\events\SDL_displayevents.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
</Project>