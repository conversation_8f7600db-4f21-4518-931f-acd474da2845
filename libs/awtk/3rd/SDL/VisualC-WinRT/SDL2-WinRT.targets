<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup>
    <LibSDL2-DeviceType Condition="'$(TargetPlatformIdentifier)' == 'Windows' and '$(TargetPlatformVersion)' == '8.1'">WinRT81</LibSDL2-DeviceType>
    <LibSDL2-DeviceType Condition="'$(TargetPlatformIdentifier)' == 'Windows Phone' and '$(TargetPlatformVersion)' == '8.0'">WinPhone80</LibSDL2-DeviceType>
    <LibSDL2-DeviceType Condition="'$(TargetPlatformIdentifier)' == 'WindowsPhoneApp' and '$(TargetPlatformVersion)' == '8.1'">WinPhone81</LibSDL2-DeviceType>
    <LibSDL2-DeviceType Condition="'$(TargetPlatformIdentifier)' == 'UAP'">UWP</LibSDL2-DeviceType>

  	<LibSDL2-BinPath>$(MSBuildThisFileDirectory)..\..\bin\$(LibSDL2-DeviceType)\$(Platform)</LibSDL2-BinPath>
  </PropertyGroup>

  <Target Name="LibSDL2-DeviceType-Check" BeforeTargets="ResolveAssemblyReferences">
      <Error Condition="'$(LibSDL2-DeviceType)' == ''" Text="Unable to determine which version of Windows is being built-for" />
  </Target>

  <ItemDefinitionGroup>
    <Link>
      <AdditionalLibraryDirectories>$(LibSDL2-BinPath);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>SDL2.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>

  <ItemDefinitionGroup>
    <ClCompile>
      <AdditionalIncludeDirectories>$(MSBuildThisFileDirectory)..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
  </ItemDefinitionGroup>

  <ItemGroup Label="LibSDL2">
    <PackagingOutputs Include="$(LibSDL2-BinPath)\SDL2.dll">
        <OutputGroup>LibSDL2Binaries</OutputGroup>
        <ProjectName>$(ProjectName)</ProjectName>
        <TargetPath>%(Filename)%(Extension)</TargetPath>
    </PackagingOutputs>
  </ItemGroup>

</Project>
