﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|ARM">
      <Configuration>Debug</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM">
      <Configuration>Release</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\include\begin_code.h" />
    <ClInclude Include="..\..\include\close_code.h" />
    <ClInclude Include="..\..\include\SDL.h" />
    <ClInclude Include="..\..\include\SDL_assert.h" />
    <ClInclude Include="..\..\include\SDL_atomic.h" />
    <ClInclude Include="..\..\include\SDL_audio.h" />
    <ClInclude Include="..\..\include\SDL_blendmode.h" />
    <ClInclude Include="..\..\include\SDL_clipboard.h" />
    <ClInclude Include="..\..\include\SDL_config.h" />
    <ClInclude Include="..\..\include\SDL_config_minimal.h" />
    <ClInclude Include="..\..\include\SDL_config_winrt.h" />
    <ClInclude Include="..\..\include\SDL_copying.h" />
    <ClInclude Include="..\..\include\SDL_cpuinfo.h" />
    <ClInclude Include="..\..\include\SDL_egl.h" />
    <ClInclude Include="..\..\include\SDL_endian.h" />
    <ClInclude Include="..\..\include\SDL_error.h" />
    <ClInclude Include="..\..\include\SDL_events.h" />
    <ClInclude Include="..\..\include\SDL_filesystem.h" />
    <ClInclude Include="..\..\include\SDL_haptic.h" />
    <ClInclude Include="..\..\include\SDL_hints.h" />
    <ClInclude Include="..\..\include\SDL_input.h" />
    <ClInclude Include="..\..\include\SDL_joystick.h" />
    <ClInclude Include="..\..\include\SDL_keyboard.h" />
    <ClInclude Include="..\..\include\SDL_keycode.h" />
    <ClInclude Include="..\..\include\SDL_loadso.h" />
    <ClInclude Include="..\..\include\SDL_log.h" />
    <ClInclude Include="..\..\include\SDL_main.h" />
    <ClInclude Include="..\..\include\SDL_mouse.h" />
    <ClInclude Include="..\..\include\SDL_mutex.h" />
    <ClInclude Include="..\..\include\SDL_name.h" />
    <ClInclude Include="..\..\include\SDL_opengles2.h" />
    <ClInclude Include="..\..\include\SDL_pixels.h" />
    <ClInclude Include="..\..\include\SDL_platform.h" />
    <ClInclude Include="..\..\include\SDL_power.h" />
    <ClInclude Include="..\..\include\SDL_quit.h" />
    <ClInclude Include="..\..\include\SDL_rect.h" />
    <ClInclude Include="..\..\include\SDL_render.h" />
    <ClInclude Include="..\..\include\SDL_revision.h" />
    <ClInclude Include="..\..\include\SDL_rwops.h" />
    <ClInclude Include="..\..\include\SDL_scancode.h" />
    <ClInclude Include="..\..\include\SDL_sensor.h" />
    <ClInclude Include="..\..\include\SDL_shape.h" />
    <ClInclude Include="..\..\include\SDL_stdinc.h" />
    <ClInclude Include="..\..\include\SDL_surface.h" />
    <ClInclude Include="..\..\include\SDL_system.h" />
    <ClInclude Include="..\..\include\SDL_syswm.h" />
    <ClInclude Include="..\..\include\SDL_thread.h" />
    <ClInclude Include="..\..\include\SDL_timer.h" />
    <ClInclude Include="..\..\include\SDL_touch.h" />
    <ClInclude Include="..\..\include\SDL_types.h" />
    <ClInclude Include="..\..\include\SDL_version.h" />
    <ClInclude Include="..\..\include\SDL_video.h" />
    <ClInclude Include="..\..\src\audio\disk\SDL_diskaudio.h" />
    <ClInclude Include="..\..\src\audio\dummy\SDL_dummyaudio.h" />
    <ClInclude Include="..\..\src\audio\SDL_audiodev_c.h" />
    <ClInclude Include="..\..\src\audio\SDL_audio_c.h" />
    <ClInclude Include="..\..\src\audio\SDL_sysaudio.h" />
    <ClInclude Include="..\..\src\audio\SDL_wave.h" />
    <ClInclude Include="..\..\src\audio\wasapi\SDL_wasapi.h" />
    <ClInclude Include="..\..\src\core\windows\SDL_directx.h" />
    <ClInclude Include="..\..\src\core\windows\SDL_windows.h" />
    <ClInclude Include="..\..\src\core\windows\SDL_xinput.h" />
    <ClInclude Include="..\..\src\core\winrt\SDL_winrtapp_common.h" />
    <ClInclude Include="..\..\src\core\winrt\SDL_winrtapp_direct3d.h" />
    <ClInclude Include="..\..\src\core\winrt\SDL_winrtapp_xaml.h" />
    <ClInclude Include="..\..\src\dynapi\SDL_dynapi.h" />
    <ClInclude Include="..\..\src\dynapi\SDL_dynapi_overrides.h" />
    <ClInclude Include="..\..\src\dynapi\SDL_dynapi_procs.h" />
    <ClInclude Include="..\..\src\events\blank_cursor.h" />
    <ClInclude Include="..\..\src\events\default_cursor.h" />
    <ClInclude Include="..\..\src\events\SDL_clipboardevents_c.h" />
    <ClInclude Include="..\..\src\events\SDL_displayevents_c.h" />
    <ClInclude Include="..\..\src\events\SDL_dropevents_c.h" />
    <ClInclude Include="..\..\src\events\SDL_events_c.h" />
    <ClInclude Include="..\..\src\events\SDL_keyboard_c.h" />
    <ClInclude Include="..\..\src\events\SDL_mouse_c.h" />
    <ClInclude Include="..\..\src\events\SDL_sysevents.h" />
    <ClInclude Include="..\..\src\events\SDL_touch_c.h" />
    <ClInclude Include="..\..\src\events\SDL_windowevents_c.h" />
    <ClInclude Include="..\..\src\haptic\SDL_haptic_c.h" />
    <ClInclude Include="..\..\src\haptic\SDL_syshaptic.h" />
    <ClInclude Include="..\..\src\haptic\windows\SDL_dinputhaptic_c.h" />
    <ClInclude Include="..\..\src\haptic\windows\SDL_windowshaptic_c.h" />
    <ClInclude Include="..\..\src\haptic\windows\SDL_xinputhaptic_c.h" />
    <ClInclude Include="..\..\src\joystick\SDL_gamecontrollerdb.h" />
    <ClInclude Include="..\..\src\joystick\SDL_joystick_c.h" />
    <ClInclude Include="..\..\src\joystick\SDL_sysjoystick.h" />
    <ClInclude Include="..\..\src\joystick\windows\SDL_dinputjoystick_c.h" />
    <ClInclude Include="..\..\src\joystick\windows\SDL_windowsjoystick_c.h" />
    <ClInclude Include="..\..\src\joystick\windows\SDL_xinputjoystick_c.h" />
    <ClInclude Include="..\..\src\render\direct3d11\SDL_render_winrt.h" />
    <ClInclude Include="..\..\src\render\direct3d11\SDL_shaders_d3d11.h" />
    <ClInclude Include="..\..\src\render\opengles2\SDL_gles2funcs.h" />
    <ClInclude Include="..\..\src\render\opengles2\SDL_shaders_gles2.h" />
    <ClInclude Include="..\..\src\render\SDL_d3dmath.h" />
    <ClInclude Include="..\..\src\render\SDL_sysrender.h" />
    <ClInclude Include="..\..\src\render\SDL_yuv_sw_c.h" />
    <ClInclude Include="..\..\src\render\software\SDL_blendfillrect.h" />
    <ClInclude Include="..\..\src\render\software\SDL_blendline.h" />
    <ClInclude Include="..\..\src\render\software\SDL_blendpoint.h" />
    <ClInclude Include="..\..\src\render\software\SDL_draw.h" />
    <ClInclude Include="..\..\src\render\software\SDL_drawline.h" />
    <ClInclude Include="..\..\src\render\software\SDL_drawpoint.h" />
    <ClInclude Include="..\..\src\render\software\SDL_render_sw_c.h" />
    <ClInclude Include="..\..\src\render\software\SDL_rotate.h" />
    <ClInclude Include="..\..\src\SDL_assert_c.h" />
    <ClInclude Include="..\..\src\SDL_dataqueue.h" />
    <ClInclude Include="..\..\src\SDL_error_c.h" />
    <ClInclude Include="..\..\src\SDL_fatal.h" />
    <ClInclude Include="..\..\src\SDL_hints_c.h" />
    <ClInclude Include="..\..\src\SDL_internal.h" />
    <ClInclude Include="..\..\src\sensor\dummy\SDL_dummysensor.h" />
    <ClInclude Include="..\..\src\sensor\SDL_sensor_c.h" />
    <ClInclude Include="..\..\src\sensor\SDL_syssensor.h" />
    <ClInclude Include="..\..\src\thread\SDL_thread_c.h" />
    <ClInclude Include="..\..\src\thread\windows\SDL_systhread_c.h" />
    <ClInclude Include="..\..\src\timer\SDL_timer_c.h" />
    <ClInclude Include="..\..\src\video\dummy\SDL_nullevents_c.h" />
    <ClInclude Include="..\..\src\video\dummy\SDL_nullframebuffer_c.h" />
    <ClInclude Include="..\..\src\video\dummy\SDL_nullvideo.h" />
    <ClInclude Include="..\..\src\video\SDL_blit.h" />
    <ClInclude Include="..\..\src\video\SDL_blit_auto.h" />
    <ClInclude Include="..\..\src\video\SDL_blit_copy.h" />
    <ClInclude Include="..\..\src\video\SDL_blit_slow.h" />
    <ClInclude Include="..\..\src\video\SDL_egl_c.h" />
    <ClInclude Include="..\..\src\video\SDL_pixels_c.h" />
    <ClInclude Include="..\..\src\video\SDL_rect_c.h" />
    <ClInclude Include="..\..\src\video\SDL_RLEaccel_c.h" />
    <ClInclude Include="..\..\src\video\SDL_shape_internals.h" />
    <ClInclude Include="..\..\src\video\SDL_sysvideo.h" />
    <ClInclude Include="..\..\src\video\SDL_yuv_c.h" />
    <ClInclude Include="..\..\src\video\winrt\SDL_winrtevents_c.h" />
    <ClInclude Include="..\..\src\video\winrt\SDL_winrtgamebar_cpp.h" />
    <ClInclude Include="..\..\src\video\winrt\SDL_winrtmessagebox.h" />
    <ClInclude Include="..\..\src\video\winrt\SDL_winrtmouse_c.h" />
    <ClInclude Include="..\..\src\video\winrt\SDL_winrtopengles.h" />
    <ClInclude Include="..\..\src\video\winrt\SDL_winrtvideo_cpp.h" />
    <ClInclude Include="..\..\src\video\yuv2rgb\yuv_rgb.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\src\atomic\SDL_atomic.c" />
    <ClCompile Include="..\..\src\atomic\SDL_spinlock.c" />
    <ClCompile Include="..\..\src\audio\disk\SDL_diskaudio.c" />
    <ClCompile Include="..\..\src\audio\dummy\SDL_dummyaudio.c" />
    <ClCompile Include="..\..\src\audio\SDL_audio.c" />
    <ClCompile Include="..\..\src\audio\SDL_audiocvt.c" />
    <ClCompile Include="..\..\src\audio\SDL_audiodev.c" />
    <ClCompile Include="..\..\src\audio\SDL_audiotypecvt.c" />
    <ClCompile Include="..\..\src\audio\SDL_mixer.c" />
    <ClCompile Include="..\..\src\audio\SDL_wave.c" />
    <ClCompile Include="..\..\src\audio\wasapi\SDL_wasapi.c" />
    <ClCompile Include="..\..\src\audio\wasapi\SDL_wasapi_winrt.cpp">
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</CompileAsWinRT>
    </ClCompile>
    <ClCompile Include="..\..\src\core\windows\SDL_windows.c" />
    <ClCompile Include="..\..\src\core\windows\SDL_xinput.c" />
    <ClCompile Include="..\..\src\core\winrt\SDL_winrtapp_common.cpp">
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</CompileAsWinRT>
    </ClCompile>
    <ClCompile Include="..\..\src\core\winrt\SDL_winrtapp_direct3d.cpp">
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</CompileAsWinRT>
    </ClCompile>
    <ClCompile Include="..\..\src\core\winrt\SDL_winrtapp_xaml.cpp">
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</CompileAsWinRT>
    </ClCompile>
    <ClCompile Include="..\..\src\cpuinfo\SDL_cpuinfo.c" />
    <ClCompile Include="..\..\src\dynapi\SDL_dynapi.c" />
    <ClCompile Include="..\..\src\events\SDL_clipboardevents.c" />
    <ClCompile Include="..\..\src\events\SDL_displayevents.c" />
    <ClCompile Include="..\..\src\events\SDL_dropevents.c" />
    <ClCompile Include="..\..\src\events\SDL_events.c" />
    <ClCompile Include="..\..\src\events\SDL_gesture.c" />
    <ClCompile Include="..\..\src\events\SDL_keyboard.c" />
    <ClCompile Include="..\..\src\events\SDL_mouse.c" />
    <ClCompile Include="..\..\src\events\SDL_quit.c" />
    <ClCompile Include="..\..\src\events\SDL_touch.c" />
    <ClCompile Include="..\..\src\events\SDL_windowevents.c" />
    <ClCompile Include="..\..\src\filesystem\winrt\SDL_sysfilesystem.cpp">
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</CompileAsWinRT>
    </ClCompile>
    <ClCompile Include="..\..\src\file\SDL_rwops.c" />
    <ClCompile Include="..\..\src\haptic\dummy\SDL_syshaptic.c" />
    <ClCompile Include="..\..\src\haptic\SDL_haptic.c" />
    <ClCompile Include="..\..\src\haptic\windows\SDL_dinputhaptic.c" />
    <ClCompile Include="..\..\src\haptic\windows\SDL_windowshaptic.c" />
    <ClCompile Include="..\..\src\haptic\windows\SDL_xinputhaptic.c" />
    <ClCompile Include="..\..\src\joystick\dummy\SDL_sysjoystick.c" />
    <ClCompile Include="..\..\src\joystick\SDL_gamecontroller.c" />
    <ClCompile Include="..\..\src\joystick\SDL_joystick.c" />
    <ClCompile Include="..\..\src\joystick\windows\SDL_dinputjoystick.c" />
    <ClCompile Include="..\..\src\joystick\windows\SDL_windowsjoystick.c" />
    <ClCompile Include="..\..\src\joystick\windows\SDL_xinputjoystick.c" />
    <ClCompile Include="..\..\src\loadso\windows\SDL_sysloadso.c" />
    <ClCompile Include="..\..\src\power\SDL_power.c" />
    <ClCompile Include="..\..\src\power\winrt\SDL_syspower.cpp" />
    <ClCompile Include="..\..\src\render\direct3d11\SDL_render_d3d11.c" />
    <ClCompile Include="..\..\src\render\direct3d11\SDL_render_winrt.cpp">
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</CompileAsWinRT>
    </ClCompile>
    <ClCompile Include="..\..\src\render\direct3d11\SDL_shaders_d3d11.c" />
    <ClCompile Include="..\..\src\render\opengles2\SDL_render_gles2.c" />
    <ClCompile Include="..\..\src\render\opengles2\SDL_shaders_gles2.c" />
    <ClCompile Include="..\..\src\render\SDL_d3dmath.c" />
    <ClCompile Include="..\..\src\render\SDL_render.c" />
    <ClCompile Include="..\..\src\render\SDL_yuv_sw.c" />
    <ClCompile Include="..\..\src\render\software\SDL_blendfillrect.c" />
    <ClCompile Include="..\..\src\render\software\SDL_blendline.c" />
    <ClCompile Include="..\..\src\render\software\SDL_blendpoint.c" />
    <ClCompile Include="..\..\src\render\software\SDL_drawline.c" />
    <ClCompile Include="..\..\src\render\software\SDL_drawpoint.c" />
    <ClCompile Include="..\..\src\render\software\SDL_render_sw.c" />
    <ClCompile Include="..\..\src\render\software\SDL_rotate.c" />
    <ClCompile Include="..\..\src\SDL.c" />
    <ClCompile Include="..\..\src\SDL_assert.c" />
    <ClCompile Include="..\..\src\SDL_dataqueue.c" />
    <ClCompile Include="..\..\src\SDL_error.c" />
    <ClCompile Include="..\..\src\SDL_hints.c" />
    <ClCompile Include="..\..\src\SDL_log.c" />
    <ClCompile Include="..\..\src\sensor\dummy\SDL_dummysensor.c" />
    <ClCompile Include="..\..\src\sensor\SDL_sensor.c" />
    <ClCompile Include="..\..\src\stdlib\SDL_getenv.c" />
    <ClCompile Include="..\..\src\stdlib\SDL_iconv.c" />
    <ClCompile Include="..\..\src\stdlib\SDL_malloc.c" />
    <ClCompile Include="..\..\src\stdlib\SDL_qsort.c" />
    <ClCompile Include="..\..\src\stdlib\SDL_stdlib.c" />
    <ClCompile Include="..\..\src\stdlib\SDL_string.c" />
    <ClCompile Include="..\..\src\thread\generic\SDL_syscond.c" />
    <ClCompile Include="..\..\src\thread\SDL_thread.c" />
    <ClCompile Include="..\..\src\thread\windows\SDL_sysmutex.c" />
    <ClCompile Include="..\..\src\thread\windows\SDL_syssem.c" />
    <ClCompile Include="..\..\src\thread\windows\SDL_systhread.c" />
    <ClCompile Include="..\..\src\thread\windows\SDL_systls.c" />
    <ClCompile Include="..\..\src\timer\SDL_timer.c" />
    <ClCompile Include="..\..\src\timer\windows\SDL_systimer.c" />
    <ClCompile Include="..\..\src\video\dummy\SDL_nullevents.c" />
    <ClCompile Include="..\..\src\video\dummy\SDL_nullframebuffer.c" />
    <ClCompile Include="..\..\src\video\dummy\SDL_nullvideo.c" />
    <ClCompile Include="..\..\src\video\SDL_blit.c" />
    <ClCompile Include="..\..\src\video\SDL_blit_0.c" />
    <ClCompile Include="..\..\src\video\SDL_blit_1.c" />
    <ClCompile Include="..\..\src\video\SDL_blit_A.c" />
    <ClCompile Include="..\..\src\video\SDL_blit_auto.c" />
    <ClCompile Include="..\..\src\video\SDL_blit_copy.c" />
    <ClCompile Include="..\..\src\video\SDL_blit_N.c" />
    <ClCompile Include="..\..\src\video\SDL_blit_slow.c" />
    <ClCompile Include="..\..\src\video\SDL_bmp.c" />
    <ClCompile Include="..\..\src\video\SDL_clipboard.c" />
    <ClCompile Include="..\..\src\video\SDL_egl.c" />
    <ClCompile Include="..\..\src\video\SDL_fillrect.c" />
    <ClCompile Include="..\..\src\video\SDL_pixels.c" />
    <ClCompile Include="..\..\src\video\SDL_rect.c" />
    <ClCompile Include="..\..\src\video\SDL_RLEaccel.c" />
    <ClCompile Include="..\..\src\video\SDL_shape.c" />
    <ClCompile Include="..\..\src\video\SDL_stretch.c" />
    <ClCompile Include="..\..\src\video\SDL_surface.c" />
    <ClCompile Include="..\..\src\video\SDL_video.c" />
    <ClCompile Include="..\..\src\video\SDL_yuv.c" />
    <ClCompile Include="..\..\src\video\winrt\SDL_winrtevents.cpp">
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</CompileAsWinRT>
    </ClCompile>
    <ClCompile Include="..\..\src\video\winrt\SDL_winrtgamebar.cpp">
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</CompileAsWinRT>
    </ClCompile>
    <ClCompile Include="..\..\src\video\winrt\SDL_winrtkeyboard.cpp">
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</CompileAsWinRT>
    </ClCompile>
    <ClCompile Include="..\..\src\video\winrt\SDL_winrtmessagebox.cpp">
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</CompileAsWinRT>
    </ClCompile>
    <ClCompile Include="..\..\src\video\winrt\SDL_winrtmouse.cpp">
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</CompileAsWinRT>
    </ClCompile>
    <ClCompile Include="..\..\src\video\winrt\SDL_winrtopengles.cpp">
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</CompileAsWinRT>
    </ClCompile>
    <ClCompile Include="..\..\src\video\winrt\SDL_winrtpointerinput.cpp">
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</CompileAsWinRT>
    </ClCompile>
    <ClCompile Include="..\..\src\video\winrt\SDL_winrtvideo.cpp">
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">true</CompileAsWinRT>
      <CompileAsWinRT Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</CompileAsWinRT>
    </ClCompile>
    <ClCompile Include="..\..\src\video\yuv2rgb\yuv_rgb.c" />
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{c8df6173-06a1-4f56-a9bc-2002596b30e9}</ProjectGuid>
    <RootNamespace>SDL_WinRT81</RootNamespace>
    <DefaultLanguage>en-US</DefaultLanguage>
    <MinimumVisualStudioVersion>12.0</MinimumVisualStudioVersion>
    <AppContainerApplication>true</AppContainerApplication>
    <ApplicationType>Windows Store</ApplicationType>
    <ApplicationTypeRevision>8.1</ApplicationTypeRevision>
    <ConvergedProjectType>CodeSharingDll</ConvergedProjectType>
    <ProjectName>SDL2-WinRT81</ProjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <GenerateManifest>false</GenerateManifest>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
    <IntDir>Objs\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</IntDir>
    <TargetName>SDL2</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <GenerateManifest>false</GenerateManifest>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
    <IntDir>Objs\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</IntDir>
    <TargetName>SDL2</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">
    <GenerateManifest>false</GenerateManifest>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
    <IntDir>Objs\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</IntDir>
    <TargetName>SDL2</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">
    <GenerateManifest>false</GenerateManifest>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
    <IntDir>Objs\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</IntDir>
    <TargetName>SDL2</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <GenerateManifest>false</GenerateManifest>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
    <IntDir>Objs\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</IntDir>
    <TargetName>SDL2</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <GenerateManifest>false</GenerateManifest>
    <IgnoreImportLibrary>false</IgnoreImportLibrary>
    <IntDir>Objs\$(Platform)\$(Configuration)\$(MSBuildProjectName)\</IntDir>
    <TargetName>SDL2</TargetName>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <CompileAsWinRT>false</CompileAsWinRT>
      <AdditionalIncludeDirectories>..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_WINDLL;_CRT_SECURE_NO_WARNINGS;SDL_BUILDING_WINRT=1;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
      <AdditionalDependencies>xinput.lib;mmdevapi.lib;d2d1.lib;d3d11.lib;dxgi.lib;ole32.lib;windowscodecs.lib;dwrite.lib;kernel32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <CompileAsWinRT>false</CompileAsWinRT>
      <AdditionalIncludeDirectories>..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_WINDLL;_CRT_SECURE_NO_WARNINGS;SDL_BUILDING_WINRT=1;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
      <AdditionalDependencies>xinput.lib;mmdevapi.lib;d2d1.lib;d3d11.lib;dxgi.lib;ole32.lib;windowscodecs.lib;dwrite.lib;kernel32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|arm'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <CompileAsWinRT>false</CompileAsWinRT>
      <AdditionalIncludeDirectories>..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_WINDLL;_CRT_SECURE_NO_WARNINGS;SDL_BUILDING_WINRT=1;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
      <AdditionalDependencies>xinput.lib;mmdevapi.lib;d2d1.lib;d3d11.lib;dxgi.lib;ole32.lib;windowscodecs.lib;dwrite.lib;kernel32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|arm'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <CompileAsWinRT>false</CompileAsWinRT>
      <AdditionalIncludeDirectories>..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_WINDLL;_CRT_SECURE_NO_WARNINGS;SDL_BUILDING_WINRT=1;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
      <AdditionalDependencies>xinput.lib;mmdevapi.lib;d2d1.lib;d3d11.lib;dxgi.lib;ole32.lib;windowscodecs.lib;dwrite.lib;kernel32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <CompileAsWinRT>false</CompileAsWinRT>
      <AdditionalIncludeDirectories>..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_WINDLL;_CRT_SECURE_NO_WARNINGS;SDL_BUILDING_WINRT=1;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
      <AdditionalDependencies>xinput.lib;mmdevapi.lib;d2d1.lib;d3d11.lib;dxgi.lib;ole32.lib;windowscodecs.lib;dwrite.lib;kernel32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <CompileAsWinRT>false</CompileAsWinRT>
      <AdditionalIncludeDirectories>..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_WINDLL;_CRT_SECURE_NO_WARNINGS;SDL_BUILDING_WINRT=1;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateWindowsMetadata>false</GenerateWindowsMetadata>
      <AdditionalDependencies>xinput.lib;mmdevapi.lib;d2d1.lib;d3d11.lib;dxgi.lib;ole32.lib;windowscodecs.lib;dwrite.lib;kernel32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>