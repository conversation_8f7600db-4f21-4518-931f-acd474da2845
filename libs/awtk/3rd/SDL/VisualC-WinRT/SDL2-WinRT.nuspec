<?xml version="1.0"?>
<package >
  <metadata>
    <id>SDL2-WinRT</id>
    <version>2.0.4-Unofficial</version>
    <authors><PERSON></authors>
    <owners><PERSON></owners>
    <licenseUrl>http://libsdl.org/license.php</licenseUrl>
    <projectUrl>http://libsdl.org</projectUrl>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <description>Unofficial pre-release of LibSDL2, built for WinRT platforms</description>
    <copyright>Copyright 2015</copyright>
    <tags>SDL2 SDL LibSDL OpenGL C C++ nativepackage</tags>
  </metadata>
  <files>
    <file src="lib\**\*.dll" target="bin"/>
    <file src="lib\**\*.lib" target="bin"/>
    <file src="lib\**\*.pdb" target="bin"/>
    <file src="..\include\**\*.*" target="include"/>
    <file src="..\src\**\*.*" target="src"/>
    <file src="SDL2-WinRT.targets" target="build\native"/>
  </files>
</package>
