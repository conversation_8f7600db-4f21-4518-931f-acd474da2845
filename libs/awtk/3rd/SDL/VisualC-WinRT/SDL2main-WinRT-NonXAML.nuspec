<?xml version="1.0"?>
<package >
  <metadata>
    <id>SDL2main-WinRT-NonXAML</id>
    <version>2.0.4-Unofficial</version>
    <authors><PERSON></authors>
    <owners><PERSON></owners>
    <licenseUrl>http://libsdl.org/license.php</licenseUrl>
    <projectUrl>http://libsdl.org</projectUrl>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <description>WinMain() function for SDL2 + WinRT + CoreWindow (non-XAML) apps</description>
    <copyright>Copyright 2015</copyright>
    <tags>SDL2 SDL LibSDL OpenGL C C++ nativepackage</tags>
    <dependencies>
      <dependency id="SDL2-WinRT" version="2.0.4"/>
    </dependencies>
  </metadata>
  <files>
    <file src="..\src\main\winrt\SDL_winrt_main_NonXAML.cpp" target="src\main\winrt"/>
    <file src="SDL2main-WinRT-NonXAML.targets" target="build\native"/>
  </files>
</package>
