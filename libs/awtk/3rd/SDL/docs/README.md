Simple DirectMedia Layer {#mainpage}
========================

                                  (SDL)

                                Version 2.0

---
http://www.libsdl.org/

Simple DirectMedia Layer is a cross-platform development library designed
to provide low level access to audio, keyboard, mouse, joystick, and graphics
hardware via OpenGL and Direct3D. It is used by video playback software,
emulators, and popular games including Valve's award winning catalog
and many Humble Bundle games.

SDL officially supports Windows, Mac OS X, Linux, iOS, and Android.
Support for other platforms may be found in the source code.

SDL is written in C, works natively with C++, and there are bindings 
available for several other languages, including C# and Python.

This library is distributed under the zlib license, which can be found
in the file "COPYING.txt".

The best way to learn how to use SDL is to check out the header files in
the "include" subdirectory and the programs in the "test" subdirectory.
The header files and test programs are well commented and always up to date.

More documentation and FAQs are available online at [the wiki](http://wiki.libsdl.org/)

- [Android](README-android.md)
- [CMake](README-cmake.md)
- [DirectFB](README-directfb.md)
- [DynAPI](README-dynapi.md)
- [Emscripten](README-emscripten.md)
- [Gesture](README-gesture.md)
- [Mercurial](README-hg.md)
- [iOS](README-ios.md)
- [Linux](README-linux.md)
- [OS X](README-macosx.md)
- [Native Client](README-nacl.md)
- [Pandora](README-pandora.md)
- [Supported Platforms](README-platforms.md)
- [Porting information](README-porting.md)
- [PSP](README-psp.md)
- [Raspberry Pi](README-raspberrypi.md)
- [Touch](README-touch.md)
- [WinCE](README-wince.md)
- [Windows](README-windows.md)
- [WinRT](README-winrt.md)

If you need help with the library, or just want to discuss SDL related
issues, you can join the [developers mailing list](http://www.libsdl.org/mailing-list.php)

If you want to report bugs or contribute patches, please submit them to
[bugzilla](https://bugzilla.libsdl.org/)

Enjoy!


Sam Lantinga <mailto:<EMAIL>>

