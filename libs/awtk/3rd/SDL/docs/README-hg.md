Mercurial
=========

The latest development version of SDL is available via Mercurial.
Mercurial allows you to get up-to-the-minute fixes and enhancements;
as a developer works on a source tree, you can use "hg" to mirror that
source tree instead of waiting for an official release. Please look
at the Mercurial website ( https://www.mercurial-scm.org/ ) for more
information on using hg, where you can also download software for
Mac OS X, Windows, and Unix systems.

    hg clone http://hg.libsdl.org/SDL

If you are building SDL via configure, you will need to run autogen.sh
before running configure.

There is a web interface to the subversion repository at:
	http://hg.libsdl.org/SDL/

There is an RSS feed available at that URL, for those that want to
track commits in real time.

