// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		1D3623EC0D0F72F000981E51 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1D3623EB0D0F72F000981E51 /* CoreGraphics.framework */; };
		1D60589F0D05DD5A006BFB54 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1D30AB110D05D00D00671497 /* Foundation.framework */; };
		1DF5F4E00D08C38300B7A737 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1DF5F4DF0D08C38300B7A737 /* UIKit.framework */; };
		FA30DEB01BBF5A8F009C397F /* common.c in Sources */ = {isa = PBXBuildFile; fileRef = FD77A0060E26BC0500F39101 /* common.c */; };
		FA30DEB11BBF5A93009C397F /* happy.c in Sources */ = {isa = PBXBuildFile; fileRef = FD77A0080E26BC0500F39101 /* happy.c */; };
		FA30DEB31BBF5AD7009C397F /* icon.bmp in Resources */ = {isa = PBXBuildFile; fileRef = FDB651CC0E43D19800F688B5 /* icon.bmp */; };
		FA30DEB41BBF5ADD009C397F /* Icon.png in Resources */ = {isa = PBXBuildFile; fileRef = FD925B180E0F276600E92347 /* Icon.png */; };
		FA30DEB61BBF5AE6009C397F /* Default.png in Resources */ = {isa = PBXBuildFile; fileRef = FD787AA00E22A5CC003E8E36 /* Default.png */; };
		FA30DEB71BBF5BB8009C397F /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = FA30DEAC1BBF59D9009C397F /* libSDL2.a */; };
		FA30DEC81BBF5C14009C397F /* GameController.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FAE0E9691BAF96A00098DFA4 /* GameController.framework */; };
		FA30DEC91BBF5C14009C397F /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDF0D7220E12D31800247964 /* AudioToolbox.framework */; };
		FA30DECA1BBF5C14009C397F /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDB96EDF0DEFC9DC00FAF19F /* QuartzCore.framework */; };
		FA30DECB1BBF5C14009C397F /* OpenGLES.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDB96ED30DEFC9C700FAF19F /* OpenGLES.framework */; };
		FA30DECC1BBF5C14009C397F /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1D3623EB0D0F72F000981E51 /* CoreGraphics.framework */; };
		FA30DECD1BBF5C14009C397F /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1DF5F4DF0D08C38300B7A737 /* UIKit.framework */; };
		FA30DECE1BBF5C14009C397F /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1D30AB110D05D00D00671497 /* Foundation.framework */; };
		FA30DECF1BBF5C14009C397F /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDF0D71D0E12D2AB00247964 /* CoreAudio.framework */; };
		FA86C0371D9765BB009CB637 /* iOS Launch Screen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = FA86C0361D9765BA009CB637 /* iOS Launch Screen.storyboard */; };
		FA86C0381D9765BB009CB637 /* iOS Launch Screen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = FA86C0361D9765BA009CB637 /* iOS Launch Screen.storyboard */; };
		FA86C0391D9765BB009CB637 /* iOS Launch Screen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = FA86C0361D9765BA009CB637 /* iOS Launch Screen.storyboard */; };
		FA86C03A1D9765BB009CB637 /* iOS Launch Screen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = FA86C0361D9765BA009CB637 /* iOS Launch Screen.storyboard */; };
		FA86C03B1D9765BB009CB637 /* iOS Launch Screen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = FA86C0361D9765BA009CB637 /* iOS Launch Screen.storyboard */; };
		FA86C03C1D9765BB009CB637 /* iOS Launch Screen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = FA86C0361D9765BA009CB637 /* iOS Launch Screen.storyboard */; };
		FA86C03D1D9765BB009CB637 /* iOS Launch Screen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = FA86C0361D9765BA009CB637 /* iOS Launch Screen.storyboard */; };
		FA8B4BA31967070A00F8EB7C /* CoreMotion.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA8B4BA21967070A00F8EB7C /* CoreMotion.framework */; };
		FA8B4BA41967071300F8EB7C /* CoreMotion.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA8B4BA21967070A00F8EB7C /* CoreMotion.framework */; };
		FA8B4BA51967071A00F8EB7C /* CoreMotion.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA8B4BA21967070A00F8EB7C /* CoreMotion.framework */; };
		FA8B4BA61967072100F8EB7C /* CoreMotion.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA8B4BA21967070A00F8EB7C /* CoreMotion.framework */; };
		FA8B4BA71967072800F8EB7C /* CoreMotion.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA8B4BA21967070A00F8EB7C /* CoreMotion.framework */; };
		FA8B4BA81967073400F8EB7C /* CoreMotion.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA8B4BA21967070A00F8EB7C /* CoreMotion.framework */; };
		FA8B4BA91967073D00F8EB7C /* CoreMotion.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA8B4BA21967070A00F8EB7C /* CoreMotion.framework */; };
		FABA34D41D8B5E5600915323 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FABA34D31D8B5E5600915323 /* AVFoundation.framework */; };
		FABA34D61D8B5E5A00915323 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FABA34D31D8B5E5600915323 /* AVFoundation.framework */; };
		FABA34D81D8B5E7700915323 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FABA34D71D8B5E7700915323 /* AVFoundation.framework */; };
		FABA34D91D8B5E7B00915323 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FABA34D31D8B5E5600915323 /* AVFoundation.framework */; };
		FABA34DA1D8B5E7F00915323 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FABA34D31D8B5E5600915323 /* AVFoundation.framework */; };
		FABA34DB1D8B5E8500915323 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FABA34D31D8B5E5600915323 /* AVFoundation.framework */; };
		FABA34DC1D8B5E8900915323 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FABA34D31D8B5E5600915323 /* AVFoundation.framework */; };
		FABA34DD1D8B5E8D00915323 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FABA34D31D8B5E5600915323 /* AVFoundation.framework */; };
		FAE0E96A1BAF96A00098DFA4 /* GameController.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FAE0E9691BAF96A00098DFA4 /* GameController.framework */; };
		FAE0E96C1BAF96A90098DFA4 /* GameController.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FAE0E9691BAF96A00098DFA4 /* GameController.framework */; };
		FAE0E96D1BAF96AF0098DFA4 /* GameController.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FAE0E9691BAF96A00098DFA4 /* GameController.framework */; };
		FAE0E96E1BAF96B10098DFA4 /* GameController.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FAE0E9691BAF96A00098DFA4 /* GameController.framework */; };
		FAE0E96F1BAF96B50098DFA4 /* GameController.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FAE0E9691BAF96A00098DFA4 /* GameController.framework */; };
		FAE0E9701BAF96B80098DFA4 /* GameController.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FAE0E9691BAF96A00098DFA4 /* GameController.framework */; };
		FAE0E9711BAF96BB0098DFA4 /* GameController.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FAE0E9691BAF96A00098DFA4 /* GameController.framework */; };
		FD15FD690E086911003BDF25 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1D30AB110D05D00D00671497 /* Foundation.framework */; };
		FD15FD6A0E086911003BDF25 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1DF5F4DF0D08C38300B7A737 /* UIKit.framework */; };
		FD15FD6B0E086911003BDF25 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1D3623EB0D0F72F000981E51 /* CoreGraphics.framework */; };
		FD15FD6C0E086911003BDF25 /* OpenGLES.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDB96ED30DEFC9C700FAF19F /* OpenGLES.framework */; };
		FD15FD6D0E086911003BDF25 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDB96EDF0DEFC9DC00FAF19F /* QuartzCore.framework */; };
		FD1B48DD0E313255007AB34E /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = FD1B489E0E313154007AB34E /* libSDL2.a */; };
		FD1B49980E313261007AB34E /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = FD1B489E0E313154007AB34E /* libSDL2.a */; };
		FD1B499C0E313269007AB34E /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = FD1B489E0E313154007AB34E /* libSDL2.a */; };
		FD1B499E0E31326C007AB34E /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = FD1B489E0E313154007AB34E /* libSDL2.a */; };
		FD1B49A00E313270007AB34E /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = FD1B489E0E313154007AB34E /* libSDL2.a */; };
		FD1B49A20E313273007AB34E /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = FD1B489E0E313154007AB34E /* libSDL2.a */; };
		FD5F9CE80E0E0741008E885B /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1D30AB110D05D00D00671497 /* Foundation.framework */; };
		FD5F9CE90E0E0741008E885B /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1DF5F4DF0D08C38300B7A737 /* UIKit.framework */; };
		FD5F9CEA0E0E0741008E885B /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1D3623EB0D0F72F000981E51 /* CoreGraphics.framework */; };
		FD5F9CEB0E0E0741008E885B /* OpenGLES.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDB96ED30DEFC9C700FAF19F /* OpenGLES.framework */; };
		FD5F9CEC0E0E0741008E885B /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDB96EDF0DEFC9DC00FAF19F /* QuartzCore.framework */; };
		FD77A00E0E26BC0500F39101 /* happy.c in Sources */ = {isa = PBXBuildFile; fileRef = FD77A0080E26BC0500F39101 /* happy.c */; };
		FD77A0130E26BC0500F39101 /* common.c in Sources */ = {isa = PBXBuildFile; fileRef = FD77A0060E26BC0500F39101 /* common.c */; };
		FD77A0160E26BC0500F39101 /* rectangles.c in Sources */ = {isa = PBXBuildFile; fileRef = FD77A00A0E26BC0500F39101 /* rectangles.c */; };
		FD77A0190E26BC0500F39101 /* common.c in Sources */ = {isa = PBXBuildFile; fileRef = FD77A0060E26BC0500F39101 /* common.c */; };
		FD77A01F0E26BC0500F39101 /* common.c in Sources */ = {isa = PBXBuildFile; fileRef = FD77A0060E26BC0500F39101 /* common.c */; };
		FD77A0230E26BC0500F39101 /* touch.c in Sources */ = {isa = PBXBuildFile; fileRef = FD77A00B0E26BC0500F39101 /* touch.c */; };
		FD77A0250E26BC0500F39101 /* common.c in Sources */ = {isa = PBXBuildFile; fileRef = FD77A0060E26BC0500F39101 /* common.c */; };
		FD77A0270E26BC0500F39101 /* mixer.c in Sources */ = {isa = PBXBuildFile; fileRef = FD77A0090E26BC0500F39101 /* mixer.c */; };
		FD77A02A0E26BC2700F39101 /* accelerometer.c in Sources */ = {isa = PBXBuildFile; fileRef = FD77A0050E26BC0500F39101 /* accelerometer.c */; };
		FD787AA10E22A5CC003E8E36 /* Default.png in Resources */ = {isa = PBXBuildFile; fileRef = FD787AA00E22A5CC003E8E36 /* Default.png */; };
		FD787AA20E22A5CC003E8E36 /* Default.png in Resources */ = {isa = PBXBuildFile; fileRef = FD787AA00E22A5CC003E8E36 /* Default.png */; };
		FD787AA30E22A5CC003E8E36 /* Default.png in Resources */ = {isa = PBXBuildFile; fileRef = FD787AA00E22A5CC003E8E36 /* Default.png */; };
		FD787AA40E22A5CC003E8E36 /* Default.png in Resources */ = {isa = PBXBuildFile; fileRef = FD787AA00E22A5CC003E8E36 /* Default.png */; };
		FD787AA50E22A5CC003E8E36 /* Default.png in Resources */ = {isa = PBXBuildFile; fileRef = FD787AA00E22A5CC003E8E36 /* Default.png */; };
		FD925B190E0F276600E92347 /* Icon.png in Resources */ = {isa = PBXBuildFile; fileRef = FD925B180E0F276600E92347 /* Icon.png */; };
		FD925B1A0E0F276600E92347 /* Icon.png in Resources */ = {isa = PBXBuildFile; fileRef = FD925B180E0F276600E92347 /* Icon.png */; };
		FD925B1B0E0F276600E92347 /* Icon.png in Resources */ = {isa = PBXBuildFile; fileRef = FD925B180E0F276600E92347 /* Icon.png */; };
		FDB651D00E43D1AD00F688B5 /* icon.bmp in Resources */ = {isa = PBXBuildFile; fileRef = FDB651CC0E43D19800F688B5 /* icon.bmp */; };
		FDB651D10E43D1B300F688B5 /* ship.bmp in Resources */ = {isa = PBXBuildFile; fileRef = FDB651CD0E43D19800F688B5 /* ship.bmp */; };
		FDB651D20E43D1B500F688B5 /* space.bmp in Resources */ = {isa = PBXBuildFile; fileRef = FDB651CE0E43D19800F688B5 /* space.bmp */; };
		FDB651D30E43D1BA00F688B5 /* stroke.bmp in Resources */ = {isa = PBXBuildFile; fileRef = FDB651CF0E43D19800F688B5 /* stroke.bmp */; };
		FDB651D40E43D1C500F688B5 /* ds_brush_snare.wav in Resources */ = {isa = PBXBuildFile; fileRef = FDB651C80E43D19800F688B5 /* ds_brush_snare.wav */; };
		FDB651D50E43D1C500F688B5 /* ds_china.wav in Resources */ = {isa = PBXBuildFile; fileRef = FDB651C90E43D19800F688B5 /* ds_china.wav */; };
		FDB651D60E43D1C500F688B5 /* ds_kick_big_amb.wav in Resources */ = {isa = PBXBuildFile; fileRef = FDB651CA0E43D19800F688B5 /* ds_kick_big_amb.wav */; };
		FDB651D70E43D1C500F688B5 /* ds_loose_skin_mute.wav in Resources */ = {isa = PBXBuildFile; fileRef = FDB651CB0E43D19800F688B5 /* ds_loose_skin_mute.wav */; };
		FDB651D80E43D1D800F688B5 /* stroke.bmp in Resources */ = {isa = PBXBuildFile; fileRef = FDB651CF0E43D19800F688B5 /* stroke.bmp */; };
		FDB651F90E43D1F300F688B5 /* stroke.bmp in Resources */ = {isa = PBXBuildFile; fileRef = FDB651CF0E43D19800F688B5 /* stroke.bmp */; };
		FDB651FA0E43D1F300F688B5 /* Icon.png in Resources */ = {isa = PBXBuildFile; fileRef = FD925B180E0F276600E92347 /* Icon.png */; };
		FDB651FB0E43D1F300F688B5 /* Default.png in Resources */ = {isa = PBXBuildFile; fileRef = FD787AA00E22A5CC003E8E36 /* Default.png */; };
		FDB651FD0E43D1F300F688B5 /* common.c in Sources */ = {isa = PBXBuildFile; fileRef = FD77A0060E26BC0500F39101 /* common.c */; };
		FDB652000E43D1F300F688B5 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = FD1B489E0E313154007AB34E /* libSDL2.a */; };
		FDB652020E43D1F300F688B5 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1D30AB110D05D00D00671497 /* Foundation.framework */; };
		FDB652030E43D1F300F688B5 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1DF5F4DF0D08C38300B7A737 /* UIKit.framework */; };
		FDB652040E43D1F300F688B5 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1D3623EB0D0F72F000981E51 /* CoreGraphics.framework */; };
		FDB652050E43D1F300F688B5 /* OpenGLES.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDB96ED30DEFC9C700FAF19F /* OpenGLES.framework */; };
		FDB652060E43D1F300F688B5 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDB96EDF0DEFC9DC00FAF19F /* QuartzCore.framework */; };
		FDB652070E43D1F300F688B5 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDF0D71D0E12D2AB00247964 /* CoreAudio.framework */; };
		FDB652080E43D1F300F688B5 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDF0D7220E12D31800247964 /* AudioToolbox.framework */; };
		FDB652120E43D21A00F688B5 /* keyboard.c in Sources */ = {isa = PBXBuildFile; fileRef = FDB652110E43D21A00F688B5 /* keyboard.c */; };
		FDB652C70E43E25900F688B5 /* kromasky_16x16.bmp in Resources */ = {isa = PBXBuildFile; fileRef = FDB652C60E43E25900F688B5 /* kromasky_16x16.bmp */; };
		FDB96ED40DEFC9C700FAF19F /* OpenGLES.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDB96ED30DEFC9C700FAF19F /* OpenGLES.framework */; };
		FDB96EE00DEFC9DC00FAF19F /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDB96EDF0DEFC9DC00FAF19F /* QuartzCore.framework */; };
		FDC202E10E107B1200ABAC90 /* Icon.png in Resources */ = {isa = PBXBuildFile; fileRef = FD925B180E0F276600E92347 /* Icon.png */; };
		FDC202E60E107B1200ABAC90 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1D30AB110D05D00D00671497 /* Foundation.framework */; };
		FDC202E70E107B1200ABAC90 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1DF5F4DF0D08C38300B7A737 /* UIKit.framework */; };
		FDC202E80E107B1200ABAC90 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1D3623EB0D0F72F000981E51 /* CoreGraphics.framework */; };
		FDC202E90E107B1200ABAC90 /* OpenGLES.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDB96ED30DEFC9C700FAF19F /* OpenGLES.framework */; };
		FDC202EA0E107B1200ABAC90 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDB96EDF0DEFC9DC00FAF19F /* QuartzCore.framework */; };
		FDC214870E26D78A00DDED23 /* common.c in Sources */ = {isa = PBXBuildFile; fileRef = FD77A0060E26BC0500F39101 /* common.c */; };
		FDC52EC80E2843D6008D768C /* Icon.png in Resources */ = {isa = PBXBuildFile; fileRef = FD925B180E0F276600E92347 /* Icon.png */; };
		FDC52EC90E2843D6008D768C /* Default.png in Resources */ = {isa = PBXBuildFile; fileRef = FD787AA00E22A5CC003E8E36 /* Default.png */; };
		FDC52ECF0E2843D6008D768C /* common.c in Sources */ = {isa = PBXBuildFile; fileRef = FD77A0060E26BC0500F39101 /* common.c */; };
		FDC52ED40E2843D6008D768C /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1D30AB110D05D00D00671497 /* Foundation.framework */; };
		FDC52ED50E2843D6008D768C /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1DF5F4DF0D08C38300B7A737 /* UIKit.framework */; };
		FDC52ED60E2843D6008D768C /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1D3623EB0D0F72F000981E51 /* CoreGraphics.framework */; };
		FDC52ED70E2843D6008D768C /* OpenGLES.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDB96ED30DEFC9C700FAF19F /* OpenGLES.framework */; };
		FDC52ED80E2843D6008D768C /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDB96EDF0DEFC9DC00FAF19F /* QuartzCore.framework */; };
		FDC52ED90E2843D6008D768C /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDF0D71D0E12D2AB00247964 /* CoreAudio.framework */; };
		FDC52EDA0E2843D6008D768C /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDF0D7220E12D31800247964 /* AudioToolbox.framework */; };
		FDC52EE50E284410008D768C /* fireworks.c in Sources */ = {isa = PBXBuildFile; fileRef = FDC52EE40E284410008D768C /* fireworks.c */; };
		FDF0D6960E12D05400247964 /* Icon.png in Resources */ = {isa = PBXBuildFile; fileRef = FD925B180E0F276600E92347 /* Icon.png */; };
		FDF0D69C0E12D05400247964 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1D30AB110D05D00D00671497 /* Foundation.framework */; };
		FDF0D69D0E12D05400247964 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1DF5F4DF0D08C38300B7A737 /* UIKit.framework */; };
		FDF0D69E0E12D05400247964 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1D3623EB0D0F72F000981E51 /* CoreGraphics.framework */; };
		FDF0D69F0E12D05400247964 /* OpenGLES.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDB96ED30DEFC9C700FAF19F /* OpenGLES.framework */; };
		FDF0D6A00E12D05400247964 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDB96EDF0DEFC9DC00FAF19F /* QuartzCore.framework */; };
		FDF0D71E0E12D2AB00247964 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDF0D71D0E12D2AB00247964 /* CoreAudio.framework */; };
		FDF0D7230E12D31800247964 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDF0D7220E12D31800247964 /* AudioToolbox.framework */; };
		FDF0D7950E12D52900247964 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDF0D71D0E12D2AB00247964 /* CoreAudio.framework */; };
		FDF0D7960E12D52900247964 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDF0D7220E12D31800247964 /* AudioToolbox.framework */; };
		FDF0D7A70E12D53200247964 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDF0D71D0E12D2AB00247964 /* CoreAudio.framework */; };
		FDF0D7A80E12D53200247964 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDF0D7220E12D31800247964 /* AudioToolbox.framework */; };
		FDF0D7A90E12D53500247964 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDF0D71D0E12D2AB00247964 /* CoreAudio.framework */; };
		FDF0D7AA0E12D53500247964 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDF0D7220E12D31800247964 /* AudioToolbox.framework */; };
		FDF0D7AB0E12D53800247964 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDF0D71D0E12D2AB00247964 /* CoreAudio.framework */; };
		FDF0D7AC0E12D53800247964 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FDF0D7220E12D31800247964 /* AudioToolbox.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		049F3694130CD86800FF080F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = FD1B48920E313154007AB34E /* SDL.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = FD6526620DE8FCCB002AD96B;
			remoteInfo = libSDL;
		};
		049F3696130CD87600FF080F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = FD1B48920E313154007AB34E /* SDL.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = FD6526620DE8FCCB002AD96B;
			remoteInfo = libSDL;
		};
		049F3698130CD87F00FF080F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = FD1B48920E313154007AB34E /* SDL.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = FD6526620DE8FCCB002AD96B;
			remoteInfo = libSDL;
		};
		049F369A130CD88800FF080F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = FD1B48920E313154007AB34E /* SDL.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = FD6526620DE8FCCB002AD96B;
			remoteInfo = libSDL;
		};
		049F369C130CD89000FF080F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = FD1B48920E313154007AB34E /* SDL.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = FD6526620DE8FCCB002AD96B;
			remoteInfo = libSDL;
		};
		049F369E130CD89800FF080F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = FD1B48920E313154007AB34E /* SDL.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = FD6526620DE8FCCB002AD96B;
			remoteInfo = libSDL;
		};
		049F36A0130CD8A000FF080F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = FD1B48920E313154007AB34E /* SDL.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = FD6526620DE8FCCB002AD96B;
			remoteInfo = libSDL;
		};
		FA30DEAB1BBF59D9009C397F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = FD1B48920E313154007AB34E /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = FAB598141BB5C1B100BE72C5;
			remoteInfo = "libSDL-tv";
		};
		FA30DEAE1BBF5A69009C397F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = FD1B48920E313154007AB34E /* SDL.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = FAB598131BB5C1B100BE72C5;
			remoteInfo = "libSDL-tv";
		};
		FD1B489D0E313154007AB34E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = FD1B48920E313154007AB34E /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = FD6526630DE8FCCB002AD96B;
			remoteInfo = StaticLib;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		1D30AB110D05D00D00671497 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		1D3623EB0D0F72F000981E51 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		1D6058910D05DD3D006BFB54 /* Rectangles.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Rectangles.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1DF5F4DF0D08C38300B7A737 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		8D1107310486CEB800E47090 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		FA30DE961BBF59D9009C397F /* Happy-TV.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Happy-TV.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		FA86C0361D9765BA009CB637 /* iOS Launch Screen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = "iOS Launch Screen.storyboard"; sourceTree = "<group>"; };
		FA8B4BA21967070A00F8EB7C /* CoreMotion.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMotion.framework; path = System/Library/Frameworks/CoreMotion.framework; sourceTree = SDKROOT; };
		FABA34D31D8B5E5600915323 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		FABA34D71D8B5E7700915323 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = Platforms/AppleTVOS.platform/Developer/SDKs/AppleTVOS10.0.sdk/System/Library/Frameworks/AVFoundation.framework; sourceTree = DEVELOPER_DIR; };
		FAE0E9691BAF96A00098DFA4 /* GameController.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = GameController.framework; path = System/Library/Frameworks/GameController.framework; sourceTree = SDKROOT; };
		FD15FCB20E086866003BDF25 /* Happy.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Happy.app; sourceTree = BUILT_PRODUCTS_DIR; };
		FD1B48920E313154007AB34E /* SDL.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = SDL.xcodeproj; path = ../SDL/SDL.xcodeproj; sourceTree = SOURCE_ROOT; };
		FD5F9BE40E0DEBEA008E885B /* Accel.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Accel.app; sourceTree = BUILT_PRODUCTS_DIR; };
		FD77A0050E26BC0500F39101 /* accelerometer.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = accelerometer.c; sourceTree = "<group>"; };
		FD77A0060E26BC0500F39101 /* common.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = common.c; sourceTree = "<group>"; };
		FD77A0070E26BC0500F39101 /* common.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = common.h; sourceTree = "<group>"; };
		FD77A0080E26BC0500F39101 /* happy.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = happy.c; sourceTree = "<group>"; };
		FD77A0090E26BC0500F39101 /* mixer.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = mixer.c; sourceTree = "<group>"; };
		FD77A00A0E26BC0500F39101 /* rectangles.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = rectangles.c; sourceTree = "<group>"; };
		FD77A00B0E26BC0500F39101 /* touch.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = touch.c; sourceTree = "<group>"; };
		FD787AA00E22A5CC003E8E36 /* Default.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Default.png; sourceTree = "<group>"; };
		FD925B180E0F276600E92347 /* Icon.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Icon.png; sourceTree = "<group>"; };
		FDB651C60E43D19800F688B5 /* license.txt */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = license.txt; sourceTree = "<group>"; };
		FDB651C80E43D19800F688B5 /* ds_brush_snare.wav */ = {isa = PBXFileReference; lastKnownFileType = audio.wav; path = ds_brush_snare.wav; sourceTree = "<group>"; };
		FDB651C90E43D19800F688B5 /* ds_china.wav */ = {isa = PBXFileReference; lastKnownFileType = audio.wav; path = ds_china.wav; sourceTree = "<group>"; };
		FDB651CA0E43D19800F688B5 /* ds_kick_big_amb.wav */ = {isa = PBXFileReference; lastKnownFileType = audio.wav; path = ds_kick_big_amb.wav; sourceTree = "<group>"; };
		FDB651CB0E43D19800F688B5 /* ds_loose_skin_mute.wav */ = {isa = PBXFileReference; lastKnownFileType = audio.wav; path = ds_loose_skin_mute.wav; sourceTree = "<group>"; };
		FDB651CC0E43D19800F688B5 /* icon.bmp */ = {isa = PBXFileReference; lastKnownFileType = image.bmp; path = icon.bmp; sourceTree = "<group>"; };
		FDB651CD0E43D19800F688B5 /* ship.bmp */ = {isa = PBXFileReference; lastKnownFileType = image.bmp; path = ship.bmp; sourceTree = "<group>"; };
		FDB651CE0E43D19800F688B5 /* space.bmp */ = {isa = PBXFileReference; lastKnownFileType = image.bmp; path = space.bmp; sourceTree = "<group>"; };
		FDB651CF0E43D19800F688B5 /* stroke.bmp */ = {isa = PBXFileReference; lastKnownFileType = image.bmp; path = stroke.bmp; sourceTree = "<group>"; };
		FDB6520C0E43D1F300F688B5 /* Keyboard.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Keyboard.app; sourceTree = BUILT_PRODUCTS_DIR; };
		FDB652110E43D21A00F688B5 /* keyboard.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = keyboard.c; sourceTree = "<group>"; };
		FDB652C60E43E25900F688B5 /* kromasky_16x16.bmp */ = {isa = PBXFileReference; lastKnownFileType = image.bmp; path = kromasky_16x16.bmp; sourceTree = "<group>"; };
		FDB96ED30DEFC9C700FAF19F /* OpenGLES.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenGLES.framework; path = System/Library/Frameworks/OpenGLES.framework; sourceTree = SDKROOT; };
		FDB96EDF0DEFC9DC00FAF19F /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		FDC202EE0E107B1200ABAC90 /* Touch.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Touch.app; sourceTree = BUILT_PRODUCTS_DIR; };
		FDC52EDE0E2843D6008D768C /* Fireworks.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Fireworks.app; sourceTree = BUILT_PRODUCTS_DIR; };
		FDC52EE40E284410008D768C /* fireworks.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = fireworks.c; sourceTree = "<group>"; };
		FDF0D6A40E12D05400247964 /* Mixer.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Mixer.app; sourceTree = BUILT_PRODUCTS_DIR; };
		FDF0D71D0E12D2AB00247964 /* CoreAudio.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreAudio.framework; path = System/Library/Frameworks/CoreAudio.framework; sourceTree = SDKROOT; };
		FDF0D7220E12D31800247964 /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		1D60588F0D05DD3D006BFB54 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FABA34D41D8B5E5600915323 /* AVFoundation.framework in Frameworks */,
				FD1B48DD0E313255007AB34E /* libSDL2.a in Frameworks */,
				FAE0E96A1BAF96A00098DFA4 /* GameController.framework in Frameworks */,
				FA8B4BA31967070A00F8EB7C /* CoreMotion.framework in Frameworks */,
				FDF0D7AB0E12D53800247964 /* CoreAudio.framework in Frameworks */,
				FDF0D7AC0E12D53800247964 /* AudioToolbox.framework in Frameworks */,
				1D60589F0D05DD5A006BFB54 /* Foundation.framework in Frameworks */,
				1DF5F4E00D08C38300B7A737 /* UIKit.framework in Frameworks */,
				1D3623EC0D0F72F000981E51 /* CoreGraphics.framework in Frameworks */,
				FDB96ED40DEFC9C700FAF19F /* OpenGLES.framework in Frameworks */,
				FDB96EE00DEFC9DC00FAF19F /* QuartzCore.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FA30DE931BBF59D9009C397F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FABA34D81D8B5E7700915323 /* AVFoundation.framework in Frameworks */,
				FA30DEB71BBF5BB8009C397F /* libSDL2.a in Frameworks */,
				FA30DEC81BBF5C14009C397F /* GameController.framework in Frameworks */,
				FA30DEC91BBF5C14009C397F /* AudioToolbox.framework in Frameworks */,
				FA30DECA1BBF5C14009C397F /* QuartzCore.framework in Frameworks */,
				FA30DECB1BBF5C14009C397F /* OpenGLES.framework in Frameworks */,
				FA30DECC1BBF5C14009C397F /* CoreGraphics.framework in Frameworks */,
				FA30DECD1BBF5C14009C397F /* UIKit.framework in Frameworks */,
				FA30DECE1BBF5C14009C397F /* Foundation.framework in Frameworks */,
				FA30DECF1BBF5C14009C397F /* CoreAudio.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FD15FCB00E086866003BDF25 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FABA34D61D8B5E5A00915323 /* AVFoundation.framework in Frameworks */,
				FD1B49980E313261007AB34E /* libSDL2.a in Frameworks */,
				FAE0E96C1BAF96A90098DFA4 /* GameController.framework in Frameworks */,
				FA8B4BA41967071300F8EB7C /* CoreMotion.framework in Frameworks */,
				FDF0D7A90E12D53500247964 /* CoreAudio.framework in Frameworks */,
				FDF0D7AA0E12D53500247964 /* AudioToolbox.framework in Frameworks */,
				FD15FD690E086911003BDF25 /* Foundation.framework in Frameworks */,
				FD15FD6A0E086911003BDF25 /* UIKit.framework in Frameworks */,
				FD15FD6B0E086911003BDF25 /* CoreGraphics.framework in Frameworks */,
				FD15FD6C0E086911003BDF25 /* OpenGLES.framework in Frameworks */,
				FD15FD6D0E086911003BDF25 /* QuartzCore.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FD5F9BE20E0DEBEA008E885B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FABA34D91D8B5E7B00915323 /* AVFoundation.framework in Frameworks */,
				FD1B499C0E313269007AB34E /* libSDL2.a in Frameworks */,
				FAE0E96D1BAF96AF0098DFA4 /* GameController.framework in Frameworks */,
				FA8B4BA51967071A00F8EB7C /* CoreMotion.framework in Frameworks */,
				FDF0D7A70E12D53200247964 /* CoreAudio.framework in Frameworks */,
				FDF0D7A80E12D53200247964 /* AudioToolbox.framework in Frameworks */,
				FD5F9CEB0E0E0741008E885B /* OpenGLES.framework in Frameworks */,
				FD5F9CEC0E0E0741008E885B /* QuartzCore.framework in Frameworks */,
				FD5F9CE80E0E0741008E885B /* Foundation.framework in Frameworks */,
				FD5F9CE90E0E0741008E885B /* UIKit.framework in Frameworks */,
				FD5F9CEA0E0E0741008E885B /* CoreGraphics.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FDB651FF0E43D1F300F688B5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FABA34DD1D8B5E8D00915323 /* AVFoundation.framework in Frameworks */,
				FDB652000E43D1F300F688B5 /* libSDL2.a in Frameworks */,
				FAE0E9711BAF96BB0098DFA4 /* GameController.framework in Frameworks */,
				FA8B4BA91967073D00F8EB7C /* CoreMotion.framework in Frameworks */,
				FDB652020E43D1F300F688B5 /* Foundation.framework in Frameworks */,
				FDB652030E43D1F300F688B5 /* UIKit.framework in Frameworks */,
				FDB652040E43D1F300F688B5 /* CoreGraphics.framework in Frameworks */,
				FDB652050E43D1F300F688B5 /* OpenGLES.framework in Frameworks */,
				FDB652060E43D1F300F688B5 /* QuartzCore.framework in Frameworks */,
				FDB652070E43D1F300F688B5 /* CoreAudio.framework in Frameworks */,
				FDB652080E43D1F300F688B5 /* AudioToolbox.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FDC202E40E107B1200ABAC90 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FABA34DA1D8B5E7F00915323 /* AVFoundation.framework in Frameworks */,
				FD1B499E0E31326C007AB34E /* libSDL2.a in Frameworks */,
				FAE0E96E1BAF96B10098DFA4 /* GameController.framework in Frameworks */,
				FA8B4BA61967072100F8EB7C /* CoreMotion.framework in Frameworks */,
				FDF0D7950E12D52900247964 /* CoreAudio.framework in Frameworks */,
				FDF0D7960E12D52900247964 /* AudioToolbox.framework in Frameworks */,
				FDC202E60E107B1200ABAC90 /* Foundation.framework in Frameworks */,
				FDC202E70E107B1200ABAC90 /* UIKit.framework in Frameworks */,
				FDC202E80E107B1200ABAC90 /* CoreGraphics.framework in Frameworks */,
				FDC202E90E107B1200ABAC90 /* OpenGLES.framework in Frameworks */,
				FDC202EA0E107B1200ABAC90 /* QuartzCore.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FDC52ED10E2843D6008D768C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FABA34DC1D8B5E8900915323 /* AVFoundation.framework in Frameworks */,
				FD1B49A20E313273007AB34E /* libSDL2.a in Frameworks */,
				FAE0E9701BAF96B80098DFA4 /* GameController.framework in Frameworks */,
				FA8B4BA81967073400F8EB7C /* CoreMotion.framework in Frameworks */,
				FDC52ED40E2843D6008D768C /* Foundation.framework in Frameworks */,
				FDC52ED50E2843D6008D768C /* UIKit.framework in Frameworks */,
				FDC52ED60E2843D6008D768C /* CoreGraphics.framework in Frameworks */,
				FDC52ED70E2843D6008D768C /* OpenGLES.framework in Frameworks */,
				FDC52ED80E2843D6008D768C /* QuartzCore.framework in Frameworks */,
				FDC52ED90E2843D6008D768C /* CoreAudio.framework in Frameworks */,
				FDC52EDA0E2843D6008D768C /* AudioToolbox.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FDF0D69A0E12D05400247964 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FABA34DB1D8B5E8500915323 /* AVFoundation.framework in Frameworks */,
				FD1B49A00E313270007AB34E /* libSDL2.a in Frameworks */,
				FAE0E96F1BAF96B50098DFA4 /* GameController.framework in Frameworks */,
				FA8B4BA71967072800F8EB7C /* CoreMotion.framework in Frameworks */,
				FDF0D69C0E12D05400247964 /* Foundation.framework in Frameworks */,
				FDF0D69D0E12D05400247964 /* UIKit.framework in Frameworks */,
				FDF0D69E0E12D05400247964 /* CoreGraphics.framework in Frameworks */,
				FDF0D69F0E12D05400247964 /* OpenGLES.framework in Frameworks */,
				FDF0D6A00E12D05400247964 /* QuartzCore.framework in Frameworks */,
				FDF0D71E0E12D2AB00247964 /* CoreAudio.framework in Frameworks */,
				FDF0D7230E12D31800247964 /* AudioToolbox.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		19C28FACFE9D520D11CA2CBB /* Products */ = {
			isa = PBXGroup;
			children = (
				1D6058910D05DD3D006BFB54 /* Rectangles.app */,
				FD15FCB20E086866003BDF25 /* Happy.app */,
				FD5F9BE40E0DEBEA008E885B /* Accel.app */,
				FDC202EE0E107B1200ABAC90 /* Touch.app */,
				FDF0D6A40E12D05400247964 /* Mixer.app */,
				FDC52EDE0E2843D6008D768C /* Fireworks.app */,
				FDB6520C0E43D1F300F688B5 /* Keyboard.app */,
				FA30DE961BBF59D9009C397F /* Happy-TV.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		29B97314FDCFA39411CA2CEA /* CustomTemplate */ = {
			isa = PBXGroup;
			children = (
				FA86C0361D9765BA009CB637 /* iOS Launch Screen.storyboard */,
				FD1B48920E313154007AB34E /* SDL.xcodeproj */,
				FD77A0040E26BC0500F39101 /* src */,
				29B97317FDCFA39411CA2CEA /* Resources */,
				29B97323FDCFA39411CA2CEA /* Frameworks */,
				19C28FACFE9D520D11CA2CBB /* Products */,
			);
			name = CustomTemplate;
			sourceTree = "<group>";
		};
		29B97317FDCFA39411CA2CEA /* Resources */ = {
			isa = PBXGroup;
			children = (
				FDB651C30E43D19800F688B5 /* data */,
				FD787AA00E22A5CC003E8E36 /* Default.png */,
				FD925B180E0F276600E92347 /* Icon.png */,
				8D1107310486CEB800E47090 /* Info.plist */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		29B97323FDCFA39411CA2CEA /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				FABA34D71D8B5E7700915323 /* AVFoundation.framework */,
				FABA34D31D8B5E5600915323 /* AVFoundation.framework */,
				FAE0E9691BAF96A00098DFA4 /* GameController.framework */,
				FA8B4BA21967070A00F8EB7C /* CoreMotion.framework */,
				FDF0D7220E12D31800247964 /* AudioToolbox.framework */,
				FDB96EDF0DEFC9DC00FAF19F /* QuartzCore.framework */,
				FDB96ED30DEFC9C700FAF19F /* OpenGLES.framework */,
				1D3623EB0D0F72F000981E51 /* CoreGraphics.framework */,
				1DF5F4DF0D08C38300B7A737 /* UIKit.framework */,
				1D30AB110D05D00D00671497 /* Foundation.framework */,
				FDF0D71D0E12D2AB00247964 /* CoreAudio.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		FD1B48930E313154007AB34E /* Products */ = {
			isa = PBXGroup;
			children = (
				FD1B489E0E313154007AB34E /* libSDL2.a */,
				FA30DEAC1BBF59D9009C397F /* libSDL2.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		FD77A0040E26BC0500F39101 /* src */ = {
			isa = PBXGroup;
			children = (
				FD77A0060E26BC0500F39101 /* common.c */,
				FD77A0070E26BC0500F39101 /* common.h */,
				FD77A00A0E26BC0500F39101 /* rectangles.c */,
				FD77A0080E26BC0500F39101 /* happy.c */,
				FD77A0050E26BC0500F39101 /* accelerometer.c */,
				FD77A00B0E26BC0500F39101 /* touch.c */,
				FD77A0090E26BC0500F39101 /* mixer.c */,
				FDB652110E43D21A00F688B5 /* keyboard.c */,
				FDC52EE40E284410008D768C /* fireworks.c */,
			);
			path = src;
			sourceTree = "<group>";
		};
		FDB651C30E43D19800F688B5 /* data */ = {
			isa = PBXGroup;
			children = (
				FDB651C40E43D19800F688B5 /* bitmapfont */,
				FDB651C70E43D19800F688B5 /* drums */,
				FDB651CC0E43D19800F688B5 /* icon.bmp */,
				FDB651CD0E43D19800F688B5 /* ship.bmp */,
				FDB651CE0E43D19800F688B5 /* space.bmp */,
				FDB651CF0E43D19800F688B5 /* stroke.bmp */,
			);
			path = data;
			sourceTree = "<group>";
		};
		FDB651C40E43D19800F688B5 /* bitmapfont */ = {
			isa = PBXGroup;
			children = (
				FDB652C60E43E25900F688B5 /* kromasky_16x16.bmp */,
				FDB651C60E43D19800F688B5 /* license.txt */,
			);
			path = bitmapfont;
			sourceTree = "<group>";
		};
		FDB651C70E43D19800F688B5 /* drums */ = {
			isa = PBXGroup;
			children = (
				FDB651C80E43D19800F688B5 /* ds_brush_snare.wav */,
				FDB651C90E43D19800F688B5 /* ds_china.wav */,
				FDB651CA0E43D19800F688B5 /* ds_kick_big_amb.wav */,
				FDB651CB0E43D19800F688B5 /* ds_loose_skin_mute.wav */,
			);
			path = drums;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1D6058900D05DD3D006BFB54 /* Rectangles */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1D6058960D05DD3E006BFB54 /* Build configuration list for PBXNativeTarget "Rectangles" */;
			buildPhases = (
				1D60588D0D05DD3D006BFB54 /* Resources */,
				1D60588E0D05DD3D006BFB54 /* Sources */,
				1D60588F0D05DD3D006BFB54 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				049F3695130CD86800FF080F /* PBXTargetDependency */,
			);
			name = Rectangles;
			productName = SDLiPodTest;
			productReference = 1D6058910D05DD3D006BFB54 /* Rectangles.app */;
			productType = "com.apple.product-type.application";
		};
		FA30DE951BBF59D9009C397F /* Happy-TV */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FA30DEAD1BBF59D9009C397F /* Build configuration list for PBXNativeTarget "Happy-TV" */;
			buildPhases = (
				FA30DE921BBF59D9009C397F /* Sources */,
				FA30DE941BBF59D9009C397F /* Resources */,
				FA30DE931BBF59D9009C397F /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				FA30DEAF1BBF5A69009C397F /* PBXTargetDependency */,
			);
			name = "Happy-TV";
			productName = "Happy-TV";
			productReference = FA30DE961BBF59D9009C397F /* Happy-TV.app */;
			productType = "com.apple.product-type.application";
		};
		FD15FCB10E086866003BDF25 /* Happy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FD15FCB70E086867003BDF25 /* Build configuration list for PBXNativeTarget "Happy" */;
			buildPhases = (
				FD15FCAE0E086866003BDF25 /* Resources */,
				FD15FCAF0E086866003BDF25 /* Sources */,
				FD15FCB00E086866003BDF25 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				049F3697130CD87600FF080F /* PBXTargetDependency */,
			);
			name = Happy;
			productName = BMPTest;
			productReference = FD15FCB20E086866003BDF25 /* Happy.app */;
			productType = "com.apple.product-type.application";
		};
		FD5F9BE30E0DEBEA008E885B /* Accel */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FD5F9BE90E0DEBEB008E885B /* Build configuration list for PBXNativeTarget "Accel" */;
			buildPhases = (
				FD5F9BE00E0DEBEA008E885B /* Resources */,
				FD5F9BE10E0DEBEA008E885B /* Sources */,
				FD5F9BE20E0DEBEA008E885B /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				049F3699130CD87F00FF080F /* PBXTargetDependency */,
			);
			name = Accel;
			productName = Accelerometer;
			productReference = FD5F9BE40E0DEBEA008E885B /* Accel.app */;
			productType = "com.apple.product-type.application";
		};
		FDB651F70E43D1F300F688B5 /* Keyboard */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FDB652090E43D1F300F688B5 /* Build configuration list for PBXNativeTarget "Keyboard" */;
			buildPhases = (
				FDB651F80E43D1F300F688B5 /* Resources */,
				FDB651FC0E43D1F300F688B5 /* Sources */,
				FDB651FF0E43D1F300F688B5 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				049F36A1130CD8A000FF080F /* PBXTargetDependency */,
			);
			name = Keyboard;
			productName = Accelerometer;
			productReference = FDB6520C0E43D1F300F688B5 /* Keyboard.app */;
			productType = "com.apple.product-type.application";
		};
		FDC202DD0E107B1200ABAC90 /* Touch */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FDC202EB0E107B1200ABAC90 /* Build configuration list for PBXNativeTarget "Touch" */;
			buildPhases = (
				FDC202DE0E107B1200ABAC90 /* Resources */,
				FDC202E20E107B1200ABAC90 /* Sources */,
				FDC202E40E107B1200ABAC90 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				049F369B130CD88800FF080F /* PBXTargetDependency */,
			);
			name = Touch;
			productName = Accelerometer;
			productReference = FDC202EE0E107B1200ABAC90 /* Touch.app */;
			productType = "com.apple.product-type.application";
		};
		FDC52EC60E2843D6008D768C /* Fireworks */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FDC52EDB0E2843D6008D768C /* Build configuration list for PBXNativeTarget "Fireworks" */;
			buildPhases = (
				FDC52EC70E2843D6008D768C /* Resources */,
				FDC52ECE0E2843D6008D768C /* Sources */,
				FDC52ED10E2843D6008D768C /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				049F369F130CD89800FF080F /* PBXTargetDependency */,
			);
			name = Fireworks;
			productName = Accelerometer;
			productReference = FDC52EDE0E2843D6008D768C /* Fireworks.app */;
			productType = "com.apple.product-type.application";
		};
		FDF0D6920E12D05400247964 /* Mixer */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FDF0D6A10E12D05400247964 /* Build configuration list for PBXNativeTarget "Mixer" */;
			buildPhases = (
				FDF0D6930E12D05400247964 /* Resources */,
				FDF0D6980E12D05400247964 /* Sources */,
				FDF0D69A0E12D05400247964 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				049F369D130CD89000FF080F /* PBXTargetDependency */,
			);
			name = Mixer;
			productName = Accelerometer;
			productReference = FDF0D6A40E12D05400247964 /* Mixer.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		29B97313FDCFA39411CA2CEA /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0630;
				TargetAttributes = {
					FA30DE951BBF59D9009C397F = {
						CreatedOnToolsVersion = 7.1;
					};
					FDC52EC60E2843D6008D768C = {
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = C01FCF4E08A954540054247B /* Build configuration list for PBXProject "Demos" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 1;
			knownRegions = (
				English,
				Japanese,
				French,
				German,
				Base,
			);
			mainGroup = 29B97314FDCFA39411CA2CEA /* CustomTemplate */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = FD1B48930E313154007AB34E /* Products */;
					ProjectRef = FD1B48920E313154007AB34E /* SDL.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				1D6058900D05DD3D006BFB54 /* Rectangles */,
				FD15FCB10E086866003BDF25 /* Happy */,
				FA30DE951BBF59D9009C397F /* Happy-TV */,
				FD5F9BE30E0DEBEA008E885B /* Accel */,
				FDC202DD0E107B1200ABAC90 /* Touch */,
				FDF0D6920E12D05400247964 /* Mixer */,
				FDC52EC60E2843D6008D768C /* Fireworks */,
				FDB651F70E43D1F300F688B5 /* Keyboard */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		FA30DEAC1BBF59D9009C397F /* libSDL2.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libSDL2.a;
			remoteRef = FA30DEAB1BBF59D9009C397F /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		FD1B489E0E313154007AB34E /* libSDL2.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libSDL2.a;
			remoteRef = FD1B489D0E313154007AB34E /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXResourcesBuildPhase section */
		1D60588D0D05DD3D006BFB54 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FA86C0371D9765BB009CB637 /* iOS Launch Screen.storyboard in Resources */,
				FD925B1B0E0F276600E92347 /* Icon.png in Resources */,
				FD787AA20E22A5CC003E8E36 /* Default.png in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FA30DE941BBF59D9009C397F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FA30DEB31BBF5AD7009C397F /* icon.bmp in Resources */,
				FA30DEB41BBF5ADD009C397F /* Icon.png in Resources */,
				FA30DEB61BBF5AE6009C397F /* Default.png in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FD15FCAE0E086866003BDF25 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FDB651D00E43D1AD00F688B5 /* icon.bmp in Resources */,
				FA86C0381D9765BB009CB637 /* iOS Launch Screen.storyboard in Resources */,
				FD925B1A0E0F276600E92347 /* Icon.png in Resources */,
				FD787AA10E22A5CC003E8E36 /* Default.png in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FD5F9BE00E0DEBEA008E885B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FDB651D20E43D1B500F688B5 /* space.bmp in Resources */,
				FDB651D10E43D1B300F688B5 /* ship.bmp in Resources */,
				FD925B190E0F276600E92347 /* Icon.png in Resources */,
				FD787AA30E22A5CC003E8E36 /* Default.png in Resources */,
				FA86C0391D9765BB009CB637 /* iOS Launch Screen.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FDB651F80E43D1F300F688B5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FDB651F90E43D1F300F688B5 /* stroke.bmp in Resources */,
				FDB651FA0E43D1F300F688B5 /* Icon.png in Resources */,
				FDB651FB0E43D1F300F688B5 /* Default.png in Resources */,
				FDB652C70E43E25900F688B5 /* kromasky_16x16.bmp in Resources */,
				FA86C03D1D9765BB009CB637 /* iOS Launch Screen.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FDC202DE0E107B1200ABAC90 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FDB651D30E43D1BA00F688B5 /* stroke.bmp in Resources */,
				FA86C03A1D9765BB009CB637 /* iOS Launch Screen.storyboard in Resources */,
				FDC202E10E107B1200ABAC90 /* Icon.png in Resources */,
				FD787AA40E22A5CC003E8E36 /* Default.png in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FDC52EC70E2843D6008D768C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FDB651D80E43D1D800F688B5 /* stroke.bmp in Resources */,
				FA86C03C1D9765BB009CB637 /* iOS Launch Screen.storyboard in Resources */,
				FDC52EC80E2843D6008D768C /* Icon.png in Resources */,
				FDC52EC90E2843D6008D768C /* Default.png in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FDF0D6930E12D05400247964 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FDB651D40E43D1C500F688B5 /* ds_brush_snare.wav in Resources */,
				FDB651D50E43D1C500F688B5 /* ds_china.wav in Resources */,
				FDB651D60E43D1C500F688B5 /* ds_kick_big_amb.wav in Resources */,
				FA86C03B1D9765BB009CB637 /* iOS Launch Screen.storyboard in Resources */,
				FDB651D70E43D1C500F688B5 /* ds_loose_skin_mute.wav in Resources */,
				FDF0D6960E12D05400247964 /* Icon.png in Resources */,
				FD787AA50E22A5CC003E8E36 /* Default.png in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1D60588E0D05DD3D006BFB54 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FD77A0130E26BC0500F39101 /* common.c in Sources */,
				FD77A0160E26BC0500F39101 /* rectangles.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FA30DE921BBF59D9009C397F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FA30DEB01BBF5A8F009C397F /* common.c in Sources */,
				FA30DEB11BBF5A93009C397F /* happy.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FD15FCAF0E086866003BDF25 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FDC214870E26D78A00DDED23 /* common.c in Sources */,
				FD77A00E0E26BC0500F39101 /* happy.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FD5F9BE10E0DEBEA008E885B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FD77A0190E26BC0500F39101 /* common.c in Sources */,
				FD77A02A0E26BC2700F39101 /* accelerometer.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FDB651FC0E43D1F300F688B5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FDB651FD0E43D1F300F688B5 /* common.c in Sources */,
				FDB652120E43D21A00F688B5 /* keyboard.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FDC202E20E107B1200ABAC90 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FD77A01F0E26BC0500F39101 /* common.c in Sources */,
				FD77A0230E26BC0500F39101 /* touch.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FDC52ECE0E2843D6008D768C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FDC52ECF0E2843D6008D768C /* common.c in Sources */,
				FDC52EE50E284410008D768C /* fireworks.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FDF0D6980E12D05400247964 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FD77A0250E26BC0500F39101 /* common.c in Sources */,
				FD77A0270E26BC0500F39101 /* mixer.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		049F3695130CD86800FF080F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = libSDL;
			targetProxy = 049F3694130CD86800FF080F /* PBXContainerItemProxy */;
		};
		049F3697130CD87600FF080F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = libSDL;
			targetProxy = 049F3696130CD87600FF080F /* PBXContainerItemProxy */;
		};
		049F3699130CD87F00FF080F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = libSDL;
			targetProxy = 049F3698130CD87F00FF080F /* PBXContainerItemProxy */;
		};
		049F369B130CD88800FF080F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = libSDL;
			targetProxy = 049F369A130CD88800FF080F /* PBXContainerItemProxy */;
		};
		049F369D130CD89000FF080F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = libSDL;
			targetProxy = 049F369C130CD89000FF080F /* PBXContainerItemProxy */;
		};
		049F369F130CD89800FF080F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = libSDL;
			targetProxy = 049F369E130CD89800FF080F /* PBXContainerItemProxy */;
		};
		049F36A1130CD8A000FF080F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = libSDL;
			targetProxy = 049F36A0130CD8A000FF080F /* PBXContainerItemProxy */;
		};
		FA30DEAF1BBF5A69009C397F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "libSDL-tv";
			targetProxy = FA30DEAE1BBF5A69009C397F /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		1D6058940D05DD3E006BFB54 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				INFOPLIST_FILE = Info.plist;
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.Rectangles;
				PRODUCT_NAME = Rectangles;
			};
			name = Debug;
		};
		1D6058950D05DD3E006BFB54 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				INFOPLIST_FILE = Info.plist;
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.Rectangles;
				PRODUCT_NAME = Rectangles;
			};
			name = Release;
		};
		C01FCF4F08A954540054247B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				GCC_OPTIMIZATION_LEVEL = 0;
				HEADER_SEARCH_PATHS = ../../include;
				ONLY_ACTIVE_ARCH = YES;
				PRELINK_LIBS = "";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		C01FCF5008A954540054247B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				HEADER_SEARCH_PATHS = ../../include;
				PRELINK_LIBS = "";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		FA30DEA71BBF59D9009C397F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = "";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				MTL_ENABLE_DEBUG_INFO = YES;
				PRODUCT_BUNDLE_IDENTIFIER = "com.yourcompany.Happy-TV";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 9.0;
			};
			name = Debug;
		};
		FA30DEA81BBF59D9009C397F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = "";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = "com.yourcompany.Happy-TV";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 9.0;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		FD15FCB50E086866003BDF25 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				GCC_DYNAMIC_NO_PIC = NO;
				INFOPLIST_FILE = Info.plist;
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.Happy;
				PRODUCT_NAME = Happy;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		FD15FCB60E086866003BDF25 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				INFOPLIST_FILE = Info.plist;
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.Happy;
				PRODUCT_NAME = Happy;
				SDKROOT = iphoneos;
			};
			name = Release;
		};
		FD5F9BE70E0DEBEB008E885B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				INFOPLIST_FILE = Info.plist;
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.Accel;
				PRODUCT_NAME = Accel;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		FD5F9BE80E0DEBEB008E885B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				INFOPLIST_FILE = Info.plist;
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.Accel;
				PRODUCT_NAME = Accel;
				SDKROOT = iphoneos;
			};
			name = Release;
		};
		FDB6520A0E43D1F300F688B5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				INFOPLIST_FILE = Info.plist;
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.Keyboard;
				PRODUCT_NAME = Keyboard;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		FDB6520B0E43D1F300F688B5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				INFOPLIST_FILE = Info.plist;
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.Keyboard;
				PRODUCT_NAME = Keyboard;
				SDKROOT = iphoneos;
			};
			name = Release;
		};
		FDC202EC0E107B1200ABAC90 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				INFOPLIST_FILE = Info.plist;
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.Touch;
				PRODUCT_NAME = Touch;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		FDC202ED0E107B1200ABAC90 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				INFOPLIST_FILE = Info.plist;
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.Touch;
				PRODUCT_NAME = Touch;
				SDKROOT = iphoneos;
			};
			name = Release;
		};
		FDC52EDC0E2843D6008D768C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = Info.plist;
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.Fireworks;
				PRODUCT_NAME = Fireworks;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		FDC52EDD0E2843D6008D768C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = Info.plist;
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.Fireworks;
				PRODUCT_NAME = Fireworks;
				SDKROOT = iphoneos;
			};
			name = Release;
		};
		FDF0D6A20E12D05400247964 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				INFOPLIST_FILE = Info.plist;
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.Mixer;
				PRODUCT_NAME = Mixer;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		FDF0D6A30E12D05400247964 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				INFOPLIST_FILE = Info.plist;
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.Mixer;
				PRODUCT_NAME = Mixer;
				SDKROOT = iphoneos;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1D6058960D05DD3E006BFB54 /* Build configuration list for PBXNativeTarget "Rectangles" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1D6058940D05DD3E006BFB54 /* Debug */,
				1D6058950D05DD3E006BFB54 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C01FCF4E08A954540054247B /* Build configuration list for PBXProject "Demos" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C01FCF4F08A954540054247B /* Debug */,
				C01FCF5008A954540054247B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FA30DEAD1BBF59D9009C397F /* Build configuration list for PBXNativeTarget "Happy-TV" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FA30DEA71BBF59D9009C397F /* Debug */,
				FA30DEA81BBF59D9009C397F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FD15FCB70E086867003BDF25 /* Build configuration list for PBXNativeTarget "Happy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FD15FCB50E086866003BDF25 /* Debug */,
				FD15FCB60E086866003BDF25 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FD5F9BE90E0DEBEB008E885B /* Build configuration list for PBXNativeTarget "Accel" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FD5F9BE70E0DEBEB008E885B /* Debug */,
				FD5F9BE80E0DEBEB008E885B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FDB652090E43D1F300F688B5 /* Build configuration list for PBXNativeTarget "Keyboard" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FDB6520A0E43D1F300F688B5 /* Debug */,
				FDB6520B0E43D1F300F688B5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FDC202EB0E107B1200ABAC90 /* Build configuration list for PBXNativeTarget "Touch" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FDC202EC0E107B1200ABAC90 /* Debug */,
				FDC202ED0E107B1200ABAC90 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FDC52EDB0E2843D6008D768C /* Build configuration list for PBXNativeTarget "Fireworks" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FDC52EDC0E2843D6008D768C /* Debug */,
				FDC52EDD0E2843D6008D768C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FDF0D6A10E12D05400247964 /* Build configuration list for PBXNativeTarget "Mixer" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FDF0D6A20E12D05400247964 /* Debug */,
				FDF0D6A30E12D05400247964 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 29B97313FDCFA39411CA2CEA /* Project object */;
}
