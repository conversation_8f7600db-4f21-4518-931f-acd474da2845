==============================================================================
About the iPhone OS Demo Applications
==============================================================================

Demos.xcodeproj contains several targets for iPhone oriented SDL demos.  These demos are written strictly using SDL 2.0 calls.  All the demos except for Fireworks (which requires OpenGL ES) should work on platforms other than iPhone OS, though you'll need to write your own compile script.

Common files:

	common.c and common.h contain code common to all demo applications.  This includes functions about delta timing (in seconds), simple error handling, and functions for generating random numbers.

Rectangles (rectangles.c):

	Draws randomly sized and colored rectangles all over the screen by using SDL_RenderFillRect.  This is the simplest of all the demos.

Happy (happy.c):

	Loads the classic happy-face bitmap and draws a large number of happy faces bouncing around the screen.  Shows how you can load a bitmap into an SDL_Texture.

Accelerometer (accelerometer.c):

	Uses the iPhone's accelerometer as a joystick device to move a spaceship around the screen.  Note the use of the macro SDL_IPHONE_MAX_GFORCE (normally defined in SDL_config_iphoneos.h) which converts between the Sint16 number returned by SDL_JoystickGetAxis, and the floating point units of g-force reported natively by the iPhone. 

Touch (touch.c):

	Acts as a finger-paint type program.  Demonstrates how you can use SDL mouse input to accept touch input from the iPhone.  If SDL for iPhone is compiled with multitouch as multiple mouse emulation (SDL_IPHONE_MULTIPLE_MICE in SDL_config_iphoneos.h) then the program will accept multiple finger inputs simultaneously. 

Mixer (mixer.c):

	Displays several rectangular buttons which can be used as a virtual drumkit.  Demonstrates how you can play .wav sounds in SDL and how you can use SDL_MixAudioFormat to build a software mixer that can play multiple sounds at once.

Keyboard (keyboard.c):

	Loads a bitmap font and let's the user type words, numbers, and symbols using the iPhone's virtual keyboard.  The iPhone's onscreen keyboard visibility is toggled when the user taps the screen.  If the user types ':)' a happy face is displayed.  Demonstrates how to use functions added to the iPhone implementation of SDL to toggle keyboard onscreen visibility.

Fireworks (fireworks.c):

	Displays a fireworks show.  When you tap the iPhone's screen, fireworks fly from the bottom of the screen and explode at the point that you tapped.  Demonstrates how you can use SDL on iPhone to build an OpenGL ES based application. Shows you how you can use SDL_LoadBMP to load a bmp image and convert it to an OpenGL ES texture.  Of lesser importance, shows how you can use OpenGL ES point sprites to build an efficient particle system.

==============================================================================
Building and Running the demos
==============================================================================

Before building the demos you must first build SDL as a static library for BOTH the iPhone Simulator and the iPhone itself.  See the iPhone SDL main README file for directions on how to do this.  Once this is done, simply launch XCode, select the target you'd like to build, select the active SDK (simulator or device), and then build and go.
