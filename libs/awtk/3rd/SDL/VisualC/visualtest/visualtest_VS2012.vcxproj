﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectName>visualtest</ProjectName>
    <RootNamespace>visualtest</RootNamespace>
    <ProjectGuid>{13DDF23A-4A8F-4AF9-9734-CC09D9157924}</ProjectGuid>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <PlatformToolset>v110</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v110</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <PlatformToolset>v110</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <PlatformToolset>v110</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC70.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC70.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC70.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC70.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.30319.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(SolutionDir)\$(Platform)\$(Configuration)\</OutDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(SolutionDir)\$(Platform)\$(Configuration)\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(Platform)\$(Configuration)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\</IntDir>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(SolutionDir)\$(Platform)\$(Configuration)\</OutDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(SolutionDir)\$(Platform)\$(Configuration)\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(Platform)\$(Configuration)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\</IntDir>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <TargetName>testharness</TargetName>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Midl>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>Win32</TargetEnvironment>
    </Midl>
    <ClCompile>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <AdditionalIncludeDirectories>..\..\include;..\..\visualtest\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <CompileAs>Default</CompileAs>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <SubSystem>Windows</SubSystem>
    </Link>
    <PostBuildEvent>
      <Command>copy "$(SolutionDir)..\test\icon.bmp" "$(ProjectDir)icon.bmp"
copy "$(SolutionDir)\$(Platform)\$(Configuration)\testsprite2.exe" "$(ProjectDir)"
copy "$(SolutionDir)\$(Platform)\$(Configuration)\testquit.exe" "$(ProjectDir)"
copy /y "$(SolutionDir)..\visualtest\*.config" "$(ProjectDir)"
copy /y "$(SolutionDir)..\visualtest\*.parameters" "$(ProjectDir)"
copy /y "$(SolutionDir)..\visualtest\*.actions" "$(ProjectDir)"
copy /y "$(SolutionDir)..\visualtest\unittest\*.config" "$(ProjectDir)"
copy /y "$(SolutionDir)..\visualtest\unittest\*.parameters" "$(ProjectDir)"
copy /y "$(SolutionDir)..\visualtest\unittest\*.actions" "$(ProjectDir)"</Command>
    </PostBuildEvent>
    <PostBuildEvent>
      <Message>Copy data files</Message>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Midl>
    <ClCompile>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <AdditionalIncludeDirectories>..\..\include;..\..\visualtest\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <CompileAs>Default</CompileAs>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <SubSystem>Windows</SubSystem>
    </Link>
    <PostBuildEvent>
      <Command>copy "$(SolutionDir)..\test\icon.bmp" "$(ProjectDir)icon.bmp"
copy "$(SolutionDir)\$(Platform)\$(Configuration)\testsprite2.exe" "$(ProjectDir)"
copy "$(SolutionDir)\$(Platform)\$(Configuration)\testquit.exe" "$(ProjectDir)"
copy /y "$(SolutionDir)..\visualtest\*.config" "$(ProjectDir)"
copy /y "$(SolutionDir)..\visualtest\*.parameters" "$(ProjectDir)"
copy /y "$(SolutionDir)..\visualtest\*.actions" "$(ProjectDir)"
copy /y "$(SolutionDir)..\visualtest\unittest\*.config" "$(ProjectDir)"
copy /y "$(SolutionDir)..\visualtest\unittest\*.parameters" "$(ProjectDir)"
copy /y "$(SolutionDir)..\visualtest\unittest\*.actions" "$(ProjectDir)"</Command>
    </PostBuildEvent>
    <PostBuildEvent>
      <Message>Copy data files</Message>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>Win32</TargetEnvironment>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>..\..\include;..\..\visualtest\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_CRT_SECURE_NO_DEPRECATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <CompileAs>Default</CompileAs>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;Shlwapi.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <ImageHasSafeExceptionHandlers>false</ImageHasSafeExceptionHandlers>
    </Link>
    <PostBuildEvent>
      <Command>copy "$(SolutionDir)..\test\icon.bmp" "$(ProjectDir)icon.bmp"
copy "$(SolutionDir)\$(Platform)\$(Configuration)\testsprite2.exe" "$(ProjectDir)"
copy "$(SolutionDir)\$(Platform)\$(Configuration)\testquit.exe" "$(ProjectDir)"
copy /y "$(SolutionDir)..\visualtest\*.config" "$(ProjectDir)"
copy /y "$(SolutionDir)..\visualtest\*.parameters" "$(ProjectDir)"
copy /y "$(SolutionDir)..\visualtest\*.actions" "$(ProjectDir)"
copy /y "$(SolutionDir)..\visualtest\unittest\*.config" "$(ProjectDir)"
copy /y "$(SolutionDir)..\visualtest\unittest\*.parameters" "$(ProjectDir)"
copy /y "$(SolutionDir)..\visualtest\unittest\*.actions" "$(ProjectDir)"</Command>
    </PostBuildEvent>
    <PostBuildEvent>
      <Message>Copy data files</Message>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>..\..\include;..\..\visualtest\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <CompileAs>Default</CompileAs>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <ImageHasSafeExceptionHandlers>false</ImageHasSafeExceptionHandlers>
    </Link>
    <PostBuildEvent>
      <Command>copy "$(SolutionDir)..\test\icon.bmp" "$(ProjectDir)icon.bmp"
copy "$(SolutionDir)\$(Platform)\$(Configuration)\testsprite2.exe" "$(ProjectDir)"
copy "$(SolutionDir)\$(Platform)\$(Configuration)\testquit.exe" "$(ProjectDir)"
copy /y "$(SolutionDir)..\visualtest\*.config" "$(ProjectDir)"
copy /y "$(SolutionDir)..\visualtest\*.parameters" "$(ProjectDir)"
copy /y "$(SolutionDir)..\visualtest\*.actions" "$(ProjectDir)"
copy /y "$(SolutionDir)..\visualtest\unittest\*.config" "$(ProjectDir)"
copy /y "$(SolutionDir)..\visualtest\unittest\*.parameters" "$(ProjectDir)"
copy /y "$(SolutionDir)..\visualtest\unittest\*.actions" "$(ProjectDir)"</Command>
    </PostBuildEvent>
    <PostBuildEvent>
      <Message>Copy data files</Message>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\..\visualtest\include\SDL_visualtest_action_configparser.h" />
    <ClInclude Include="..\..\visualtest\include\SDL_visualtest_exhaustive_variator.h" />
    <ClInclude Include="..\..\visualtest\include\SDL_visualtest_harness_argparser.h" />
    <ClInclude Include="..\..\visualtest\include\SDL_visualtest_mischelper.h" />
    <ClInclude Include="..\..\visualtest\include\SDL_visualtest_parsehelper.h" />
    <ClInclude Include="..\..\visualtest\include\SDL_visualtest_process.h" />
    <ClInclude Include="..\..\visualtest\include\SDL_visualtest_random_variator.h" />
    <ClInclude Include="..\..\visualtest\include\SDL_visualtest_rwhelper.h" />
    <ClInclude Include="..\..\visualtest\include\SDL_visualtest_screenshot.h" />
    <ClInclude Include="..\..\visualtest\include\SDL_visualtest_sut_configparser.h" />
    <ClInclude Include="..\..\visualtest\include\SDL_visualtest_variators.h" />
    <ClInclude Include="..\..\visualtest\include\SDL_visualtest_variator_common.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\visualtest\src\action_configparser.c" />
    <ClCompile Include="..\..\visualtest\src\harness_argparser.c" />
    <ClCompile Include="..\..\visualtest\src\mischelper.c" />
    <ClCompile Include="..\..\visualtest\src\parsehelper.c" />
    <ClCompile Include="..\..\visualtest\src\rwhelper.c" />
    <ClCompile Include="..\..\visualtest\src\screenshot.c" />
    <ClCompile Include="..\..\visualtest\src\sut_configparser.c" />
    <ClCompile Include="..\..\visualtest\src\testharness.c" />
    <ClCompile Include="..\..\visualtest\src\variators.c" />
    <ClCompile Include="..\..\visualtest\src\variator_exhaustive.c" />
    <ClCompile Include="..\..\visualtest\src\variator_common.c" />
    <ClCompile Include="..\..\visualtest\src\variator_random.c" />
    <ClCompile Include="..\..\visualtest\src\windows\windows_process.c" />
    <ClCompile Include="..\..\visualtest\src\windows\windows_screenshot.c" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\SDLmain\SDLmain_VS2012.vcxproj">
      <Project>{da956fd3-e142-46f2-9dd5-c78bebb56b7a}</Project>
    </ProjectReference>
    <ProjectReference Include="..\SDLtest\SDLtest_VS2012.vcxproj">
      <Project>{da956fd3-e143-46f2-9fe5-c77bebc56b1a}</Project>
    </ProjectReference>
    <ProjectReference Include="..\SDL\SDL_VS2012.vcxproj">
      <Project>{81ce8daf-ebb2-4761-8e45-b71abcca8c68}</Project>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>