/* Generated by wayland-scanner 1.14.0 */

#ifndef XDG_SHELL_UNSTABLE_V6_CLIENT_PROTOCOL_H
#define XDG_SHELL_UNSTABLE_V6_CLIENT_PROTOCOL_H

#include <stdint.h>
#include <stddef.h>
#include "wayland-client.h"

#ifdef  __cplusplus
extern "C" {
#endif

/**
 * @page page_xdg_shell_unstable_v6 The xdg_shell_unstable_v6 protocol
 * @section page_ifaces_xdg_shell_unstable_v6 Interfaces
 * - @subpage page_iface_zxdg_shell_v6 - create desktop-style surfaces
 * - @subpage page_iface_zxdg_positioner_v6 - child surface positioner
 * - @subpage page_iface_zxdg_surface_v6 - desktop user interface surface base interface
 * - @subpage page_iface_zxdg_toplevel_v6 - toplevel surface
 * - @subpage page_iface_zxdg_popup_v6 - short-lived, popup surfaces for menus
 * @section page_copyright_xdg_shell_unstable_v6 Copyright
 * <pre>
 *
 * Copyright © 2008-2013 <PERSON><PERSON>
 * Copyright © 2013      <PERSON>
 * Copyright © 2013      Jasper St. Pierre
 * Copyright © 2010-2013 Intel Corporation
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice (including the next
 * paragraph) shall be included in all copies or substantial portions of the
 * Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
 * THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.
 * </pre>
 */
struct wl_output;
struct wl_seat;
struct wl_surface;
struct zxdg_popup_v6;
struct zxdg_positioner_v6;
struct zxdg_shell_v6;
struct zxdg_surface_v6;
struct zxdg_toplevel_v6;

/**
 * @page page_iface_zxdg_shell_v6 zxdg_shell_v6
 * @section page_iface_zxdg_shell_v6_desc Description
 *
 * xdg_shell allows clients to turn a wl_surface into a "real window"
 * which can be dragged, resized, stacked, and moved around by the
 * user. Everything about this interface is suited towards traditional
 * desktop environments.
 * @section page_iface_zxdg_shell_v6_api API
 * See @ref iface_zxdg_shell_v6.
 */
/**
 * @defgroup iface_zxdg_shell_v6 The zxdg_shell_v6 interface
 *
 * xdg_shell allows clients to turn a wl_surface into a "real window"
 * which can be dragged, resized, stacked, and moved around by the
 * user. Everything about this interface is suited towards traditional
 * desktop environments.
 */
extern const struct wl_interface zxdg_shell_v6_interface;
/**
 * @page page_iface_zxdg_positioner_v6 zxdg_positioner_v6
 * @section page_iface_zxdg_positioner_v6_desc Description
 *
 * The xdg_positioner provides a collection of rules for the placement of a
 * child surface relative to a parent surface. Rules can be defined to ensure
 * the child surface remains within the visible area's borders, and to
 * specify how the child surface changes its position, such as sliding along
 * an axis, or flipping around a rectangle. These positioner-created rules are
 * constrained by the requirement that a child surface must intersect with or
 * be at least partially adjacent to its parent surface.
 *
 * See the various requests for details about possible rules.
 *
 * At the time of the request, the compositor makes a copy of the rules
 * specified by the xdg_positioner. Thus, after the request is complete the
 * xdg_positioner object can be destroyed or reused; further changes to the
 * object will have no effect on previous usages.
 *
 * For an xdg_positioner object to be considered complete, it must have a
 * non-zero size set by set_size, and a non-zero anchor rectangle set by
 * set_anchor_rect. Passing an incomplete xdg_positioner object when
 * positioning a surface raises an error.
 * @section page_iface_zxdg_positioner_v6_api API
 * See @ref iface_zxdg_positioner_v6.
 */
/**
 * @defgroup iface_zxdg_positioner_v6 The zxdg_positioner_v6 interface
 *
 * The xdg_positioner provides a collection of rules for the placement of a
 * child surface relative to a parent surface. Rules can be defined to ensure
 * the child surface remains within the visible area's borders, and to
 * specify how the child surface changes its position, such as sliding along
 * an axis, or flipping around a rectangle. These positioner-created rules are
 * constrained by the requirement that a child surface must intersect with or
 * be at least partially adjacent to its parent surface.
 *
 * See the various requests for details about possible rules.
 *
 * At the time of the request, the compositor makes a copy of the rules
 * specified by the xdg_positioner. Thus, after the request is complete the
 * xdg_positioner object can be destroyed or reused; further changes to the
 * object will have no effect on previous usages.
 *
 * For an xdg_positioner object to be considered complete, it must have a
 * non-zero size set by set_size, and a non-zero anchor rectangle set by
 * set_anchor_rect. Passing an incomplete xdg_positioner object when
 * positioning a surface raises an error.
 */
extern const struct wl_interface zxdg_positioner_v6_interface;
/**
 * @page page_iface_zxdg_surface_v6 zxdg_surface_v6
 * @section page_iface_zxdg_surface_v6_desc Description
 *
 * An interface that may be implemented by a wl_surface, for
 * implementations that provide a desktop-style user interface.
 *
 * It provides a base set of functionality required to construct user
 * interface elements requiring management by the compositor, such as
 * toplevel windows, menus, etc. The types of functionality are split into
 * xdg_surface roles.
 *
 * Creating an xdg_surface does not set the role for a wl_surface. In order
 * to map an xdg_surface, the client must create a role-specific object
 * using, e.g., get_toplevel, get_popup. The wl_surface for any given
 * xdg_surface can have at most one role, and may not be assigned any role
 * not based on xdg_surface.
 *
 * A role must be assigned before any other requests are made to the
 * xdg_surface object.
 *
 * The client must call wl_surface.commit on the corresponding wl_surface
 * for the xdg_surface state to take effect.
 *
 * Creating an xdg_surface from a wl_surface which has a buffer attached or
 * committed is a client error, and any attempts by a client to attach or
 * manipulate a buffer prior to the first xdg_surface.configure call must
 * also be treated as errors.
 *
 * For a surface to be mapped by the compositor, the following conditions
 * must be met: (1) the client has assigned a xdg_surface based role to the
 * surface, (2) the client has set and committed the xdg_surface state and
 * the role dependent state to the surface and (3) the client has committed a
 * buffer to the surface.
 * @section page_iface_zxdg_surface_v6_api API
 * See @ref iface_zxdg_surface_v6.
 */
/**
 * @defgroup iface_zxdg_surface_v6 The zxdg_surface_v6 interface
 *
 * An interface that may be implemented by a wl_surface, for
 * implementations that provide a desktop-style user interface.
 *
 * It provides a base set of functionality required to construct user
 * interface elements requiring management by the compositor, such as
 * toplevel windows, menus, etc. The types of functionality are split into
 * xdg_surface roles.
 *
 * Creating an xdg_surface does not set the role for a wl_surface. In order
 * to map an xdg_surface, the client must create a role-specific object
 * using, e.g., get_toplevel, get_popup. The wl_surface for any given
 * xdg_surface can have at most one role, and may not be assigned any role
 * not based on xdg_surface.
 *
 * A role must be assigned before any other requests are made to the
 * xdg_surface object.
 *
 * The client must call wl_surface.commit on the corresponding wl_surface
 * for the xdg_surface state to take effect.
 *
 * Creating an xdg_surface from a wl_surface which has a buffer attached or
 * committed is a client error, and any attempts by a client to attach or
 * manipulate a buffer prior to the first xdg_surface.configure call must
 * also be treated as errors.
 *
 * For a surface to be mapped by the compositor, the following conditions
 * must be met: (1) the client has assigned a xdg_surface based role to the
 * surface, (2) the client has set and committed the xdg_surface state and
 * the role dependent state to the surface and (3) the client has committed a
 * buffer to the surface.
 */
extern const struct wl_interface zxdg_surface_v6_interface;
/**
 * @page page_iface_zxdg_toplevel_v6 zxdg_toplevel_v6
 * @section page_iface_zxdg_toplevel_v6_desc Description
 *
 * This interface defines an xdg_surface role which allows a surface to,
 * among other things, set window-like properties such as maximize,
 * fullscreen, and minimize, set application-specific metadata like title and
 * id, and well as trigger user interactive operations such as interactive
 * resize and move.
 * @section page_iface_zxdg_toplevel_v6_api API
 * See @ref iface_zxdg_toplevel_v6.
 */
/**
 * @defgroup iface_zxdg_toplevel_v6 The zxdg_toplevel_v6 interface
 *
 * This interface defines an xdg_surface role which allows a surface to,
 * among other things, set window-like properties such as maximize,
 * fullscreen, and minimize, set application-specific metadata like title and
 * id, and well as trigger user interactive operations such as interactive
 * resize and move.
 */
extern const struct wl_interface zxdg_toplevel_v6_interface;
/**
 * @page page_iface_zxdg_popup_v6 zxdg_popup_v6
 * @section page_iface_zxdg_popup_v6_desc Description
 *
 * A popup surface is a short-lived, temporary surface. It can be used to
 * implement for example menus, popovers, tooltips and other similar user
 * interface concepts.
 *
 * A popup can be made to take an explicit grab. See xdg_popup.grab for
 * details.
 *
 * When the popup is dismissed, a popup_done event will be sent out, and at
 * the same time the surface will be unmapped. See the xdg_popup.popup_done
 * event for details.
 *
 * Explicitly destroying the xdg_popup object will also dismiss the popup and
 * unmap the surface. Clients that want to dismiss the popup when another
 * surface of their own is clicked should dismiss the popup using the destroy
 * request.
 *
 * The parent surface must have either the xdg_toplevel or xdg_popup surface
 * role.
 *
 * A newly created xdg_popup will be stacked on top of all previously created
 * xdg_popup surfaces associated with the same xdg_toplevel.
 *
 * The parent of an xdg_popup must be mapped (see the xdg_surface
 * description) before the xdg_popup itself.
 *
 * The x and y arguments passed when creating the popup object specify
 * where the top left of the popup should be placed, relative to the
 * local surface coordinates of the parent surface. See
 * xdg_surface.get_popup. An xdg_popup must intersect with or be at least
 * partially adjacent to its parent surface.
 *
 * The client must call wl_surface.commit on the corresponding wl_surface
 * for the xdg_popup state to take effect.
 * @section page_iface_zxdg_popup_v6_api API
 * See @ref iface_zxdg_popup_v6.
 */
/**
 * @defgroup iface_zxdg_popup_v6 The zxdg_popup_v6 interface
 *
 * A popup surface is a short-lived, temporary surface. It can be used to
 * implement for example menus, popovers, tooltips and other similar user
 * interface concepts.
 *
 * A popup can be made to take an explicit grab. See xdg_popup.grab for
 * details.
 *
 * When the popup is dismissed, a popup_done event will be sent out, and at
 * the same time the surface will be unmapped. See the xdg_popup.popup_done
 * event for details.
 *
 * Explicitly destroying the xdg_popup object will also dismiss the popup and
 * unmap the surface. Clients that want to dismiss the popup when another
 * surface of their own is clicked should dismiss the popup using the destroy
 * request.
 *
 * The parent surface must have either the xdg_toplevel or xdg_popup surface
 * role.
 *
 * A newly created xdg_popup will be stacked on top of all previously created
 * xdg_popup surfaces associated with the same xdg_toplevel.
 *
 * The parent of an xdg_popup must be mapped (see the xdg_surface
 * description) before the xdg_popup itself.
 *
 * The x and y arguments passed when creating the popup object specify
 * where the top left of the popup should be placed, relative to the
 * local surface coordinates of the parent surface. See
 * xdg_surface.get_popup. An xdg_popup must intersect with or be at least
 * partially adjacent to its parent surface.
 *
 * The client must call wl_surface.commit on the corresponding wl_surface
 * for the xdg_popup state to take effect.
 */
extern const struct wl_interface zxdg_popup_v6_interface;

#ifndef ZXDG_SHELL_V6_ERROR_ENUM
#define ZXDG_SHELL_V6_ERROR_ENUM
enum zxdg_shell_v6_error {
	/**
	 * given wl_surface has another role
	 */
	ZXDG_SHELL_V6_ERROR_ROLE = 0,
	/**
	 * xdg_shell was destroyed before children
	 */
	ZXDG_SHELL_V6_ERROR_DEFUNCT_SURFACES = 1,
	/**
	 * the client tried to map or destroy a non-topmost popup
	 */
	ZXDG_SHELL_V6_ERROR_NOT_THE_TOPMOST_POPUP = 2,
	/**
	 * the client specified an invalid popup parent surface
	 */
	ZXDG_SHELL_V6_ERROR_INVALID_POPUP_PARENT = 3,
	/**
	 * the client provided an invalid surface state
	 */
	ZXDG_SHELL_V6_ERROR_INVALID_SURFACE_STATE = 4,
	/**
	 * the client provided an invalid positioner
	 */
	ZXDG_SHELL_V6_ERROR_INVALID_POSITIONER = 5,
};
#endif /* ZXDG_SHELL_V6_ERROR_ENUM */

/**
 * @ingroup iface_zxdg_shell_v6
 * @struct zxdg_shell_v6_listener
 */
struct zxdg_shell_v6_listener {
	/**
	 * check if the client is alive
	 *
	 * The ping event asks the client if it's still alive. Pass the
	 * serial specified in the event back to the compositor by sending
	 * a "pong" request back with the specified serial. See
	 * xdg_shell.ping.
	 *
	 * Compositors can use this to determine if the client is still
	 * alive. It's unspecified what will happen if the client doesn't
	 * respond to the ping request, or in what timeframe. Clients
	 * should try to respond in a reasonable amount of time.
	 *
	 * A compositor is free to ping in any way it wants, but a client
	 * must always respond to any xdg_shell object it created.
	 * @param serial pass this to the pong request
	 */
	void (*ping)(void *data,
		     struct zxdg_shell_v6 *zxdg_shell_v6,
		     uint32_t serial);
};

/**
 * @ingroup iface_zxdg_shell_v6
 */
static inline int
zxdg_shell_v6_add_listener(struct zxdg_shell_v6 *zxdg_shell_v6,
			   const struct zxdg_shell_v6_listener *listener, void *data)
{
	return wl_proxy_add_listener((struct wl_proxy *) zxdg_shell_v6,
				     (void (**)(void)) listener, data);
}

#define ZXDG_SHELL_V6_DESTROY 0
#define ZXDG_SHELL_V6_CREATE_POSITIONER 1
#define ZXDG_SHELL_V6_GET_XDG_SURFACE 2
#define ZXDG_SHELL_V6_PONG 3

/**
 * @ingroup iface_zxdg_shell_v6
 */
#define ZXDG_SHELL_V6_PING_SINCE_VERSION 1

/**
 * @ingroup iface_zxdg_shell_v6
 */
#define ZXDG_SHELL_V6_DESTROY_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_shell_v6
 */
#define ZXDG_SHELL_V6_CREATE_POSITIONER_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_shell_v6
 */
#define ZXDG_SHELL_V6_GET_XDG_SURFACE_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_shell_v6
 */
#define ZXDG_SHELL_V6_PONG_SINCE_VERSION 1

/** @ingroup iface_zxdg_shell_v6 */
static inline void
zxdg_shell_v6_set_user_data(struct zxdg_shell_v6 *zxdg_shell_v6, void *user_data)
{
	wl_proxy_set_user_data((struct wl_proxy *) zxdg_shell_v6, user_data);
}

/** @ingroup iface_zxdg_shell_v6 */
static inline void *
zxdg_shell_v6_get_user_data(struct zxdg_shell_v6 *zxdg_shell_v6)
{
	return wl_proxy_get_user_data((struct wl_proxy *) zxdg_shell_v6);
}

static inline uint32_t
zxdg_shell_v6_get_version(struct zxdg_shell_v6 *zxdg_shell_v6)
{
	return wl_proxy_get_version((struct wl_proxy *) zxdg_shell_v6);
}

/**
 * @ingroup iface_zxdg_shell_v6
 *
 * Destroy this xdg_shell object.
 *
 * Destroying a bound xdg_shell object while there are surfaces
 * still alive created by this xdg_shell object instance is illegal
 * and will result in a protocol error.
 */
static inline void
zxdg_shell_v6_destroy(struct zxdg_shell_v6 *zxdg_shell_v6)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_shell_v6,
			 ZXDG_SHELL_V6_DESTROY);

	wl_proxy_destroy((struct wl_proxy *) zxdg_shell_v6);
}

/**
 * @ingroup iface_zxdg_shell_v6
 *
 * Create a positioner object. A positioner object is used to position
 * surfaces relative to some parent surface. See the interface description
 * and xdg_surface.get_popup for details.
 */
static inline struct zxdg_positioner_v6 *
zxdg_shell_v6_create_positioner(struct zxdg_shell_v6 *zxdg_shell_v6)
{
	struct wl_proxy *id;

	id = wl_proxy_marshal_constructor((struct wl_proxy *) zxdg_shell_v6,
			 ZXDG_SHELL_V6_CREATE_POSITIONER, &zxdg_positioner_v6_interface, NULL);

	return (struct zxdg_positioner_v6 *) id;
}

/**
 * @ingroup iface_zxdg_shell_v6
 *
 * This creates an xdg_surface for the given surface. While xdg_surface
 * itself is not a role, the corresponding surface may only be assigned
 * a role extending xdg_surface, such as xdg_toplevel or xdg_popup.
 *
 * This creates an xdg_surface for the given surface. An xdg_surface is
 * used as basis to define a role to a given surface, such as xdg_toplevel
 * or xdg_popup. It also manages functionality shared between xdg_surface
 * based surface roles.
 *
 * See the documentation of xdg_surface for more details about what an
 * xdg_surface is and how it is used.
 */
static inline struct zxdg_surface_v6 *
zxdg_shell_v6_get_xdg_surface(struct zxdg_shell_v6 *zxdg_shell_v6, struct wl_surface *surface)
{
	struct wl_proxy *id;

	id = wl_proxy_marshal_constructor((struct wl_proxy *) zxdg_shell_v6,
			 ZXDG_SHELL_V6_GET_XDG_SURFACE, &zxdg_surface_v6_interface, NULL, surface);

	return (struct zxdg_surface_v6 *) id;
}

/**
 * @ingroup iface_zxdg_shell_v6
 *
 * A client must respond to a ping event with a pong request or
 * the client may be deemed unresponsive. See xdg_shell.ping.
 */
static inline void
zxdg_shell_v6_pong(struct zxdg_shell_v6 *zxdg_shell_v6, uint32_t serial)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_shell_v6,
			 ZXDG_SHELL_V6_PONG, serial);
}

#ifndef ZXDG_POSITIONER_V6_ERROR_ENUM
#define ZXDG_POSITIONER_V6_ERROR_ENUM
enum zxdg_positioner_v6_error {
	/**
	 * invalid input provided
	 */
	ZXDG_POSITIONER_V6_ERROR_INVALID_INPUT = 0,
};
#endif /* ZXDG_POSITIONER_V6_ERROR_ENUM */

#ifndef ZXDG_POSITIONER_V6_ANCHOR_ENUM
#define ZXDG_POSITIONER_V6_ANCHOR_ENUM
enum zxdg_positioner_v6_anchor {
	/**
	 * the center of the anchor rectangle
	 */
	ZXDG_POSITIONER_V6_ANCHOR_NONE = 0,
	/**
	 * the top edge of the anchor rectangle
	 */
	ZXDG_POSITIONER_V6_ANCHOR_TOP = 1,
	/**
	 * the bottom edge of the anchor rectangle
	 */
	ZXDG_POSITIONER_V6_ANCHOR_BOTTOM = 2,
	/**
	 * the left edge of the anchor rectangle
	 */
	ZXDG_POSITIONER_V6_ANCHOR_LEFT = 4,
	/**
	 * the right edge of the anchor rectangle
	 */
	ZXDG_POSITIONER_V6_ANCHOR_RIGHT = 8,
};
#endif /* ZXDG_POSITIONER_V6_ANCHOR_ENUM */

#ifndef ZXDG_POSITIONER_V6_GRAVITY_ENUM
#define ZXDG_POSITIONER_V6_GRAVITY_ENUM
enum zxdg_positioner_v6_gravity {
	/**
	 * center over the anchor edge
	 */
	ZXDG_POSITIONER_V6_GRAVITY_NONE = 0,
	/**
	 * position above the anchor edge
	 */
	ZXDG_POSITIONER_V6_GRAVITY_TOP = 1,
	/**
	 * position below the anchor edge
	 */
	ZXDG_POSITIONER_V6_GRAVITY_BOTTOM = 2,
	/**
	 * position to the left of the anchor edge
	 */
	ZXDG_POSITIONER_V6_GRAVITY_LEFT = 4,
	/**
	 * position to the right of the anchor edge
	 */
	ZXDG_POSITIONER_V6_GRAVITY_RIGHT = 8,
};
#endif /* ZXDG_POSITIONER_V6_GRAVITY_ENUM */

#ifndef ZXDG_POSITIONER_V6_CONSTRAINT_ADJUSTMENT_ENUM
#define ZXDG_POSITIONER_V6_CONSTRAINT_ADJUSTMENT_ENUM
/**
 * @ingroup iface_zxdg_positioner_v6
 * vertically resize the surface
 *
 * Resize the surface vertically so that it is completely unconstrained.
 */
enum zxdg_positioner_v6_constraint_adjustment {
	ZXDG_POSITIONER_V6_CONSTRAINT_ADJUSTMENT_NONE = 0,
	ZXDG_POSITIONER_V6_CONSTRAINT_ADJUSTMENT_SLIDE_X = 1,
	ZXDG_POSITIONER_V6_CONSTRAINT_ADJUSTMENT_SLIDE_Y = 2,
	ZXDG_POSITIONER_V6_CONSTRAINT_ADJUSTMENT_FLIP_X = 4,
	ZXDG_POSITIONER_V6_CONSTRAINT_ADJUSTMENT_FLIP_Y = 8,
	ZXDG_POSITIONER_V6_CONSTRAINT_ADJUSTMENT_RESIZE_X = 16,
	ZXDG_POSITIONER_V6_CONSTRAINT_ADJUSTMENT_RESIZE_Y = 32,
};
#endif /* ZXDG_POSITIONER_V6_CONSTRAINT_ADJUSTMENT_ENUM */

#define ZXDG_POSITIONER_V6_DESTROY 0
#define ZXDG_POSITIONER_V6_SET_SIZE 1
#define ZXDG_POSITIONER_V6_SET_ANCHOR_RECT 2
#define ZXDG_POSITIONER_V6_SET_ANCHOR 3
#define ZXDG_POSITIONER_V6_SET_GRAVITY 4
#define ZXDG_POSITIONER_V6_SET_CONSTRAINT_ADJUSTMENT 5
#define ZXDG_POSITIONER_V6_SET_OFFSET 6


/**
 * @ingroup iface_zxdg_positioner_v6
 */
#define ZXDG_POSITIONER_V6_DESTROY_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_positioner_v6
 */
#define ZXDG_POSITIONER_V6_SET_SIZE_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_positioner_v6
 */
#define ZXDG_POSITIONER_V6_SET_ANCHOR_RECT_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_positioner_v6
 */
#define ZXDG_POSITIONER_V6_SET_ANCHOR_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_positioner_v6
 */
#define ZXDG_POSITIONER_V6_SET_GRAVITY_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_positioner_v6
 */
#define ZXDG_POSITIONER_V6_SET_CONSTRAINT_ADJUSTMENT_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_positioner_v6
 */
#define ZXDG_POSITIONER_V6_SET_OFFSET_SINCE_VERSION 1

/** @ingroup iface_zxdg_positioner_v6 */
static inline void
zxdg_positioner_v6_set_user_data(struct zxdg_positioner_v6 *zxdg_positioner_v6, void *user_data)
{
	wl_proxy_set_user_data((struct wl_proxy *) zxdg_positioner_v6, user_data);
}

/** @ingroup iface_zxdg_positioner_v6 */
static inline void *
zxdg_positioner_v6_get_user_data(struct zxdg_positioner_v6 *zxdg_positioner_v6)
{
	return wl_proxy_get_user_data((struct wl_proxy *) zxdg_positioner_v6);
}

static inline uint32_t
zxdg_positioner_v6_get_version(struct zxdg_positioner_v6 *zxdg_positioner_v6)
{
	return wl_proxy_get_version((struct wl_proxy *) zxdg_positioner_v6);
}

/**
 * @ingroup iface_zxdg_positioner_v6
 *
 * Notify the compositor that the xdg_positioner will no longer be used.
 */
static inline void
zxdg_positioner_v6_destroy(struct zxdg_positioner_v6 *zxdg_positioner_v6)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_positioner_v6,
			 ZXDG_POSITIONER_V6_DESTROY);

	wl_proxy_destroy((struct wl_proxy *) zxdg_positioner_v6);
}

/**
 * @ingroup iface_zxdg_positioner_v6
 *
 * Set the size of the surface that is to be positioned with the positioner
 * object. The size is in surface-local coordinates and corresponds to the
 * window geometry. See xdg_surface.set_window_geometry.
 *
 * If a zero or negative size is set the invalid_input error is raised.
 */
static inline void
zxdg_positioner_v6_set_size(struct zxdg_positioner_v6 *zxdg_positioner_v6, int32_t width, int32_t height)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_positioner_v6,
			 ZXDG_POSITIONER_V6_SET_SIZE, width, height);
}

/**
 * @ingroup iface_zxdg_positioner_v6
 *
 * Specify the anchor rectangle within the parent surface that the child
 * surface will be placed relative to. The rectangle is relative to the
 * window geometry as defined by xdg_surface.set_window_geometry of the
 * parent surface. The rectangle must be at least 1x1 large.
 *
 * When the xdg_positioner object is used to position a child surface, the
 * anchor rectangle may not extend outside the window geometry of the
 * positioned child's parent surface.
 *
 * If a zero or negative size is set the invalid_input error is raised.
 */
static inline void
zxdg_positioner_v6_set_anchor_rect(struct zxdg_positioner_v6 *zxdg_positioner_v6, int32_t x, int32_t y, int32_t width, int32_t height)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_positioner_v6,
			 ZXDG_POSITIONER_V6_SET_ANCHOR_RECT, x, y, width, height);
}

/**
 * @ingroup iface_zxdg_positioner_v6
 *
 * Defines a set of edges for the anchor rectangle. These are used to
 * derive an anchor point that the child surface will be positioned
 * relative to. If two orthogonal edges are specified (e.g. 'top' and
 * 'left'), then the anchor point will be the intersection of the edges
 * (e.g. the top left position of the rectangle); otherwise, the derived
 * anchor point will be centered on the specified edge, or in the center of
 * the anchor rectangle if no edge is specified.
 *
 * If two parallel anchor edges are specified (e.g. 'left' and 'right'),
 * the invalid_input error is raised.
 */
static inline void
zxdg_positioner_v6_set_anchor(struct zxdg_positioner_v6 *zxdg_positioner_v6, uint32_t anchor)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_positioner_v6,
			 ZXDG_POSITIONER_V6_SET_ANCHOR, anchor);
}

/**
 * @ingroup iface_zxdg_positioner_v6
 *
 * Defines in what direction a surface should be positioned, relative to
 * the anchor point of the parent surface. If two orthogonal gravities are
 * specified (e.g. 'bottom' and 'right'), then the child surface will be
 * placed in the specified direction; otherwise, the child surface will be
 * centered over the anchor point on any axis that had no gravity
 * specified.
 *
 * If two parallel gravities are specified (e.g. 'left' and 'right'), the
 * invalid_input error is raised.
 */
static inline void
zxdg_positioner_v6_set_gravity(struct zxdg_positioner_v6 *zxdg_positioner_v6, uint32_t gravity)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_positioner_v6,
			 ZXDG_POSITIONER_V6_SET_GRAVITY, gravity);
}

/**
 * @ingroup iface_zxdg_positioner_v6
 *
 * Specify how the window should be positioned if the originally intended
 * position caused the surface to be constrained, meaning at least
 * partially outside positioning boundaries set by the compositor. The
 * adjustment is set by constructing a bitmask describing the adjustment to
 * be made when the surface is constrained on that axis.
 *
 * If no bit for one axis is set, the compositor will assume that the child
 * surface should not change its position on that axis when constrained.
 *
 * If more than one bit for one axis is set, the order of how adjustments
 * are applied is specified in the corresponding adjustment descriptions.
 *
 * The default adjustment is none.
 */
static inline void
zxdg_positioner_v6_set_constraint_adjustment(struct zxdg_positioner_v6 *zxdg_positioner_v6, uint32_t constraint_adjustment)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_positioner_v6,
			 ZXDG_POSITIONER_V6_SET_CONSTRAINT_ADJUSTMENT, constraint_adjustment);
}

/**
 * @ingroup iface_zxdg_positioner_v6
 *
 * Specify the surface position offset relative to the position of the
 * anchor on the anchor rectangle and the anchor on the surface. For
 * example if the anchor of the anchor rectangle is at (x, y), the surface
 * has the gravity bottom|right, and the offset is (ox, oy), the calculated
 * surface position will be (x + ox, y + oy). The offset position of the
 * surface is the one used for constraint testing. See
 * set_constraint_adjustment.
 *
 * An example use case is placing a popup menu on top of a user interface
 * element, while aligning the user interface element of the parent surface
 * with some user interface element placed somewhere in the popup surface.
 */
static inline void
zxdg_positioner_v6_set_offset(struct zxdg_positioner_v6 *zxdg_positioner_v6, int32_t x, int32_t y)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_positioner_v6,
			 ZXDG_POSITIONER_V6_SET_OFFSET, x, y);
}

#ifndef ZXDG_SURFACE_V6_ERROR_ENUM
#define ZXDG_SURFACE_V6_ERROR_ENUM
enum zxdg_surface_v6_error {
	ZXDG_SURFACE_V6_ERROR_NOT_CONSTRUCTED = 1,
	ZXDG_SURFACE_V6_ERROR_ALREADY_CONSTRUCTED = 2,
	ZXDG_SURFACE_V6_ERROR_UNCONFIGURED_BUFFER = 3,
};
#endif /* ZXDG_SURFACE_V6_ERROR_ENUM */

/**
 * @ingroup iface_zxdg_surface_v6
 * @struct zxdg_surface_v6_listener
 */
struct zxdg_surface_v6_listener {
	/**
	 * suggest a surface change
	 *
	 * The configure event marks the end of a configure sequence. A
	 * configure sequence is a set of one or more events configuring
	 * the state of the xdg_surface, including the final
	 * xdg_surface.configure event.
	 *
	 * Where applicable, xdg_surface surface roles will during a
	 * configure sequence extend this event as a latched state sent as
	 * events before the xdg_surface.configure event. Such events
	 * should be considered to make up a set of atomically applied
	 * configuration states, where the xdg_surface.configure commits
	 * the accumulated state.
	 *
	 * Clients should arrange their surface for the new states, and
	 * then send an ack_configure request with the serial sent in this
	 * configure event at some point before committing the new surface.
	 *
	 * If the client receives multiple configure events before it can
	 * respond to one, it is free to discard all but the last event it
	 * received.
	 * @param serial serial of the configure event
	 */
	void (*configure)(void *data,
			  struct zxdg_surface_v6 *zxdg_surface_v6,
			  uint32_t serial);
};

/**
 * @ingroup iface_zxdg_surface_v6
 */
static inline int
zxdg_surface_v6_add_listener(struct zxdg_surface_v6 *zxdg_surface_v6,
			     const struct zxdg_surface_v6_listener *listener, void *data)
{
	return wl_proxy_add_listener((struct wl_proxy *) zxdg_surface_v6,
				     (void (**)(void)) listener, data);
}

#define ZXDG_SURFACE_V6_DESTROY 0
#define ZXDG_SURFACE_V6_GET_TOPLEVEL 1
#define ZXDG_SURFACE_V6_GET_POPUP 2
#define ZXDG_SURFACE_V6_SET_WINDOW_GEOMETRY 3
#define ZXDG_SURFACE_V6_ACK_CONFIGURE 4

/**
 * @ingroup iface_zxdg_surface_v6
 */
#define ZXDG_SURFACE_V6_CONFIGURE_SINCE_VERSION 1

/**
 * @ingroup iface_zxdg_surface_v6
 */
#define ZXDG_SURFACE_V6_DESTROY_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_surface_v6
 */
#define ZXDG_SURFACE_V6_GET_TOPLEVEL_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_surface_v6
 */
#define ZXDG_SURFACE_V6_GET_POPUP_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_surface_v6
 */
#define ZXDG_SURFACE_V6_SET_WINDOW_GEOMETRY_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_surface_v6
 */
#define ZXDG_SURFACE_V6_ACK_CONFIGURE_SINCE_VERSION 1

/** @ingroup iface_zxdg_surface_v6 */
static inline void
zxdg_surface_v6_set_user_data(struct zxdg_surface_v6 *zxdg_surface_v6, void *user_data)
{
	wl_proxy_set_user_data((struct wl_proxy *) zxdg_surface_v6, user_data);
}

/** @ingroup iface_zxdg_surface_v6 */
static inline void *
zxdg_surface_v6_get_user_data(struct zxdg_surface_v6 *zxdg_surface_v6)
{
	return wl_proxy_get_user_data((struct wl_proxy *) zxdg_surface_v6);
}

static inline uint32_t
zxdg_surface_v6_get_version(struct zxdg_surface_v6 *zxdg_surface_v6)
{
	return wl_proxy_get_version((struct wl_proxy *) zxdg_surface_v6);
}

/**
 * @ingroup iface_zxdg_surface_v6
 *
 * Destroy the xdg_surface object. An xdg_surface must only be destroyed
 * after its role object has been destroyed.
 */
static inline void
zxdg_surface_v6_destroy(struct zxdg_surface_v6 *zxdg_surface_v6)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_surface_v6,
			 ZXDG_SURFACE_V6_DESTROY);

	wl_proxy_destroy((struct wl_proxy *) zxdg_surface_v6);
}

/**
 * @ingroup iface_zxdg_surface_v6
 *
 * This creates an xdg_toplevel object for the given xdg_surface and gives
 * the associated wl_surface the xdg_toplevel role.
 *
 * See the documentation of xdg_toplevel for more details about what an
 * xdg_toplevel is and how it is used.
 */
static inline struct zxdg_toplevel_v6 *
zxdg_surface_v6_get_toplevel(struct zxdg_surface_v6 *zxdg_surface_v6)
{
	struct wl_proxy *id;

	id = wl_proxy_marshal_constructor((struct wl_proxy *) zxdg_surface_v6,
			 ZXDG_SURFACE_V6_GET_TOPLEVEL, &zxdg_toplevel_v6_interface, NULL);

	return (struct zxdg_toplevel_v6 *) id;
}

/**
 * @ingroup iface_zxdg_surface_v6
 *
 * This creates an xdg_popup object for the given xdg_surface and gives the
 * associated wl_surface the xdg_popup role.
 *
 * See the documentation of xdg_popup for more details about what an
 * xdg_popup is and how it is used.
 */
static inline struct zxdg_popup_v6 *
zxdg_surface_v6_get_popup(struct zxdg_surface_v6 *zxdg_surface_v6, struct zxdg_surface_v6 *parent, struct zxdg_positioner_v6 *positioner)
{
	struct wl_proxy *id;

	id = wl_proxy_marshal_constructor((struct wl_proxy *) zxdg_surface_v6,
			 ZXDG_SURFACE_V6_GET_POPUP, &zxdg_popup_v6_interface, NULL, parent, positioner);

	return (struct zxdg_popup_v6 *) id;
}

/**
 * @ingroup iface_zxdg_surface_v6
 *
 * The window geometry of a surface is its "visible bounds" from the
 * user's perspective. Client-side decorations often have invisible
 * portions like drop-shadows which should be ignored for the
 * purposes of aligning, placing and constraining windows.
 *
 * The window geometry is double buffered, and will be applied at the
 * time wl_surface.commit of the corresponding wl_surface is called.
 *
 * Once the window geometry of the surface is set, it is not possible to
 * unset it, and it will remain the same until set_window_geometry is
 * called again, even if a new subsurface or buffer is attached.
 *
 * If never set, the value is the full bounds of the surface,
 * including any subsurfaces. This updates dynamically on every
 * commit. This unset is meant for extremely simple clients.
 *
 * The arguments are given in the surface-local coordinate space of
 * the wl_surface associated with this xdg_surface.
 *
 * The width and height must be greater than zero. Setting an invalid size
 * will raise an error. When applied, the effective window geometry will be
 * the set window geometry clamped to the bounding rectangle of the
 * combined geometry of the surface of the xdg_surface and the associated
 * subsurfaces.
 */
static inline void
zxdg_surface_v6_set_window_geometry(struct zxdg_surface_v6 *zxdg_surface_v6, int32_t x, int32_t y, int32_t width, int32_t height)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_surface_v6,
			 ZXDG_SURFACE_V6_SET_WINDOW_GEOMETRY, x, y, width, height);
}

/**
 * @ingroup iface_zxdg_surface_v6
 *
 * When a configure event is received, if a client commits the
 * surface in response to the configure event, then the client
 * must make an ack_configure request sometime before the commit
 * request, passing along the serial of the configure event.
 *
 * For instance, for toplevel surfaces the compositor might use this
 * information to move a surface to the top left only when the client has
 * drawn itself for the maximized or fullscreen state.
 *
 * If the client receives multiple configure events before it
 * can respond to one, it only has to ack the last configure event.
 *
 * A client is not required to commit immediately after sending
 * an ack_configure request - it may even ack_configure several times
 * before its next surface commit.
 *
 * A client may send multiple ack_configure requests before committing, but
 * only the last request sent before a commit indicates which configure
 * event the client really is responding to.
 */
static inline void
zxdg_surface_v6_ack_configure(struct zxdg_surface_v6 *zxdg_surface_v6, uint32_t serial)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_surface_v6,
			 ZXDG_SURFACE_V6_ACK_CONFIGURE, serial);
}

#ifndef ZXDG_TOPLEVEL_V6_RESIZE_EDGE_ENUM
#define ZXDG_TOPLEVEL_V6_RESIZE_EDGE_ENUM
/**
 * @ingroup iface_zxdg_toplevel_v6
 * edge values for resizing
 *
 * These values are used to indicate which edge of a surface
 * is being dragged in a resize operation.
 */
enum zxdg_toplevel_v6_resize_edge {
	ZXDG_TOPLEVEL_V6_RESIZE_EDGE_NONE = 0,
	ZXDG_TOPLEVEL_V6_RESIZE_EDGE_TOP = 1,
	ZXDG_TOPLEVEL_V6_RESIZE_EDGE_BOTTOM = 2,
	ZXDG_TOPLEVEL_V6_RESIZE_EDGE_LEFT = 4,
	ZXDG_TOPLEVEL_V6_RESIZE_EDGE_TOP_LEFT = 5,
	ZXDG_TOPLEVEL_V6_RESIZE_EDGE_BOTTOM_LEFT = 6,
	ZXDG_TOPLEVEL_V6_RESIZE_EDGE_RIGHT = 8,
	ZXDG_TOPLEVEL_V6_RESIZE_EDGE_TOP_RIGHT = 9,
	ZXDG_TOPLEVEL_V6_RESIZE_EDGE_BOTTOM_RIGHT = 10,
};
#endif /* ZXDG_TOPLEVEL_V6_RESIZE_EDGE_ENUM */

#ifndef ZXDG_TOPLEVEL_V6_STATE_ENUM
#define ZXDG_TOPLEVEL_V6_STATE_ENUM
/**
 * @ingroup iface_zxdg_toplevel_v6
 * the surface is now activated
 *
 * Client window decorations should be painted as if the window is
 * active. Do not assume this means that the window actually has
 * keyboard or pointer focus.
 */
enum zxdg_toplevel_v6_state {
	/**
	 * the surface is maximized
	 */
	ZXDG_TOPLEVEL_V6_STATE_MAXIMIZED = 1,
	/**
	 * the surface is fullscreen
	 */
	ZXDG_TOPLEVEL_V6_STATE_FULLSCREEN = 2,
	/**
	 * the surface is being resized
	 */
	ZXDG_TOPLEVEL_V6_STATE_RESIZING = 3,
	/**
	 * the surface is now activated
	 */
	ZXDG_TOPLEVEL_V6_STATE_ACTIVATED = 4,
};
#endif /* ZXDG_TOPLEVEL_V6_STATE_ENUM */

/**
 * @ingroup iface_zxdg_toplevel_v6
 * @struct zxdg_toplevel_v6_listener
 */
struct zxdg_toplevel_v6_listener {
	/**
	 * suggest a surface change
	 *
	 * This configure event asks the client to resize its toplevel
	 * surface or to change its state. The configured state should not
	 * be applied immediately. See xdg_surface.configure for details.
	 *
	 * The width and height arguments specify a hint to the window
	 * about how its surface should be resized in window geometry
	 * coordinates. See set_window_geometry.
	 *
	 * If the width or height arguments are zero, it means the client
	 * should decide its own window dimension. This may happen when the
	 * compositor needs to configure the state of the surface but
	 * doesn't have any information about any previous or expected
	 * dimension.
	 *
	 * The states listed in the event specify how the width/height
	 * arguments should be interpreted, and possibly how it should be
	 * drawn.
	 *
	 * Clients must send an ack_configure in response to this event.
	 * See xdg_surface.configure and xdg_surface.ack_configure for
	 * details.
	 */
	void (*configure)(void *data,
			  struct zxdg_toplevel_v6 *zxdg_toplevel_v6,
			  int32_t width,
			  int32_t height,
			  struct wl_array *states);
	/**
	 * surface wants to be closed
	 *
	 * The close event is sent by the compositor when the user wants
	 * the surface to be closed. This should be equivalent to the user
	 * clicking the close button in client-side decorations, if your
	 * application has any.
	 *
	 * This is only a request that the user intends to close the
	 * window. The client may choose to ignore this request, or show a
	 * dialog to ask the user to save their data, etc.
	 */
	void (*close)(void *data,
		      struct zxdg_toplevel_v6 *zxdg_toplevel_v6);
};

/**
 * @ingroup iface_zxdg_toplevel_v6
 */
static inline int
zxdg_toplevel_v6_add_listener(struct zxdg_toplevel_v6 *zxdg_toplevel_v6,
			      const struct zxdg_toplevel_v6_listener *listener, void *data)
{
	return wl_proxy_add_listener((struct wl_proxy *) zxdg_toplevel_v6,
				     (void (**)(void)) listener, data);
}

#define ZXDG_TOPLEVEL_V6_DESTROY 0
#define ZXDG_TOPLEVEL_V6_SET_PARENT 1
#define ZXDG_TOPLEVEL_V6_SET_TITLE 2
#define ZXDG_TOPLEVEL_V6_SET_APP_ID 3
#define ZXDG_TOPLEVEL_V6_SHOW_WINDOW_MENU 4
#define ZXDG_TOPLEVEL_V6_MOVE 5
#define ZXDG_TOPLEVEL_V6_RESIZE 6
#define ZXDG_TOPLEVEL_V6_SET_MAX_SIZE 7
#define ZXDG_TOPLEVEL_V6_SET_MIN_SIZE 8
#define ZXDG_TOPLEVEL_V6_SET_MAXIMIZED 9
#define ZXDG_TOPLEVEL_V6_UNSET_MAXIMIZED 10
#define ZXDG_TOPLEVEL_V6_SET_FULLSCREEN 11
#define ZXDG_TOPLEVEL_V6_UNSET_FULLSCREEN 12
#define ZXDG_TOPLEVEL_V6_SET_MINIMIZED 13

/**
 * @ingroup iface_zxdg_toplevel_v6
 */
#define ZXDG_TOPLEVEL_V6_CONFIGURE_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_toplevel_v6
 */
#define ZXDG_TOPLEVEL_V6_CLOSE_SINCE_VERSION 1

/**
 * @ingroup iface_zxdg_toplevel_v6
 */
#define ZXDG_TOPLEVEL_V6_DESTROY_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_toplevel_v6
 */
#define ZXDG_TOPLEVEL_V6_SET_PARENT_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_toplevel_v6
 */
#define ZXDG_TOPLEVEL_V6_SET_TITLE_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_toplevel_v6
 */
#define ZXDG_TOPLEVEL_V6_SET_APP_ID_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_toplevel_v6
 */
#define ZXDG_TOPLEVEL_V6_SHOW_WINDOW_MENU_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_toplevel_v6
 */
#define ZXDG_TOPLEVEL_V6_MOVE_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_toplevel_v6
 */
#define ZXDG_TOPLEVEL_V6_RESIZE_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_toplevel_v6
 */
#define ZXDG_TOPLEVEL_V6_SET_MAX_SIZE_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_toplevel_v6
 */
#define ZXDG_TOPLEVEL_V6_SET_MIN_SIZE_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_toplevel_v6
 */
#define ZXDG_TOPLEVEL_V6_SET_MAXIMIZED_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_toplevel_v6
 */
#define ZXDG_TOPLEVEL_V6_UNSET_MAXIMIZED_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_toplevel_v6
 */
#define ZXDG_TOPLEVEL_V6_SET_FULLSCREEN_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_toplevel_v6
 */
#define ZXDG_TOPLEVEL_V6_UNSET_FULLSCREEN_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_toplevel_v6
 */
#define ZXDG_TOPLEVEL_V6_SET_MINIMIZED_SINCE_VERSION 1

/** @ingroup iface_zxdg_toplevel_v6 */
static inline void
zxdg_toplevel_v6_set_user_data(struct zxdg_toplevel_v6 *zxdg_toplevel_v6, void *user_data)
{
	wl_proxy_set_user_data((struct wl_proxy *) zxdg_toplevel_v6, user_data);
}

/** @ingroup iface_zxdg_toplevel_v6 */
static inline void *
zxdg_toplevel_v6_get_user_data(struct zxdg_toplevel_v6 *zxdg_toplevel_v6)
{
	return wl_proxy_get_user_data((struct wl_proxy *) zxdg_toplevel_v6);
}

static inline uint32_t
zxdg_toplevel_v6_get_version(struct zxdg_toplevel_v6 *zxdg_toplevel_v6)
{
	return wl_proxy_get_version((struct wl_proxy *) zxdg_toplevel_v6);
}

/**
 * @ingroup iface_zxdg_toplevel_v6
 *
 * Unmap and destroy the window. The window will be effectively
 * hidden from the user's point of view, and all state like
 * maximization, fullscreen, and so on, will be lost.
 */
static inline void
zxdg_toplevel_v6_destroy(struct zxdg_toplevel_v6 *zxdg_toplevel_v6)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_toplevel_v6,
			 ZXDG_TOPLEVEL_V6_DESTROY);

	wl_proxy_destroy((struct wl_proxy *) zxdg_toplevel_v6);
}

/**
 * @ingroup iface_zxdg_toplevel_v6
 *
 * Set the "parent" of this surface. This window should be stacked
 * above a parent. The parent surface must be mapped as long as this
 * surface is mapped.
 *
 * Parent windows should be set on dialogs, toolboxes, or other
 * "auxiliary" surfaces, so that the parent is raised when the dialog
 * is raised.
 */
static inline void
zxdg_toplevel_v6_set_parent(struct zxdg_toplevel_v6 *zxdg_toplevel_v6, struct zxdg_toplevel_v6 *parent)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_toplevel_v6,
			 ZXDG_TOPLEVEL_V6_SET_PARENT, parent);
}

/**
 * @ingroup iface_zxdg_toplevel_v6
 *
 * Set a short title for the surface.
 *
 * This string may be used to identify the surface in a task bar,
 * window list, or other user interface elements provided by the
 * compositor.
 *
 * The string must be encoded in UTF-8.
 */
static inline void
zxdg_toplevel_v6_set_title(struct zxdg_toplevel_v6 *zxdg_toplevel_v6, const char *title)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_toplevel_v6,
			 ZXDG_TOPLEVEL_V6_SET_TITLE, title);
}

/**
 * @ingroup iface_zxdg_toplevel_v6
 *
 * Set an application identifier for the surface.
 *
 * The app ID identifies the general class of applications to which
 * the surface belongs. The compositor can use this to group multiple
 * surfaces together, or to determine how to launch a new application.
 *
 * For D-Bus activatable applications, the app ID is used as the D-Bus
 * service name.
 *
 * The compositor shell will try to group application surfaces together
 * by their app ID. As a best practice, it is suggested to select app
 * ID's that match the basename of the application's .desktop file.
 * For example, "org.freedesktop.FooViewer" where the .desktop file is
 * "org.freedesktop.FooViewer.desktop".
 *
 * See the desktop-entry specification [0] for more details on
 * application identifiers and how they relate to well-known D-Bus
 * names and .desktop files.
 *
 * [0] http://standards.freedesktop.org/desktop-entry-spec/
 */
static inline void
zxdg_toplevel_v6_set_app_id(struct zxdg_toplevel_v6 *zxdg_toplevel_v6, const char *app_id)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_toplevel_v6,
			 ZXDG_TOPLEVEL_V6_SET_APP_ID, app_id);
}

/**
 * @ingroup iface_zxdg_toplevel_v6
 *
 * Clients implementing client-side decorations might want to show
 * a context menu when right-clicking on the decorations, giving the
 * user a menu that they can use to maximize or minimize the window.
 *
 * This request asks the compositor to pop up such a window menu at
 * the given position, relative to the local surface coordinates of
 * the parent surface. There are no guarantees as to what menu items
 * the window menu contains.
 *
 * This request must be used in response to some sort of user action
 * like a button press, key press, or touch down event.
 */
static inline void
zxdg_toplevel_v6_show_window_menu(struct zxdg_toplevel_v6 *zxdg_toplevel_v6, struct wl_seat *seat, uint32_t serial, int32_t x, int32_t y)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_toplevel_v6,
			 ZXDG_TOPLEVEL_V6_SHOW_WINDOW_MENU, seat, serial, x, y);
}

/**
 * @ingroup iface_zxdg_toplevel_v6
 *
 * Start an interactive, user-driven move of the surface.
 *
 * This request must be used in response to some sort of user action
 * like a button press, key press, or touch down event. The passed
 * serial is used to determine the type of interactive move (touch,
 * pointer, etc).
 *
 * The server may ignore move requests depending on the state of
 * the surface (e.g. fullscreen or maximized), or if the passed serial
 * is no longer valid.
 *
 * If triggered, the surface will lose the focus of the device
 * (wl_pointer, wl_touch, etc) used for the move. It is up to the
 * compositor to visually indicate that the move is taking place, such as
 * updating a pointer cursor, during the move. There is no guarantee
 * that the device focus will return when the move is completed.
 */
static inline void
zxdg_toplevel_v6_move(struct zxdg_toplevel_v6 *zxdg_toplevel_v6, struct wl_seat *seat, uint32_t serial)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_toplevel_v6,
			 ZXDG_TOPLEVEL_V6_MOVE, seat, serial);
}

/**
 * @ingroup iface_zxdg_toplevel_v6
 *
 * Start a user-driven, interactive resize of the surface.
 *
 * This request must be used in response to some sort of user action
 * like a button press, key press, or touch down event. The passed
 * serial is used to determine the type of interactive resize (touch,
 * pointer, etc).
 *
 * The server may ignore resize requests depending on the state of
 * the surface (e.g. fullscreen or maximized).
 *
 * If triggered, the client will receive configure events with the
 * "resize" state enum value and the expected sizes. See the "resize"
 * enum value for more details about what is required. The client
 * must also acknowledge configure events using "ack_configure". After
 * the resize is completed, the client will receive another "configure"
 * event without the resize state.
 *
 * If triggered, the surface also will lose the focus of the device
 * (wl_pointer, wl_touch, etc) used for the resize. It is up to the
 * compositor to visually indicate that the resize is taking place,
 * such as updating a pointer cursor, during the resize. There is no
 * guarantee that the device focus will return when the resize is
 * completed.
 *
 * The edges parameter specifies how the surface should be resized,
 * and is one of the values of the resize_edge enum. The compositor
 * may use this information to update the surface position for
 * example when dragging the top left corner. The compositor may also
 * use this information to adapt its behavior, e.g. choose an
 * appropriate cursor image.
 */
static inline void
zxdg_toplevel_v6_resize(struct zxdg_toplevel_v6 *zxdg_toplevel_v6, struct wl_seat *seat, uint32_t serial, uint32_t edges)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_toplevel_v6,
			 ZXDG_TOPLEVEL_V6_RESIZE, seat, serial, edges);
}

/**
 * @ingroup iface_zxdg_toplevel_v6
 *
 * Set a maximum size for the window.
 *
 * The client can specify a maximum size so that the compositor does
 * not try to configure the window beyond this size.
 *
 * The width and height arguments are in window geometry coordinates.
 * See xdg_surface.set_window_geometry.
 *
 * Values set in this way are double-buffered. They will get applied
 * on the next commit.
 *
 * The compositor can use this information to allow or disallow
 * different states like maximize or fullscreen and draw accurate
 * animations.
 *
 * Similarly, a tiling window manager may use this information to
 * place and resize client windows in a more effective way.
 *
 * The client should not rely on the compositor to obey the maximum
 * size. The compositor may decide to ignore the values set by the
 * client and request a larger size.
 *
 * If never set, or a value of zero in the request, means that the
 * client has no expected maximum size in the given dimension.
 * As a result, a client wishing to reset the maximum size
 * to an unspecified state can use zero for width and height in the
 * request.
 *
 * Requesting a maximum size to be smaller than the minimum size of
 * a surface is illegal and will result in a protocol error.
 *
 * The width and height must be greater than or equal to zero. Using
 * strictly negative values for width and height will result in a
 * protocol error.
 */
static inline void
zxdg_toplevel_v6_set_max_size(struct zxdg_toplevel_v6 *zxdg_toplevel_v6, int32_t width, int32_t height)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_toplevel_v6,
			 ZXDG_TOPLEVEL_V6_SET_MAX_SIZE, width, height);
}

/**
 * @ingroup iface_zxdg_toplevel_v6
 *
 * Set a minimum size for the window.
 *
 * The client can specify a minimum size so that the compositor does
 * not try to configure the window below this size.
 *
 * The width and height arguments are in window geometry coordinates.
 * See xdg_surface.set_window_geometry.
 *
 * Values set in this way are double-buffered. They will get applied
 * on the next commit.
 *
 * The compositor can use this information to allow or disallow
 * different states like maximize or fullscreen and draw accurate
 * animations.
 *
 * Similarly, a tiling window manager may use this information to
 * place and resize client windows in a more effective way.
 *
 * The client should not rely on the compositor to obey the minimum
 * size. The compositor may decide to ignore the values set by the
 * client and request a smaller size.
 *
 * If never set, or a value of zero in the request, means that the
 * client has no expected minimum size in the given dimension.
 * As a result, a client wishing to reset the minimum size
 * to an unspecified state can use zero for width and height in the
 * request.
 *
 * Requesting a minimum size to be larger than the maximum size of
 * a surface is illegal and will result in a protocol error.
 *
 * The width and height must be greater than or equal to zero. Using
 * strictly negative values for width and height will result in a
 * protocol error.
 */
static inline void
zxdg_toplevel_v6_set_min_size(struct zxdg_toplevel_v6 *zxdg_toplevel_v6, int32_t width, int32_t height)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_toplevel_v6,
			 ZXDG_TOPLEVEL_V6_SET_MIN_SIZE, width, height);
}

/**
 * @ingroup iface_zxdg_toplevel_v6
 *
 * Maximize the surface.
 *
 * After requesting that the surface should be maximized, the compositor
 * will respond by emitting a configure event with the "maximized" state
 * and the required window geometry. The client should then update its
 * content, drawing it in a maximized state, i.e. without shadow or other
 * decoration outside of the window geometry. The client must also
 * acknowledge the configure when committing the new content (see
 * ack_configure).
 *
 * It is up to the compositor to decide how and where to maximize the
 * surface, for example which output and what region of the screen should
 * be used.
 *
 * If the surface was already maximized, the compositor will still emit
 * a configure event with the "maximized" state.
 */
static inline void
zxdg_toplevel_v6_set_maximized(struct zxdg_toplevel_v6 *zxdg_toplevel_v6)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_toplevel_v6,
			 ZXDG_TOPLEVEL_V6_SET_MAXIMIZED);
}

/**
 * @ingroup iface_zxdg_toplevel_v6
 *
 * Unmaximize the surface.
 *
 * After requesting that the surface should be unmaximized, the compositor
 * will respond by emitting a configure event without the "maximized"
 * state. If available, the compositor will include the window geometry
 * dimensions the window had prior to being maximized in the configure
 * request. The client must then update its content, drawing it in a
 * regular state, i.e. potentially with shadow, etc. The client must also
 * acknowledge the configure when committing the new content (see
 * ack_configure).
 *
 * It is up to the compositor to position the surface after it was
 * unmaximized; usually the position the surface had before maximizing, if
 * applicable.
 *
 * If the surface was already not maximized, the compositor will still
 * emit a configure event without the "maximized" state.
 */
static inline void
zxdg_toplevel_v6_unset_maximized(struct zxdg_toplevel_v6 *zxdg_toplevel_v6)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_toplevel_v6,
			 ZXDG_TOPLEVEL_V6_UNSET_MAXIMIZED);
}

/**
 * @ingroup iface_zxdg_toplevel_v6
 *
 * Make the surface fullscreen.
 *
 * You can specify an output that you would prefer to be fullscreen.
 * If this value is NULL, it's up to the compositor to choose which
 * display will be used to map this surface.
 *
 * If the surface doesn't cover the whole output, the compositor will
 * position the surface in the center of the output and compensate with
 * black borders filling the rest of the output.
 */
static inline void
zxdg_toplevel_v6_set_fullscreen(struct zxdg_toplevel_v6 *zxdg_toplevel_v6, struct wl_output *output)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_toplevel_v6,
			 ZXDG_TOPLEVEL_V6_SET_FULLSCREEN, output);
}

/**
 * @ingroup iface_zxdg_toplevel_v6
 */
static inline void
zxdg_toplevel_v6_unset_fullscreen(struct zxdg_toplevel_v6 *zxdg_toplevel_v6)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_toplevel_v6,
			 ZXDG_TOPLEVEL_V6_UNSET_FULLSCREEN);
}

/**
 * @ingroup iface_zxdg_toplevel_v6
 *
 * Request that the compositor minimize your surface. There is no
 * way to know if the surface is currently minimized, nor is there
 * any way to unset minimization on this surface.
 *
 * If you are looking to throttle redrawing when minimized, please
 * instead use the wl_surface.frame event for this, as this will
 * also work with live previews on windows in Alt-Tab, Expose or
 * similar compositor features.
 */
static inline void
zxdg_toplevel_v6_set_minimized(struct zxdg_toplevel_v6 *zxdg_toplevel_v6)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_toplevel_v6,
			 ZXDG_TOPLEVEL_V6_SET_MINIMIZED);
}

#ifndef ZXDG_POPUP_V6_ERROR_ENUM
#define ZXDG_POPUP_V6_ERROR_ENUM
enum zxdg_popup_v6_error {
	/**
	 * tried to grab after being mapped
	 */
	ZXDG_POPUP_V6_ERROR_INVALID_GRAB = 0,
};
#endif /* ZXDG_POPUP_V6_ERROR_ENUM */

/**
 * @ingroup iface_zxdg_popup_v6
 * @struct zxdg_popup_v6_listener
 */
struct zxdg_popup_v6_listener {
	/**
	 * configure the popup surface
	 *
	 * This event asks the popup surface to configure itself given
	 * the configuration. The configured state should not be applied
	 * immediately. See xdg_surface.configure for details.
	 *
	 * The x and y arguments represent the position the popup was
	 * placed at given the xdg_positioner rule, relative to the upper
	 * left corner of the window geometry of the parent surface.
	 * @param x x position relative to parent surface window geometry
	 * @param y y position relative to parent surface window geometry
	 * @param width window geometry width
	 * @param height window geometry height
	 */
	void (*configure)(void *data,
			  struct zxdg_popup_v6 *zxdg_popup_v6,
			  int32_t x,
			  int32_t y,
			  int32_t width,
			  int32_t height);
	/**
	 * popup interaction is done
	 *
	 * The popup_done event is sent out when a popup is dismissed by
	 * the compositor. The client should destroy the xdg_popup object
	 * at this point.
	 */
	void (*popup_done)(void *data,
			   struct zxdg_popup_v6 *zxdg_popup_v6);
};

/**
 * @ingroup iface_zxdg_popup_v6
 */
static inline int
zxdg_popup_v6_add_listener(struct zxdg_popup_v6 *zxdg_popup_v6,
			   const struct zxdg_popup_v6_listener *listener, void *data)
{
	return wl_proxy_add_listener((struct wl_proxy *) zxdg_popup_v6,
				     (void (**)(void)) listener, data);
}

#define ZXDG_POPUP_V6_DESTROY 0
#define ZXDG_POPUP_V6_GRAB 1

/**
 * @ingroup iface_zxdg_popup_v6
 */
#define ZXDG_POPUP_V6_CONFIGURE_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_popup_v6
 */
#define ZXDG_POPUP_V6_POPUP_DONE_SINCE_VERSION 1

/**
 * @ingroup iface_zxdg_popup_v6
 */
#define ZXDG_POPUP_V6_DESTROY_SINCE_VERSION 1
/**
 * @ingroup iface_zxdg_popup_v6
 */
#define ZXDG_POPUP_V6_GRAB_SINCE_VERSION 1

/** @ingroup iface_zxdg_popup_v6 */
static inline void
zxdg_popup_v6_set_user_data(struct zxdg_popup_v6 *zxdg_popup_v6, void *user_data)
{
	wl_proxy_set_user_data((struct wl_proxy *) zxdg_popup_v6, user_data);
}

/** @ingroup iface_zxdg_popup_v6 */
static inline void *
zxdg_popup_v6_get_user_data(struct zxdg_popup_v6 *zxdg_popup_v6)
{
	return wl_proxy_get_user_data((struct wl_proxy *) zxdg_popup_v6);
}

static inline uint32_t
zxdg_popup_v6_get_version(struct zxdg_popup_v6 *zxdg_popup_v6)
{
	return wl_proxy_get_version((struct wl_proxy *) zxdg_popup_v6);
}

/**
 * @ingroup iface_zxdg_popup_v6
 *
 * This destroys the popup. Explicitly destroying the xdg_popup
 * object will also dismiss the popup, and unmap the surface.
 *
 * If this xdg_popup is not the "topmost" popup, a protocol error
 * will be sent.
 */
static inline void
zxdg_popup_v6_destroy(struct zxdg_popup_v6 *zxdg_popup_v6)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_popup_v6,
			 ZXDG_POPUP_V6_DESTROY);

	wl_proxy_destroy((struct wl_proxy *) zxdg_popup_v6);
}

/**
 * @ingroup iface_zxdg_popup_v6
 *
 * This request makes the created popup take an explicit grab. An explicit
 * grab will be dismissed when the user dismisses the popup, or when the
 * client destroys the xdg_popup. This can be done by the user clicking
 * outside the surface, using the keyboard, or even locking the screen
 * through closing the lid or a timeout.
 *
 * If the compositor denies the grab, the popup will be immediately
 * dismissed.
 *
 * This request must be used in response to some sort of user action like a
 * button press, key press, or touch down event. The serial number of the
 * event should be passed as 'serial'.
 *
 * The parent of a grabbing popup must either be an xdg_toplevel surface or
 * another xdg_popup with an explicit grab. If the parent is another
 * xdg_popup it means that the popups are nested, with this popup now being
 * the topmost popup.
 *
 * Nested popups must be destroyed in the reverse order they were created
 * in, e.g. the only popup you are allowed to destroy at all times is the
 * topmost one.
 *
 * When compositors choose to dismiss a popup, they may dismiss every
 * nested grabbing popup as well. When a compositor dismisses popups, it
 * will follow the same dismissing order as required from the client.
 *
 * The parent of a grabbing popup must either be another xdg_popup with an
 * active explicit grab, or an xdg_popup or xdg_toplevel, if there are no
 * explicit grabs already taken.
 *
 * If the topmost grabbing popup is destroyed, the grab will be returned to
 * the parent of the popup, if that parent previously had an explicit grab.
 *
 * If the parent is a grabbing popup which has already been dismissed, this
 * popup will be immediately dismissed. If the parent is a popup that did
 * not take an explicit grab, an error will be raised.
 *
 * During a popup grab, the client owning the grab will receive pointer
 * and touch events for all their surfaces as normal (similar to an
 * "owner-events" grab in X11 parlance), while the top most grabbing popup
 * will always have keyboard focus.
 */
static inline void
zxdg_popup_v6_grab(struct zxdg_popup_v6 *zxdg_popup_v6, struct wl_seat *seat, uint32_t serial)
{
	wl_proxy_marshal((struct wl_proxy *) zxdg_popup_v6,
			 ZXDG_POPUP_V6_GRAB, seat, serial);
}

#ifdef  __cplusplus
}
#endif

#endif
