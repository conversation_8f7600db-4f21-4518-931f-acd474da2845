// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		007317A40858DECD00B2BC32 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0073179D0858DECD00B2BC32 /* Cocoa.framework */; };
		007317A60858DECD00B2BC32 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0073179F0858DECD00B2BC32 /* IOKit.framework */; };
		007317AB0858DECD00B2BC32 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0073179D0858DECD00B2BC32 /* Cocoa.framework */; };
		007317AD0858DECD00B2BC32 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0073179F0858DECD00B2BC32 /* IOKit.framework */; };
		007317C30858E15000B2BC32 /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 007317C10858E15000B2BC32 /* Carbon.framework */; };
		00CFA89D106B4BA100758660 /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 00CFA89C106B4BA100758660 /* ForceFeedback.framework */; };
		00D0D08410675DD9004B05EF /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 00D0D08310675DD9004B05EF /* CoreFoundation.framework */; };
		00D0D0D810675E46004B05EF /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 007317C10858E15000B2BC32 /* Carbon.framework */; };
		04043BBB12FEB1BE0076DB1F /* SDL_glfuncs.h in Headers */ = {isa = PBXBuildFile; fileRef = 04043BBA12FEB1BE0076DB1F /* SDL_glfuncs.h */; };
		04043BBC12FEB1BE0076DB1F /* SDL_glfuncs.h in Headers */ = {isa = PBXBuildFile; fileRef = 04043BBA12FEB1BE0076DB1F /* SDL_glfuncs.h */; };
		041B2CA512FA0D680087D585 /* SDL_render.c in Sources */ = {isa = PBXBuildFile; fileRef = 041B2C9E12FA0D680087D585 /* SDL_render.c */; };
		041B2CA612FA0D680087D585 /* SDL_sysrender.h in Headers */ = {isa = PBXBuildFile; fileRef = 041B2C9F12FA0D680087D585 /* SDL_sysrender.h */; };
		041B2CAB12FA0D680087D585 /* SDL_render.c in Sources */ = {isa = PBXBuildFile; fileRef = 041B2C9E12FA0D680087D585 /* SDL_render.c */; };
		041B2CAC12FA0D680087D585 /* SDL_sysrender.h in Headers */ = {isa = PBXBuildFile; fileRef = 041B2C9F12FA0D680087D585 /* SDL_sysrender.h */; };
		0435673E1303160F00BA5428 /* SDL_shaders_gl.c in Sources */ = {isa = PBXBuildFile; fileRef = 0435673C1303160F00BA5428 /* SDL_shaders_gl.c */; };
		0435673F1303160F00BA5428 /* SDL_shaders_gl.h in Headers */ = {isa = PBXBuildFile; fileRef = 0435673D1303160F00BA5428 /* SDL_shaders_gl.h */; };
		043567401303160F00BA5428 /* SDL_shaders_gl.c in Sources */ = {isa = PBXBuildFile; fileRef = 0435673C1303160F00BA5428 /* SDL_shaders_gl.c */; };
		043567411303160F00BA5428 /* SDL_shaders_gl.h in Headers */ = {isa = PBXBuildFile; fileRef = 0435673D1303160F00BA5428 /* SDL_shaders_gl.h */; };
		04409B9312FA97ED00FB9AA8 /* SDL_yuv_sw_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04409B8F12FA97ED00FB9AA8 /* SDL_yuv_sw_c.h */; };
		04409B9412FA97ED00FB9AA8 /* SDL_yuv_sw.c in Sources */ = {isa = PBXBuildFile; fileRef = 04409B9012FA97ED00FB9AA8 /* SDL_yuv_sw.c */; };
		04409B9712FA97ED00FB9AA8 /* SDL_yuv_sw_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04409B8F12FA97ED00FB9AA8 /* SDL_yuv_sw_c.h */; };
		04409B9812FA97ED00FB9AA8 /* SDL_yuv_sw.c in Sources */ = {isa = PBXBuildFile; fileRef = 04409B9012FA97ED00FB9AA8 /* SDL_yuv_sw.c */; };
		0442EC1812FE1BBA004C9285 /* SDL_render_gl.c in Sources */ = {isa = PBXBuildFile; fileRef = 0442EC1712FE1BBA004C9285 /* SDL_render_gl.c */; };
		0442EC1912FE1BBA004C9285 /* SDL_render_gl.c in Sources */ = {isa = PBXBuildFile; fileRef = 0442EC1712FE1BBA004C9285 /* SDL_render_gl.c */; };
		0442EC1C12FE1BCB004C9285 /* SDL_render_sw_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 0442EC1A12FE1BCB004C9285 /* SDL_render_sw_c.h */; };
		0442EC1D12FE1BCB004C9285 /* SDL_render_sw.c in Sources */ = {isa = PBXBuildFile; fileRef = 0442EC1B12FE1BCB004C9285 /* SDL_render_sw.c */; };
		0442EC1E12FE1BCB004C9285 /* SDL_render_sw_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 0442EC1A12FE1BCB004C9285 /* SDL_render_sw_c.h */; };
		0442EC1F12FE1BCB004C9285 /* SDL_render_sw.c in Sources */ = {isa = PBXBuildFile; fileRef = 0442EC1B12FE1BCB004C9285 /* SDL_render_sw.c */; };
		0442EC5A12FE1C60004C9285 /* SDL_x11framebuffer.c in Sources */ = {isa = PBXBuildFile; fileRef = 0442EC5812FE1C60004C9285 /* SDL_x11framebuffer.c */; };
		0442EC5B12FE1C60004C9285 /* SDL_x11framebuffer.h in Headers */ = {isa = PBXBuildFile; fileRef = 0442EC5912FE1C60004C9285 /* SDL_x11framebuffer.h */; };
		0442EC5C12FE1C60004C9285 /* SDL_x11framebuffer.c in Sources */ = {isa = PBXBuildFile; fileRef = 0442EC5812FE1C60004C9285 /* SDL_x11framebuffer.c */; };
		0442EC5D12FE1C60004C9285 /* SDL_x11framebuffer.h in Headers */ = {isa = PBXBuildFile; fileRef = 0442EC5912FE1C60004C9285 /* SDL_x11framebuffer.h */; };
		0442EC5F12FE1C75004C9285 /* SDL_hints.c in Sources */ = {isa = PBXBuildFile; fileRef = 0442EC5E12FE1C75004C9285 /* SDL_hints.c */; };
		0442EC6012FE1C75004C9285 /* SDL_hints.c in Sources */ = {isa = PBXBuildFile; fileRef = 0442EC5E12FE1C75004C9285 /* SDL_hints.c */; };
		04BAC0C81300C2160055DE28 /* SDL_log.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BAC0C71300C2160055DE28 /* SDL_log.c */; };
		04BAC0C91300C2160055DE28 /* SDL_log.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BAC0C71300C2160055DE28 /* SDL_log.c */; };
		04BD000812E6671800899322 /* SDL_diskaudio.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFD8812E6671700899322 /* SDL_diskaudio.c */; };
		04BD000912E6671800899322 /* SDL_diskaudio.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFD8912E6671700899322 /* SDL_diskaudio.h */; };
		04BD001012E6671800899322 /* SDL_dummyaudio.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFD9412E6671700899322 /* SDL_dummyaudio.c */; };
		04BD001112E6671800899322 /* SDL_dummyaudio.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFD9512E6671700899322 /* SDL_dummyaudio.h */; };
		04BD001912E6671800899322 /* SDL_coreaudio.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDA112E6671700899322 /* SDL_coreaudio.h */; };
		04BD002612E6671800899322 /* SDL_audio.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDB412E6671700899322 /* SDL_audio.c */; };
		04BD002712E6671800899322 /* SDL_audio_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDB512E6671700899322 /* SDL_audio_c.h */; };
		04BD002812E6671800899322 /* SDL_audiocvt.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDB612E6671700899322 /* SDL_audiocvt.c */; };
		04BD002912E6671800899322 /* SDL_audiodev.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDB712E6671700899322 /* SDL_audiodev.c */; };
		04BD002A12E6671800899322 /* SDL_audiodev_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDB812E6671700899322 /* SDL_audiodev_c.h */; };
		04BD002C12E6671800899322 /* SDL_audiotypecvt.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDBA12E6671700899322 /* SDL_audiotypecvt.c */; };
		04BD002D12E6671800899322 /* SDL_mixer.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDBB12E6671700899322 /* SDL_mixer.c */; };
		04BD003412E6671800899322 /* SDL_sysaudio.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDC212E6671700899322 /* SDL_sysaudio.h */; };
		04BD003512E6671800899322 /* SDL_wave.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDC312E6671700899322 /* SDL_wave.c */; };
		04BD003612E6671800899322 /* SDL_wave.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDC412E6671700899322 /* SDL_wave.h */; };
		04BD004112E6671800899322 /* SDL_cpuinfo.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDD412E6671700899322 /* SDL_cpuinfo.c */; };
		04BD004212E6671800899322 /* blank_cursor.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDD612E6671700899322 /* blank_cursor.h */; };
		04BD004312E6671800899322 /* default_cursor.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDD712E6671700899322 /* default_cursor.h */; };
		04BD004412E6671800899322 /* scancodes_darwin.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDD812E6671700899322 /* scancodes_darwin.h */; };
		04BD004512E6671800899322 /* scancodes_linux.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDD912E6671700899322 /* scancodes_linux.h */; };
		04BD004712E6671800899322 /* scancodes_xfree86.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDDB12E6671700899322 /* scancodes_xfree86.h */; };
		04BD004812E6671800899322 /* SDL_clipboardevents.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDDC12E6671700899322 /* SDL_clipboardevents.c */; };
		04BD004912E6671800899322 /* SDL_clipboardevents_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDDD12E6671700899322 /* SDL_clipboardevents_c.h */; };
		04BD004A12E6671800899322 /* SDL_events.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDDE12E6671700899322 /* SDL_events.c */; };
		04BD004B12E6671800899322 /* SDL_events_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDDF12E6671700899322 /* SDL_events_c.h */; };
		04BD004C12E6671800899322 /* SDL_gesture.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDE012E6671700899322 /* SDL_gesture.c */; };
		04BD004D12E6671800899322 /* SDL_gesture_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDE112E6671700899322 /* SDL_gesture_c.h */; };
		04BD004E12E6671800899322 /* SDL_keyboard.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDE212E6671700899322 /* SDL_keyboard.c */; };
		04BD004F12E6671800899322 /* SDL_keyboard_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDE312E6671700899322 /* SDL_keyboard_c.h */; };
		04BD005012E6671800899322 /* SDL_mouse.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDE412E6671700899322 /* SDL_mouse.c */; };
		04BD005112E6671800899322 /* SDL_mouse_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDE512E6671700899322 /* SDL_mouse_c.h */; };
		04BD005212E6671800899322 /* SDL_quit.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDE612E6671700899322 /* SDL_quit.c */; };
		04BD005312E6671800899322 /* SDL_sysevents.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDE712E6671700899322 /* SDL_sysevents.h */; };
		04BD005412E6671800899322 /* SDL_touch.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDE812E6671700899322 /* SDL_touch.c */; };
		04BD005512E6671800899322 /* SDL_touch_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDE912E6671700899322 /* SDL_touch_c.h */; };
		04BD005612E6671800899322 /* SDL_windowevents.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDEA12E6671700899322 /* SDL_windowevents.c */; };
		04BD005712E6671800899322 /* SDL_windowevents_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDEB12E6671700899322 /* SDL_windowevents_c.h */; };
		04BD005812E6671800899322 /* SDL_rwopsbundlesupport.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDEE12E6671700899322 /* SDL_rwopsbundlesupport.h */; };
		04BD005912E6671800899322 /* SDL_rwopsbundlesupport.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDEF12E6671700899322 /* SDL_rwopsbundlesupport.m */; };
		04BD005A12E6671800899322 /* SDL_rwops.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDF012E6671700899322 /* SDL_rwops.c */; };
		04BD005B12E6671800899322 /* SDL_syshaptic.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDF312E6671700899322 /* SDL_syshaptic.c */; };
		04BD005F12E6671800899322 /* SDL_haptic.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDFA12E6671700899322 /* SDL_haptic.c */; };
		04BD006012E6671800899322 /* SDL_haptic_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDFB12E6671700899322 /* SDL_haptic_c.h */; };
		04BD006112E6671800899322 /* SDL_syshaptic.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDFC12E6671700899322 /* SDL_syshaptic.h */; };
		04BD006612E6671800899322 /* SDL_sysjoystick.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE0712E6671700899322 /* SDL_sysjoystick.c */; };
		04BD006712E6671800899322 /* SDL_sysjoystick_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE0812E6671700899322 /* SDL_sysjoystick_c.h */; };
		04BD007012E6671800899322 /* SDL_joystick.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE1612E6671700899322 /* SDL_joystick.c */; };
		04BD007112E6671800899322 /* SDL_joystick_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE1712E6671700899322 /* SDL_joystick_c.h */; };
		04BD007212E6671800899322 /* SDL_sysjoystick.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE1812E6671700899322 /* SDL_sysjoystick.h */; };
		04BD008812E6671800899322 /* SDL_sysloadso.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE3312E6671700899322 /* SDL_sysloadso.c */; };
		04BD009412E6671800899322 /* SDL_syspower.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE4B12E6671700899322 /* SDL_syspower.c */; };
		04BD009612E6671800899322 /* SDL_power.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE4E12E6671700899322 /* SDL_power.c */; };
		04BD009B12E6671800899322 /* SDL_assert_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE5512E6671700899322 /* SDL_assert_c.h */; };
		04BD009C12E6671800899322 /* SDL_assert.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE5612E6671700899322 /* SDL_assert.c */; };
		04BD009E12E6671800899322 /* SDL_error_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE5812E6671700899322 /* SDL_error_c.h */; };
		04BD009F12E6671800899322 /* SDL_error.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE5912E6671700899322 /* SDL_error.c */; };
		04BD00A212E6671800899322 /* SDL.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE5C12E6671700899322 /* SDL.c */; };
		04BD00A312E6671800899322 /* SDL_getenv.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE5E12E6671700899322 /* SDL_getenv.c */; };
		04BD00A412E6671800899322 /* SDL_iconv.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE5F12E6671700899322 /* SDL_iconv.c */; };
		04BD00A512E6671800899322 /* SDL_malloc.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE6012E6671700899322 /* SDL_malloc.c */; };
		04BD00A612E6671800899322 /* SDL_qsort.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE6112E6671700899322 /* SDL_qsort.c */; };
		04BD00A712E6671800899322 /* SDL_stdlib.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE6212E6671700899322 /* SDL_stdlib.c */; };
		04BD00A812E6671800899322 /* SDL_string.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE6312E6671700899322 /* SDL_string.c */; };
		04BD00BD12E6671800899322 /* SDL_syscond.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE7E12E6671800899322 /* SDL_syscond.c */; };
		04BD00BE12E6671800899322 /* SDL_sysmutex.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE7F12E6671800899322 /* SDL_sysmutex.c */; };
		04BD00BF12E6671800899322 /* SDL_sysmutex_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE8012E6671800899322 /* SDL_sysmutex_c.h */; };
		04BD00C012E6671800899322 /* SDL_syssem.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE8112E6671800899322 /* SDL_syssem.c */; };
		04BD00C112E6671800899322 /* SDL_systhread.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE8212E6671800899322 /* SDL_systhread.c */; };
		04BD00C212E6671800899322 /* SDL_systhread_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE8312E6671800899322 /* SDL_systhread_c.h */; };
		04BD00C912E6671800899322 /* SDL_systhread.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE8B12E6671800899322 /* SDL_systhread.h */; };
		04BD00CA12E6671800899322 /* SDL_thread.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE8C12E6671800899322 /* SDL_thread.c */; };
		04BD00CB12E6671800899322 /* SDL_thread_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE8D12E6671800899322 /* SDL_thread_c.h */; };
		04BD00D712E6671800899322 /* SDL_timer.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE9F12E6671800899322 /* SDL_timer.c */; };
		04BD00D812E6671800899322 /* SDL_timer_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFEA012E6671800899322 /* SDL_timer_c.h */; };
		04BD00D912E6671800899322 /* SDL_systimer.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFEA212E6671800899322 /* SDL_systimer.c */; };
		04BD00F312E6671800899322 /* SDL_cocoaclipboard.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFEC212E6671800899322 /* SDL_cocoaclipboard.h */; };
		04BD00F412E6671800899322 /* SDL_cocoaclipboard.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFEC312E6671800899322 /* SDL_cocoaclipboard.m */; };
		04BD00F512E6671800899322 /* SDL_cocoaevents.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFEC412E6671800899322 /* SDL_cocoaevents.h */; };
		04BD00F612E6671800899322 /* SDL_cocoaevents.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFEC512E6671800899322 /* SDL_cocoaevents.m */; };
		04BD00F712E6671800899322 /* SDL_cocoakeyboard.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFEC612E6671800899322 /* SDL_cocoakeyboard.h */; };
		04BD00F812E6671800899322 /* SDL_cocoakeyboard.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFEC712E6671800899322 /* SDL_cocoakeyboard.m */; };
		04BD00F912E6671800899322 /* SDL_cocoamodes.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFEC812E6671800899322 /* SDL_cocoamodes.h */; };
		04BD00FA12E6671800899322 /* SDL_cocoamodes.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFEC912E6671800899322 /* SDL_cocoamodes.m */; };
		04BD00FB12E6671800899322 /* SDL_cocoamouse.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFECA12E6671800899322 /* SDL_cocoamouse.h */; };
		04BD00FC12E6671800899322 /* SDL_cocoamouse.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFECB12E6671800899322 /* SDL_cocoamouse.m */; };
		04BD00FD12E6671800899322 /* SDL_cocoaopengl.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFECC12E6671800899322 /* SDL_cocoaopengl.h */; };
		04BD00FE12E6671800899322 /* SDL_cocoaopengl.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFECD12E6671800899322 /* SDL_cocoaopengl.m */; };
		04BD00FF12E6671800899322 /* SDL_cocoashape.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFECE12E6671800899322 /* SDL_cocoashape.h */; };
		04BD010012E6671800899322 /* SDL_cocoashape.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFECF12E6671800899322 /* SDL_cocoashape.m */; };
		04BD010112E6671800899322 /* SDL_cocoavideo.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFED012E6671800899322 /* SDL_cocoavideo.h */; };
		04BD010212E6671800899322 /* SDL_cocoavideo.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFED112E6671800899322 /* SDL_cocoavideo.m */; };
		04BD010312E6671800899322 /* SDL_cocoawindow.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFED212E6671800899322 /* SDL_cocoawindow.h */; };
		04BD010412E6671800899322 /* SDL_cocoawindow.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFED312E6671800899322 /* SDL_cocoawindow.m */; };
		04BD011712E6671800899322 /* SDL_nullevents.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFEE812E6671800899322 /* SDL_nullevents.c */; };
		04BD011812E6671800899322 /* SDL_nullevents_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFEE912E6671800899322 /* SDL_nullevents_c.h */; };
		04BD011B12E6671800899322 /* SDL_nullvideo.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFEEC12E6671800899322 /* SDL_nullvideo.c */; };
		04BD011C12E6671800899322 /* SDL_nullvideo.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFEED12E6671800899322 /* SDL_nullvideo.h */; };
		04BD017512E6671800899322 /* SDL_blit.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF4E12E6671800899322 /* SDL_blit.c */; };
		04BD017612E6671800899322 /* SDL_blit.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFF4F12E6671800899322 /* SDL_blit.h */; };
		04BD017712E6671800899322 /* SDL_blit_0.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5012E6671800899322 /* SDL_blit_0.c */; };
		04BD017812E6671800899322 /* SDL_blit_1.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5112E6671800899322 /* SDL_blit_1.c */; };
		04BD017912E6671800899322 /* SDL_blit_A.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5212E6671800899322 /* SDL_blit_A.c */; };
		04BD017A12E6671800899322 /* SDL_blit_auto.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5312E6671800899322 /* SDL_blit_auto.c */; };
		04BD017B12E6671800899322 /* SDL_blit_auto.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFF5412E6671800899322 /* SDL_blit_auto.h */; };
		04BD017C12E6671800899322 /* SDL_blit_copy.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5512E6671800899322 /* SDL_blit_copy.c */; };
		04BD017D12E6671800899322 /* SDL_blit_copy.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFF5612E6671800899322 /* SDL_blit_copy.h */; };
		04BD017E12E6671800899322 /* SDL_blit_N.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5712E6671800899322 /* SDL_blit_N.c */; };
		04BD017F12E6671800899322 /* SDL_blit_slow.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5812E6671800899322 /* SDL_blit_slow.c */; };
		04BD018012E6671800899322 /* SDL_blit_slow.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFF5912E6671800899322 /* SDL_blit_slow.h */; };
		04BD018112E6671800899322 /* SDL_bmp.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5A12E6671800899322 /* SDL_bmp.c */; };
		04BD018212E6671800899322 /* SDL_clipboard.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5B12E6671800899322 /* SDL_clipboard.c */; };
		04BD018712E6671800899322 /* SDL_fillrect.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF6012E6671800899322 /* SDL_fillrect.c */; };
		04BD018C12E6671800899322 /* SDL_pixels.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF6512E6671800899322 /* SDL_pixels.c */; };
		04BD018D12E6671800899322 /* SDL_pixels_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFF6612E6671800899322 /* SDL_pixels_c.h */; };
		04BD018E12E6671800899322 /* SDL_rect.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF6712E6671800899322 /* SDL_rect.c */; };
		04BD019612E6671800899322 /* SDL_RLEaccel.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF6F12E6671800899322 /* SDL_RLEaccel.c */; };
		04BD019712E6671800899322 /* SDL_RLEaccel_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFF7012E6671800899322 /* SDL_RLEaccel_c.h */; };
		04BD019812E6671800899322 /* SDL_shape.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF7112E6671800899322 /* SDL_shape.c */; };
		04BD019912E6671800899322 /* SDL_shape_internals.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFF7212E6671800899322 /* SDL_shape_internals.h */; };
		04BD019A12E6671800899322 /* SDL_stretch.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF7312E6671800899322 /* SDL_stretch.c */; };
		04BD019B12E6671800899322 /* SDL_surface.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF7412E6671800899322 /* SDL_surface.c */; };
		04BD019C12E6671800899322 /* SDL_sysvideo.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFF7512E6671800899322 /* SDL_sysvideo.h */; };
		04BD019D12E6671800899322 /* SDL_video.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF7612E6671800899322 /* SDL_video.c */; };
		04BD01DB12E6671800899322 /* imKStoUCS.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFB812E6671800899322 /* imKStoUCS.c */; };
		04BD01DC12E6671800899322 /* imKStoUCS.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFB912E6671800899322 /* imKStoUCS.h */; };
		04BD01DD12E6671800899322 /* SDL_x11clipboard.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFBA12E6671800899322 /* SDL_x11clipboard.c */; };
		04BD01DE12E6671800899322 /* SDL_x11clipboard.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFBB12E6671800899322 /* SDL_x11clipboard.h */; };
		04BD01DF12E6671800899322 /* SDL_x11dyn.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFBC12E6671800899322 /* SDL_x11dyn.c */; };
		04BD01E012E6671800899322 /* SDL_x11dyn.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFBD12E6671800899322 /* SDL_x11dyn.h */; };
		04BD01E112E6671800899322 /* SDL_x11events.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFBE12E6671800899322 /* SDL_x11events.c */; };
		04BD01E212E6671800899322 /* SDL_x11events.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFBF12E6671800899322 /* SDL_x11events.h */; };
		04BD01E512E6671800899322 /* SDL_x11keyboard.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFC212E6671800899322 /* SDL_x11keyboard.c */; };
		04BD01E612E6671800899322 /* SDL_x11keyboard.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFC312E6671800899322 /* SDL_x11keyboard.h */; };
		04BD01E712E6671800899322 /* SDL_x11modes.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFC412E6671800899322 /* SDL_x11modes.c */; };
		04BD01E812E6671800899322 /* SDL_x11modes.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFC512E6671800899322 /* SDL_x11modes.h */; };
		04BD01E912E6671800899322 /* SDL_x11mouse.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFC612E6671800899322 /* SDL_x11mouse.c */; };
		04BD01EA12E6671800899322 /* SDL_x11mouse.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFC712E6671800899322 /* SDL_x11mouse.h */; };
		04BD01EB12E6671800899322 /* SDL_x11opengl.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFC812E6671800899322 /* SDL_x11opengl.c */; };
		04BD01EC12E6671800899322 /* SDL_x11opengl.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFC912E6671800899322 /* SDL_x11opengl.h */; };
		04BD01ED12E6671800899322 /* SDL_x11opengles.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFCA12E6671800899322 /* SDL_x11opengles.c */; };
		04BD01EE12E6671800899322 /* SDL_x11opengles.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFCB12E6671800899322 /* SDL_x11opengles.h */; };
		04BD01F112E6671800899322 /* SDL_x11shape.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFCE12E6671800899322 /* SDL_x11shape.c */; };
		04BD01F212E6671800899322 /* SDL_x11shape.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFCF12E6671800899322 /* SDL_x11shape.h */; };
		04BD01F312E6671800899322 /* SDL_x11sym.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFD012E6671800899322 /* SDL_x11sym.h */; };
		04BD01F412E6671800899322 /* SDL_x11touch.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFD112E6671800899322 /* SDL_x11touch.c */; };
		04BD01F512E6671800899322 /* SDL_x11touch.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFD212E6671800899322 /* SDL_x11touch.h */; };
		04BD01F612E6671800899322 /* SDL_x11video.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFD312E6671800899322 /* SDL_x11video.c */; };
		04BD01F712E6671800899322 /* SDL_x11video.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFD412E6671800899322 /* SDL_x11video.h */; };
		04BD01F812E6671800899322 /* SDL_x11window.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFD512E6671800899322 /* SDL_x11window.c */; };
		04BD01F912E6671800899322 /* SDL_x11window.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFD612E6671800899322 /* SDL_x11window.h */; };
		04BD021712E6671800899322 /* SDL_atomic.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFD7412E6671700899322 /* SDL_atomic.c */; };
		04BD021812E6671800899322 /* SDL_spinlock.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFD7512E6671700899322 /* SDL_spinlock.c */; };
		04BD022412E6671800899322 /* SDL_diskaudio.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFD8812E6671700899322 /* SDL_diskaudio.c */; };
		04BD022512E6671800899322 /* SDL_diskaudio.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFD8912E6671700899322 /* SDL_diskaudio.h */; };
		04BD022C12E6671800899322 /* SDL_dummyaudio.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFD9412E6671700899322 /* SDL_dummyaudio.c */; };
		04BD022D12E6671800899322 /* SDL_dummyaudio.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFD9512E6671700899322 /* SDL_dummyaudio.h */; };
		04BD023512E6671800899322 /* SDL_coreaudio.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDA112E6671700899322 /* SDL_coreaudio.h */; };
		04BD024212E6671800899322 /* SDL_audio.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDB412E6671700899322 /* SDL_audio.c */; };
		04BD024312E6671800899322 /* SDL_audio_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDB512E6671700899322 /* SDL_audio_c.h */; };
		04BD024412E6671800899322 /* SDL_audiocvt.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDB612E6671700899322 /* SDL_audiocvt.c */; };
		04BD024512E6671800899322 /* SDL_audiodev.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDB712E6671700899322 /* SDL_audiodev.c */; };
		04BD024612E6671800899322 /* SDL_audiodev_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDB812E6671700899322 /* SDL_audiodev_c.h */; };
		04BD024812E6671800899322 /* SDL_audiotypecvt.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDBA12E6671700899322 /* SDL_audiotypecvt.c */; };
		04BD024912E6671800899322 /* SDL_mixer.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDBB12E6671700899322 /* SDL_mixer.c */; };
		04BD025012E6671800899322 /* SDL_sysaudio.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDC212E6671700899322 /* SDL_sysaudio.h */; };
		04BD025112E6671800899322 /* SDL_wave.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDC312E6671700899322 /* SDL_wave.c */; };
		04BD025212E6671800899322 /* SDL_wave.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDC412E6671700899322 /* SDL_wave.h */; };
		04BD025C12E6671800899322 /* SDL_cpuinfo.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDD412E6671700899322 /* SDL_cpuinfo.c */; };
		04BD025D12E6671800899322 /* blank_cursor.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDD612E6671700899322 /* blank_cursor.h */; };
		04BD025E12E6671800899322 /* default_cursor.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDD712E6671700899322 /* default_cursor.h */; };
		04BD025F12E6671800899322 /* scancodes_darwin.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDD812E6671700899322 /* scancodes_darwin.h */; };
		04BD026012E6671800899322 /* scancodes_linux.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDD912E6671700899322 /* scancodes_linux.h */; };
		04BD026212E6671800899322 /* scancodes_xfree86.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDDB12E6671700899322 /* scancodes_xfree86.h */; };
		04BD026312E6671800899322 /* SDL_clipboardevents.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDDC12E6671700899322 /* SDL_clipboardevents.c */; };
		04BD026412E6671800899322 /* SDL_clipboardevents_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDDD12E6671700899322 /* SDL_clipboardevents_c.h */; };
		04BD026512E6671800899322 /* SDL_events.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDDE12E6671700899322 /* SDL_events.c */; };
		04BD026612E6671800899322 /* SDL_events_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDDF12E6671700899322 /* SDL_events_c.h */; };
		04BD026712E6671800899322 /* SDL_gesture.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDE012E6671700899322 /* SDL_gesture.c */; };
		04BD026812E6671800899322 /* SDL_gesture_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDE112E6671700899322 /* SDL_gesture_c.h */; };
		04BD026912E6671800899322 /* SDL_keyboard.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDE212E6671700899322 /* SDL_keyboard.c */; };
		04BD026A12E6671800899322 /* SDL_keyboard_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDE312E6671700899322 /* SDL_keyboard_c.h */; };
		04BD026B12E6671800899322 /* SDL_mouse.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDE412E6671700899322 /* SDL_mouse.c */; };
		04BD026C12E6671800899322 /* SDL_mouse_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDE512E6671700899322 /* SDL_mouse_c.h */; };
		04BD026D12E6671800899322 /* SDL_quit.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDE612E6671700899322 /* SDL_quit.c */; };
		04BD026E12E6671800899322 /* SDL_sysevents.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDE712E6671700899322 /* SDL_sysevents.h */; };
		04BD026F12E6671800899322 /* SDL_touch.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDE812E6671700899322 /* SDL_touch.c */; };
		04BD027012E6671800899322 /* SDL_touch_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDE912E6671700899322 /* SDL_touch_c.h */; };
		04BD027112E6671800899322 /* SDL_windowevents.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDEA12E6671700899322 /* SDL_windowevents.c */; };
		04BD027212E6671800899322 /* SDL_windowevents_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDEB12E6671700899322 /* SDL_windowevents_c.h */; };
		04BD027312E6671800899322 /* SDL_rwopsbundlesupport.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDEE12E6671700899322 /* SDL_rwopsbundlesupport.h */; };
		04BD027412E6671800899322 /* SDL_rwopsbundlesupport.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDEF12E6671700899322 /* SDL_rwopsbundlesupport.m */; };
		04BD027512E6671800899322 /* SDL_rwops.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDF012E6671700899322 /* SDL_rwops.c */; };
		04BD027612E6671800899322 /* SDL_syshaptic.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDF312E6671700899322 /* SDL_syshaptic.c */; };
		04BD027A12E6671800899322 /* SDL_haptic.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDFA12E6671700899322 /* SDL_haptic.c */; };
		04BD027B12E6671800899322 /* SDL_haptic_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDFB12E6671700899322 /* SDL_haptic_c.h */; };
		04BD027C12E6671800899322 /* SDL_syshaptic.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDFC12E6671700899322 /* SDL_syshaptic.h */; };
		04BD028112E6671800899322 /* SDL_sysjoystick.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE0712E6671700899322 /* SDL_sysjoystick.c */; };
		04BD028212E6671800899322 /* SDL_sysjoystick_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE0812E6671700899322 /* SDL_sysjoystick_c.h */; };
		04BD028B12E6671800899322 /* SDL_joystick.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE1612E6671700899322 /* SDL_joystick.c */; };
		04BD028C12E6671800899322 /* SDL_joystick_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE1712E6671700899322 /* SDL_joystick_c.h */; };
		04BD028D12E6671800899322 /* SDL_sysjoystick.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE1812E6671700899322 /* SDL_sysjoystick.h */; };
		04BD02A312E6671800899322 /* SDL_sysloadso.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE3312E6671700899322 /* SDL_sysloadso.c */; };
		04BD02AE12E6671800899322 /* SDL_syspower.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE4B12E6671700899322 /* SDL_syspower.c */; };
		04BD02B012E6671800899322 /* SDL_power.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE4E12E6671700899322 /* SDL_power.c */; };
		04BD02B512E6671800899322 /* SDL_assert_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE5512E6671700899322 /* SDL_assert_c.h */; };
		04BD02B612E6671800899322 /* SDL_assert.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE5612E6671700899322 /* SDL_assert.c */; };
		04BD02B812E6671800899322 /* SDL_error_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE5812E6671700899322 /* SDL_error_c.h */; };
		04BD02B912E6671800899322 /* SDL_error.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE5912E6671700899322 /* SDL_error.c */; };
		04BD02BC12E6671800899322 /* SDL.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE5C12E6671700899322 /* SDL.c */; };
		04BD02BD12E6671800899322 /* SDL_getenv.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE5E12E6671700899322 /* SDL_getenv.c */; };
		04BD02BE12E6671800899322 /* SDL_iconv.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE5F12E6671700899322 /* SDL_iconv.c */; };
		04BD02BF12E6671800899322 /* SDL_malloc.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE6012E6671700899322 /* SDL_malloc.c */; };
		04BD02C012E6671800899322 /* SDL_qsort.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE6112E6671700899322 /* SDL_qsort.c */; };
		04BD02C112E6671800899322 /* SDL_stdlib.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE6212E6671700899322 /* SDL_stdlib.c */; };
		04BD02C212E6671800899322 /* SDL_string.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE6312E6671700899322 /* SDL_string.c */; };
		04BD02D712E6671800899322 /* SDL_syscond.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE7E12E6671800899322 /* SDL_syscond.c */; };
		04BD02D812E6671800899322 /* SDL_sysmutex.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE7F12E6671800899322 /* SDL_sysmutex.c */; };
		04BD02D912E6671800899322 /* SDL_sysmutex_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE8012E6671800899322 /* SDL_sysmutex_c.h */; };
		04BD02DA12E6671800899322 /* SDL_syssem.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE8112E6671800899322 /* SDL_syssem.c */; };
		04BD02DB12E6671800899322 /* SDL_systhread.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE8212E6671800899322 /* SDL_systhread.c */; };
		04BD02DC12E6671800899322 /* SDL_systhread_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE8312E6671800899322 /* SDL_systhread_c.h */; };
		04BD02E312E6671800899322 /* SDL_systhread.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE8B12E6671800899322 /* SDL_systhread.h */; };
		04BD02E412E6671800899322 /* SDL_thread.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE8C12E6671800899322 /* SDL_thread.c */; };
		04BD02E512E6671800899322 /* SDL_thread_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE8D12E6671800899322 /* SDL_thread_c.h */; };
		04BD02F112E6671800899322 /* SDL_timer.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE9F12E6671800899322 /* SDL_timer.c */; };
		04BD02F212E6671800899322 /* SDL_timer_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFEA012E6671800899322 /* SDL_timer_c.h */; };
		04BD02F312E6671800899322 /* SDL_systimer.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFEA212E6671800899322 /* SDL_systimer.c */; };
		04BD030D12E6671800899322 /* SDL_cocoaclipboard.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFEC212E6671800899322 /* SDL_cocoaclipboard.h */; };
		04BD030E12E6671800899322 /* SDL_cocoaclipboard.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFEC312E6671800899322 /* SDL_cocoaclipboard.m */; };
		04BD030F12E6671800899322 /* SDL_cocoaevents.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFEC412E6671800899322 /* SDL_cocoaevents.h */; };
		04BD031012E6671800899322 /* SDL_cocoaevents.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFEC512E6671800899322 /* SDL_cocoaevents.m */; };
		04BD031112E6671800899322 /* SDL_cocoakeyboard.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFEC612E6671800899322 /* SDL_cocoakeyboard.h */; };
		04BD031212E6671800899322 /* SDL_cocoakeyboard.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFEC712E6671800899322 /* SDL_cocoakeyboard.m */; };
		04BD031312E6671800899322 /* SDL_cocoamodes.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFEC812E6671800899322 /* SDL_cocoamodes.h */; };
		04BD031412E6671800899322 /* SDL_cocoamodes.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFEC912E6671800899322 /* SDL_cocoamodes.m */; };
		04BD031512E6671800899322 /* SDL_cocoamouse.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFECA12E6671800899322 /* SDL_cocoamouse.h */; };
		04BD031612E6671800899322 /* SDL_cocoamouse.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFECB12E6671800899322 /* SDL_cocoamouse.m */; };
		04BD031712E6671800899322 /* SDL_cocoaopengl.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFECC12E6671800899322 /* SDL_cocoaopengl.h */; };
		04BD031812E6671800899322 /* SDL_cocoaopengl.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFECD12E6671800899322 /* SDL_cocoaopengl.m */; };
		04BD031912E6671800899322 /* SDL_cocoashape.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFECE12E6671800899322 /* SDL_cocoashape.h */; };
		04BD031A12E6671800899322 /* SDL_cocoashape.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFECF12E6671800899322 /* SDL_cocoashape.m */; };
		04BD031B12E6671800899322 /* SDL_cocoavideo.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFED012E6671800899322 /* SDL_cocoavideo.h */; };
		04BD031C12E6671800899322 /* SDL_cocoavideo.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFED112E6671800899322 /* SDL_cocoavideo.m */; };
		04BD031D12E6671800899322 /* SDL_cocoawindow.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFED212E6671800899322 /* SDL_cocoawindow.h */; };
		04BD031E12E6671800899322 /* SDL_cocoawindow.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFED312E6671800899322 /* SDL_cocoawindow.m */; };
		04BD033112E6671800899322 /* SDL_nullevents.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFEE812E6671800899322 /* SDL_nullevents.c */; };
		04BD033212E6671800899322 /* SDL_nullevents_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFEE912E6671800899322 /* SDL_nullevents_c.h */; };
		04BD033512E6671800899322 /* SDL_nullvideo.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFEEC12E6671800899322 /* SDL_nullvideo.c */; };
		04BD033612E6671800899322 /* SDL_nullvideo.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFEED12E6671800899322 /* SDL_nullvideo.h */; };
		04BD038F12E6671800899322 /* SDL_blit.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF4E12E6671800899322 /* SDL_blit.c */; };
		04BD039012E6671800899322 /* SDL_blit.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFF4F12E6671800899322 /* SDL_blit.h */; };
		04BD039112E6671800899322 /* SDL_blit_0.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5012E6671800899322 /* SDL_blit_0.c */; };
		04BD039212E6671800899322 /* SDL_blit_1.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5112E6671800899322 /* SDL_blit_1.c */; };
		04BD039312E6671800899322 /* SDL_blit_A.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5212E6671800899322 /* SDL_blit_A.c */; };
		04BD039412E6671800899322 /* SDL_blit_auto.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5312E6671800899322 /* SDL_blit_auto.c */; };
		04BD039512E6671800899322 /* SDL_blit_auto.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFF5412E6671800899322 /* SDL_blit_auto.h */; };
		04BD039612E6671800899322 /* SDL_blit_copy.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5512E6671800899322 /* SDL_blit_copy.c */; };
		04BD039712E6671800899322 /* SDL_blit_copy.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFF5612E6671800899322 /* SDL_blit_copy.h */; };
		04BD039812E6671800899322 /* SDL_blit_N.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5712E6671800899322 /* SDL_blit_N.c */; };
		04BD039912E6671800899322 /* SDL_blit_slow.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5812E6671800899322 /* SDL_blit_slow.c */; };
		04BD039A12E6671800899322 /* SDL_blit_slow.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFF5912E6671800899322 /* SDL_blit_slow.h */; };
		04BD039B12E6671800899322 /* SDL_bmp.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5A12E6671800899322 /* SDL_bmp.c */; };
		04BD039C12E6671800899322 /* SDL_clipboard.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5B12E6671800899322 /* SDL_clipboard.c */; };
		04BD03A112E6671800899322 /* SDL_fillrect.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF6012E6671800899322 /* SDL_fillrect.c */; };
		04BD03A612E6671800899322 /* SDL_pixels.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF6512E6671800899322 /* SDL_pixels.c */; };
		04BD03A712E6671800899322 /* SDL_pixels_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFF6612E6671800899322 /* SDL_pixels_c.h */; };
		04BD03A812E6671800899322 /* SDL_rect.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF6712E6671800899322 /* SDL_rect.c */; };
		04BD03B012E6671800899322 /* SDL_RLEaccel.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF6F12E6671800899322 /* SDL_RLEaccel.c */; };
		04BD03B112E6671800899322 /* SDL_RLEaccel_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFF7012E6671800899322 /* SDL_RLEaccel_c.h */; };
		04BD03B212E6671800899322 /* SDL_shape.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF7112E6671800899322 /* SDL_shape.c */; };
		04BD03B312E6671800899322 /* SDL_shape_internals.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFF7212E6671800899322 /* SDL_shape_internals.h */; };
		04BD03B412E6671800899322 /* SDL_stretch.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF7312E6671800899322 /* SDL_stretch.c */; };
		04BD03B512E6671800899322 /* SDL_surface.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF7412E6671800899322 /* SDL_surface.c */; };
		04BD03B612E6671800899322 /* SDL_sysvideo.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFF7512E6671800899322 /* SDL_sysvideo.h */; };
		04BD03B712E6671800899322 /* SDL_video.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF7612E6671800899322 /* SDL_video.c */; };
		04BD03F312E6671800899322 /* imKStoUCS.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFB812E6671800899322 /* imKStoUCS.c */; };
		04BD03F412E6671800899322 /* imKStoUCS.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFB912E6671800899322 /* imKStoUCS.h */; };
		04BD03F512E6671800899322 /* SDL_x11clipboard.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFBA12E6671800899322 /* SDL_x11clipboard.c */; };
		04BD03F612E6671800899322 /* SDL_x11clipboard.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFBB12E6671800899322 /* SDL_x11clipboard.h */; };
		04BD03F712E6671800899322 /* SDL_x11dyn.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFBC12E6671800899322 /* SDL_x11dyn.c */; };
		04BD03F812E6671800899322 /* SDL_x11dyn.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFBD12E6671800899322 /* SDL_x11dyn.h */; };
		04BD03F912E6671800899322 /* SDL_x11events.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFBE12E6671800899322 /* SDL_x11events.c */; };
		04BD03FA12E6671800899322 /* SDL_x11events.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFBF12E6671800899322 /* SDL_x11events.h */; };
		04BD03FD12E6671800899322 /* SDL_x11keyboard.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFC212E6671800899322 /* SDL_x11keyboard.c */; };
		04BD03FE12E6671800899322 /* SDL_x11keyboard.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFC312E6671800899322 /* SDL_x11keyboard.h */; };
		04BD03FF12E6671800899322 /* SDL_x11modes.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFC412E6671800899322 /* SDL_x11modes.c */; };
		04BD040012E6671800899322 /* SDL_x11modes.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFC512E6671800899322 /* SDL_x11modes.h */; };
		04BD040112E6671800899322 /* SDL_x11mouse.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFC612E6671800899322 /* SDL_x11mouse.c */; };
		04BD040212E6671800899322 /* SDL_x11mouse.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFC712E6671800899322 /* SDL_x11mouse.h */; };
		04BD040312E6671800899322 /* SDL_x11opengl.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFC812E6671800899322 /* SDL_x11opengl.c */; };
		04BD040412E6671800899322 /* SDL_x11opengl.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFC912E6671800899322 /* SDL_x11opengl.h */; };
		04BD040512E6671800899322 /* SDL_x11opengles.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFCA12E6671800899322 /* SDL_x11opengles.c */; };
		04BD040612E6671800899322 /* SDL_x11opengles.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFCB12E6671800899322 /* SDL_x11opengles.h */; };
		04BD040912E6671800899322 /* SDL_x11shape.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFCE12E6671800899322 /* SDL_x11shape.c */; };
		04BD040A12E6671800899322 /* SDL_x11shape.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFCF12E6671800899322 /* SDL_x11shape.h */; };
		04BD040B12E6671800899322 /* SDL_x11sym.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFD012E6671800899322 /* SDL_x11sym.h */; };
		04BD040C12E6671800899322 /* SDL_x11touch.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFD112E6671800899322 /* SDL_x11touch.c */; };
		04BD040D12E6671800899322 /* SDL_x11touch.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFD212E6671800899322 /* SDL_x11touch.h */; };
		04BD040E12E6671800899322 /* SDL_x11video.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFD312E6671800899322 /* SDL_x11video.c */; };
		04BD040F12E6671800899322 /* SDL_x11video.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFD412E6671800899322 /* SDL_x11video.h */; };
		04BD041012E6671800899322 /* SDL_x11window.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFD512E6671800899322 /* SDL_x11window.c */; };
		04BD041112E6671800899322 /* SDL_x11window.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFD612E6671800899322 /* SDL_x11window.h */; };
		04BDFFFB12E6671800899322 /* SDL_atomic.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFD7412E6671700899322 /* SDL_atomic.c */; };
		04BDFFFC12E6671800899322 /* SDL_spinlock.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFD7512E6671700899322 /* SDL_spinlock.c */; };
		04F7803912FB748500FC43C0 /* SDL_nullframebuffer_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04F7803712FB748500FC43C0 /* SDL_nullframebuffer_c.h */; };
		04F7803A12FB748500FC43C0 /* SDL_nullframebuffer.c in Sources */ = {isa = PBXBuildFile; fileRef = 04F7803812FB748500FC43C0 /* SDL_nullframebuffer.c */; };
		04F7803B12FB748500FC43C0 /* SDL_nullframebuffer_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04F7803712FB748500FC43C0 /* SDL_nullframebuffer_c.h */; };
		04F7803C12FB748500FC43C0 /* SDL_nullframebuffer.c in Sources */ = {isa = PBXBuildFile; fileRef = 04F7803812FB748500FC43C0 /* SDL_nullframebuffer.c */; };
		04F7804912FB74A200FC43C0 /* SDL_blendfillrect.c in Sources */ = {isa = PBXBuildFile; fileRef = 04F7803D12FB74A200FC43C0 /* SDL_blendfillrect.c */; };
		04F7804A12FB74A200FC43C0 /* SDL_blendfillrect.h in Headers */ = {isa = PBXBuildFile; fileRef = 04F7803E12FB74A200FC43C0 /* SDL_blendfillrect.h */; };
		04F7804B12FB74A200FC43C0 /* SDL_blendline.c in Sources */ = {isa = PBXBuildFile; fileRef = 04F7803F12FB74A200FC43C0 /* SDL_blendline.c */; };
		04F7804C12FB74A200FC43C0 /* SDL_blendline.h in Headers */ = {isa = PBXBuildFile; fileRef = 04F7804012FB74A200FC43C0 /* SDL_blendline.h */; };
		04F7804D12FB74A200FC43C0 /* SDL_blendpoint.c in Sources */ = {isa = PBXBuildFile; fileRef = 04F7804112FB74A200FC43C0 /* SDL_blendpoint.c */; };
		04F7804E12FB74A200FC43C0 /* SDL_blendpoint.h in Headers */ = {isa = PBXBuildFile; fileRef = 04F7804212FB74A200FC43C0 /* SDL_blendpoint.h */; };
		04F7804F12FB74A200FC43C0 /* SDL_draw.h in Headers */ = {isa = PBXBuildFile; fileRef = 04F7804312FB74A200FC43C0 /* SDL_draw.h */; };
		04F7805012FB74A200FC43C0 /* SDL_drawline.c in Sources */ = {isa = PBXBuildFile; fileRef = 04F7804412FB74A200FC43C0 /* SDL_drawline.c */; };
		04F7805112FB74A200FC43C0 /* SDL_drawline.h in Headers */ = {isa = PBXBuildFile; fileRef = 04F7804512FB74A200FC43C0 /* SDL_drawline.h */; };
		04F7805212FB74A200FC43C0 /* SDL_drawpoint.c in Sources */ = {isa = PBXBuildFile; fileRef = 04F7804612FB74A200FC43C0 /* SDL_drawpoint.c */; };
		04F7805312FB74A200FC43C0 /* SDL_drawpoint.h in Headers */ = {isa = PBXBuildFile; fileRef = 04F7804712FB74A200FC43C0 /* SDL_drawpoint.h */; };
		04F7805512FB74A200FC43C0 /* SDL_blendfillrect.c in Sources */ = {isa = PBXBuildFile; fileRef = 04F7803D12FB74A200FC43C0 /* SDL_blendfillrect.c */; };
		04F7805612FB74A200FC43C0 /* SDL_blendfillrect.h in Headers */ = {isa = PBXBuildFile; fileRef = 04F7803E12FB74A200FC43C0 /* SDL_blendfillrect.h */; };
		04F7805712FB74A200FC43C0 /* SDL_blendline.c in Sources */ = {isa = PBXBuildFile; fileRef = 04F7803F12FB74A200FC43C0 /* SDL_blendline.c */; };
		04F7805812FB74A200FC43C0 /* SDL_blendline.h in Headers */ = {isa = PBXBuildFile; fileRef = 04F7804012FB74A200FC43C0 /* SDL_blendline.h */; };
		04F7805912FB74A200FC43C0 /* SDL_blendpoint.c in Sources */ = {isa = PBXBuildFile; fileRef = 04F7804112FB74A200FC43C0 /* SDL_blendpoint.c */; };
		04F7805A12FB74A200FC43C0 /* SDL_blendpoint.h in Headers */ = {isa = PBXBuildFile; fileRef = 04F7804212FB74A200FC43C0 /* SDL_blendpoint.h */; };
		04F7805B12FB74A200FC43C0 /* SDL_draw.h in Headers */ = {isa = PBXBuildFile; fileRef = 04F7804312FB74A200FC43C0 /* SDL_draw.h */; };
		04F7805C12FB74A200FC43C0 /* SDL_drawline.c in Sources */ = {isa = PBXBuildFile; fileRef = 04F7804412FB74A200FC43C0 /* SDL_drawline.c */; };
		04F7805D12FB74A200FC43C0 /* SDL_drawline.h in Headers */ = {isa = PBXBuildFile; fileRef = 04F7804512FB74A200FC43C0 /* SDL_drawline.h */; };
		04F7805E12FB74A200FC43C0 /* SDL_drawpoint.c in Sources */ = {isa = PBXBuildFile; fileRef = 04F7804612FB74A200FC43C0 /* SDL_drawpoint.c */; };
		04F7805F12FB74A200FC43C0 /* SDL_drawpoint.h in Headers */ = {isa = PBXBuildFile; fileRef = 04F7804712FB74A200FC43C0 /* SDL_drawpoint.h */; };
		4D16644E1EDD6023003DE88E /* SDL_vulkan_internal.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D16644C1EDD6023003DE88E /* SDL_vulkan_internal.h */; };
		4D16644F1EDD6023003DE88E /* SDL_vulkan_utils.c in Sources */ = {isa = PBXBuildFile; fileRef = 4D16644D1EDD6023003DE88E /* SDL_vulkan_utils.c */; };
		4D1664531EDD60AD003DE88E /* SDL_cocoametalview.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D1664501EDD60AD003DE88E /* SDL_cocoametalview.m */; };
		4D1664541EDD60AD003DE88E /* SDL_cocoavulkan.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D1664511EDD60AD003DE88E /* SDL_cocoavulkan.h */; };
		4D1664551EDD60AD003DE88E /* SDL_cocoavulkan.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D1664521EDD60AD003DE88E /* SDL_cocoavulkan.m */; };
		4D1664561EDD61DA003DE88E /* SDL_vulkan_utils.c in Sources */ = {isa = PBXBuildFile; fileRef = 4D16644D1EDD6023003DE88E /* SDL_vulkan_utils.c */; };
		4D1664571EDD61F0003DE88E /* SDL_cocoametalview.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D1664501EDD60AD003DE88E /* SDL_cocoametalview.m */; };
		4D1664581EDD61F0003DE88E /* SDL_cocoavulkan.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D1664521EDD60AD003DE88E /* SDL_cocoavulkan.m */; };
		4D1664591EDD621B003DE88E /* SDL_vulkan_utils.c in Sources */ = {isa = PBXBuildFile; fileRef = 4D16644D1EDD6023003DE88E /* SDL_vulkan_utils.c */; };
		4D16645A1EDD6235003DE88E /* SDL_cocoametalview.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D1664501EDD60AD003DE88E /* SDL_cocoametalview.m */; };
		4D16645B1EDD6235003DE88E /* SDL_cocoavulkan.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D1664521EDD60AD003DE88E /* SDL_cocoavulkan.m */; };
		4D7517291EE2562B00820EEA /* SDL_cocoametalview.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D7517281EE2562B00820EEA /* SDL_cocoametalview.h */; };
		56115BBB1DF72C6D00F47E1E /* SDL_dataqueue.c in Sources */ = {isa = PBXBuildFile; fileRef = 56115BB91DF72C6D00F47E1E /* SDL_dataqueue.c */; };
		56115BBC1DF72C6D00F47E1E /* SDL_dataqueue.h in Headers */ = {isa = PBXBuildFile; fileRef = 56115BBA1DF72C6D00F47E1E /* SDL_dataqueue.h */; };
		562C4AE91D8F496200AF9EBE /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A7381E931D8B69C300B177DD /* AudioToolbox.framework */; };
		562C4AEA1D8F496300AF9EBE /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A7381E931D8B69C300B177DD /* AudioToolbox.framework */; };
		562D3C7C1D8F4933003FEEE6 /* SDL_coreaudio.m in Sources */ = {isa = PBXBuildFile; fileRef = FABA34C61D8B5DB100915323 /* SDL_coreaudio.m */; };
		562D3C7D1D8F4933003FEEE6 /* SDL_coreaudio.m in Sources */ = {isa = PBXBuildFile; fileRef = FABA34C61D8B5DB100915323 /* SDL_coreaudio.m */; };
		564624361FF821C20074AC87 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 564624351FF821B80074AC87 /* QuartzCore.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		564624381FF821DA0074AC87 /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 564624371FF821CB0074AC87 /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		564624391FF821EF0074AC87 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 564624351FF821B80074AC87 /* QuartzCore.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		5646243A1FF821FF0074AC87 /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 564624371FF821CB0074AC87 /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		5646243B1FF822100074AC87 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 564624351FF821B80074AC87 /* QuartzCore.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		5646243C1FF822170074AC87 /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 564624371FF821CB0074AC87 /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		566CDE8F148F0AC200C5A9BB /* SDL_dropevents_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 566CDE8D148F0AC200C5A9BB /* SDL_dropevents_c.h */; };
		566CDE90148F0AC200C5A9BB /* SDL_dropevents.c in Sources */ = {isa = PBXBuildFile; fileRef = 566CDE8E148F0AC200C5A9BB /* SDL_dropevents.c */; };
		567E2F1C17C44BB2005F1892 /* SDL_sysfilesystem.m in Sources */ = {isa = PBXBuildFile; fileRef = 567E2F1B17C44BB2005F1892 /* SDL_sysfilesystem.m */; };
		567E2F2117C44C35005F1892 /* SDL_filesystem.h in Headers */ = {isa = PBXBuildFile; fileRef = 567E2F2017C44C35005F1892 /* SDL_filesystem.h */; settings = {ATTRIBUTES = (Public, ); }; };
		56A670091856545C0007D20F /* SDL_internal.h in Headers */ = {isa = PBXBuildFile; fileRef = 56A670081856545C0007D20F /* SDL_internal.h */; };
		56A6700A1856545C0007D20F /* SDL_internal.h in Headers */ = {isa = PBXBuildFile; fileRef = 56A670081856545C0007D20F /* SDL_internal.h */; };
		56A6700B1856545C0007D20F /* SDL_internal.h in Headers */ = {isa = PBXBuildFile; fileRef = 56A670081856545C0007D20F /* SDL_internal.h */; };
		56A67021185654B40007D20F /* SDL_dynapi_procs.h in Headers */ = {isa = PBXBuildFile; fileRef = 56A6701D185654B40007D20F /* SDL_dynapi_procs.h */; };
		56A67022185654B40007D20F /* SDL_dynapi_procs.h in Headers */ = {isa = PBXBuildFile; fileRef = 56A6701D185654B40007D20F /* SDL_dynapi_procs.h */; };
		56A67023185654B40007D20F /* SDL_dynapi_procs.h in Headers */ = {isa = PBXBuildFile; fileRef = 56A6701D185654B40007D20F /* SDL_dynapi_procs.h */; };
		56A67024185654B40007D20F /* SDL_dynapi.c in Sources */ = {isa = PBXBuildFile; fileRef = 56A6701E185654B40007D20F /* SDL_dynapi.c */; };
		56A67025185654B40007D20F /* SDL_dynapi.c in Sources */ = {isa = PBXBuildFile; fileRef = 56A6701E185654B40007D20F /* SDL_dynapi.c */; };
		56A67026185654B40007D20F /* SDL_dynapi.c in Sources */ = {isa = PBXBuildFile; fileRef = 56A6701E185654B40007D20F /* SDL_dynapi.c */; };
		56A67027185654B40007D20F /* SDL_dynapi.h in Headers */ = {isa = PBXBuildFile; fileRef = 56A6701F185654B40007D20F /* SDL_dynapi.h */; };
		56A67028185654B40007D20F /* SDL_dynapi.h in Headers */ = {isa = PBXBuildFile; fileRef = 56A6701F185654B40007D20F /* SDL_dynapi.h */; };
		56A67029185654B40007D20F /* SDL_dynapi.h in Headers */ = {isa = PBXBuildFile; fileRef = 56A6701F185654B40007D20F /* SDL_dynapi.h */; };
		56A6702A185654B40007D20F /* SDL_dynapi_overrides.h in Headers */ = {isa = PBXBuildFile; fileRef = 56A67020185654B40007D20F /* SDL_dynapi_overrides.h */; };
		56A6702B185654B40007D20F /* SDL_dynapi_overrides.h in Headers */ = {isa = PBXBuildFile; fileRef = 56A67020185654B40007D20F /* SDL_dynapi_overrides.h */; };
		56A6702C185654B40007D20F /* SDL_dynapi_overrides.h in Headers */ = {isa = PBXBuildFile; fileRef = 56A67020185654B40007D20F /* SDL_dynapi_overrides.h */; };
		56C5237E1D8F4985001F2F30 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A7381E951D8B69D600B177DD /* CoreAudio.framework */; };
		56C5237F1D8F4985001F2F30 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A7381E951D8B69D600B177DD /* CoreAudio.framework */; };
		56C523801D8F498B001F2F30 /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 00D0D08310675DD9004B05EF /* CoreFoundation.framework */; };
		56C523811D8F498C001F2F30 /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 00D0D08310675DD9004B05EF /* CoreFoundation.framework */; };
		56F9D55C1DF73B6B00C15B5D /* SDL_dataqueue.c in Sources */ = {isa = PBXBuildFile; fileRef = 56115BB91DF72C6D00F47E1E /* SDL_dataqueue.c */; };
		56F9D55D1DF73B6C00C15B5D /* SDL_dataqueue.c in Sources */ = {isa = PBXBuildFile; fileRef = 56115BB91DF72C6D00F47E1E /* SDL_dataqueue.c */; };
		56F9D55E1DF73B7C00C15B5D /* SDL_dataqueue.h in Headers */ = {isa = PBXBuildFile; fileRef = 56115BBA1DF72C6D00F47E1E /* SDL_dataqueue.h */; };
		56F9D55F1DF73B7D00C15B5D /* SDL_dataqueue.h in Headers */ = {isa = PBXBuildFile; fileRef = 56115BBA1DF72C6D00F47E1E /* SDL_dataqueue.h */; };
		5C2EF69F1FC987C6003F5197 /* SDL_gles2funcs.h in Headers */ = {isa = PBXBuildFile; fileRef = 5C2EF69B1FC987C6003F5197 /* SDL_gles2funcs.h */; };
		5C2EF6A01FC987C6003F5197 /* SDL_render_gles2.c in Sources */ = {isa = PBXBuildFile; fileRef = 5C2EF69C1FC987C6003F5197 /* SDL_render_gles2.c */; };
		5C2EF6A11FC987C6003F5197 /* SDL_shaders_gles2.h in Headers */ = {isa = PBXBuildFile; fileRef = 5C2EF69D1FC987C6003F5197 /* SDL_shaders_gles2.h */; };
		5C2EF6A21FC987C6003F5197 /* SDL_shaders_gles2.c in Sources */ = {isa = PBXBuildFile; fileRef = 5C2EF69E1FC987C6003F5197 /* SDL_shaders_gles2.c */; };
		5C2EF6A31FC98B38003F5197 /* SDL_yuv.c in Sources */ = {isa = PBXBuildFile; fileRef = AA9A7F131FB0209C00FED37F /* SDL_yuv.c */; };
		5C2EF6A41FC98B39003F5197 /* SDL_yuv.c in Sources */ = {isa = PBXBuildFile; fileRef = AA9A7F131FB0209C00FED37F /* SDL_yuv.c */; };
		5C2EF6A51FC98B6B003F5197 /* yuv_rgb.c in Sources */ = {isa = PBXBuildFile; fileRef = AA9A7F101FB0206300FED37F /* yuv_rgb.c */; };
		5C2EF6A61FC98B6C003F5197 /* yuv_rgb.c in Sources */ = {isa = PBXBuildFile; fileRef = AA9A7F101FB0206300FED37F /* yuv_rgb.c */; };
		5C2EF6A71FC98D2D003F5197 /* SDL_gles2funcs.h in Headers */ = {isa = PBXBuildFile; fileRef = 5C2EF69B1FC987C6003F5197 /* SDL_gles2funcs.h */; };
		5C2EF6A81FC98D2D003F5197 /* SDL_render_gles2.c in Sources */ = {isa = PBXBuildFile; fileRef = 5C2EF69C1FC987C6003F5197 /* SDL_render_gles2.c */; };
		5C2EF6A91FC98D2D003F5197 /* SDL_shaders_gles2.c in Sources */ = {isa = PBXBuildFile; fileRef = 5C2EF69E1FC987C6003F5197 /* SDL_shaders_gles2.c */; };
		5C2EF6AA1FC98D2D003F5197 /* SDL_shaders_gles2.h in Headers */ = {isa = PBXBuildFile; fileRef = 5C2EF69D1FC987C6003F5197 /* SDL_shaders_gles2.h */; };
		5C2EF6AB1FC98D2E003F5197 /* SDL_gles2funcs.h in Headers */ = {isa = PBXBuildFile; fileRef = 5C2EF69B1FC987C6003F5197 /* SDL_gles2funcs.h */; };
		5C2EF6AC1FC98D2E003F5197 /* SDL_render_gles2.c in Sources */ = {isa = PBXBuildFile; fileRef = 5C2EF69C1FC987C6003F5197 /* SDL_render_gles2.c */; };
		5C2EF6AD1FC98D2E003F5197 /* SDL_shaders_gles2.c in Sources */ = {isa = PBXBuildFile; fileRef = 5C2EF69E1FC987C6003F5197 /* SDL_shaders_gles2.c */; };
		5C2EF6AE1FC98D2E003F5197 /* SDL_shaders_gles2.h in Headers */ = {isa = PBXBuildFile; fileRef = 5C2EF69D1FC987C6003F5197 /* SDL_shaders_gles2.h */; };
		5C2EF6EE1FC9D0ED003F5197 /* SDL_cocoaopengles.m in Sources */ = {isa = PBXBuildFile; fileRef = 5C2EF6EC1FC9D0EC003F5197 /* SDL_cocoaopengles.m */; };
		5C2EF6EF1FC9D0ED003F5197 /* SDL_cocoaopengles.h in Headers */ = {isa = PBXBuildFile; fileRef = 5C2EF6ED1FC9D0ED003F5197 /* SDL_cocoaopengles.h */; };
		5C2EF6F01FC9D181003F5197 /* SDL_cocoaopengles.h in Headers */ = {isa = PBXBuildFile; fileRef = 5C2EF6ED1FC9D0ED003F5197 /* SDL_cocoaopengles.h */; };
		5C2EF6F11FC9D181003F5197 /* SDL_cocoaopengles.m in Sources */ = {isa = PBXBuildFile; fileRef = 5C2EF6EC1FC9D0EC003F5197 /* SDL_cocoaopengles.m */; };
		5C2EF6F21FC9D182003F5197 /* SDL_cocoaopengles.h in Headers */ = {isa = PBXBuildFile; fileRef = 5C2EF6ED1FC9D0ED003F5197 /* SDL_cocoaopengles.h */; };
		5C2EF6F31FC9D182003F5197 /* SDL_cocoaopengles.m in Sources */ = {isa = PBXBuildFile; fileRef = 5C2EF6EC1FC9D0EC003F5197 /* SDL_cocoaopengles.m */; };
		5C2EF6F71FC9EE35003F5197 /* SDL_rect_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 5C2EF6F41FC9EE34003F5197 /* SDL_rect_c.h */; };
		5C2EF6F81FC9EE35003F5197 /* SDL_egl.c in Sources */ = {isa = PBXBuildFile; fileRef = 5C2EF6F51FC9EE35003F5197 /* SDL_egl.c */; };
		5C2EF6F91FC9EE35003F5197 /* SDL_egl_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 5C2EF6F61FC9EE35003F5197 /* SDL_egl_c.h */; };
		5C2EF6FA1FC9EE64003F5197 /* SDL_egl_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 5C2EF6F61FC9EE35003F5197 /* SDL_egl_c.h */; };
		5C2EF6FB1FC9EE64003F5197 /* SDL_egl.c in Sources */ = {isa = PBXBuildFile; fileRef = 5C2EF6F51FC9EE35003F5197 /* SDL_egl.c */; };
		5C2EF6FC1FC9EE64003F5197 /* SDL_rect_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 5C2EF6F41FC9EE34003F5197 /* SDL_rect_c.h */; };
		5C2EF6FD1FC9EE65003F5197 /* SDL_egl_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 5C2EF6F61FC9EE35003F5197 /* SDL_egl_c.h */; };
		5C2EF6FE1FC9EE65003F5197 /* SDL_egl.c in Sources */ = {isa = PBXBuildFile; fileRef = 5C2EF6F51FC9EE35003F5197 /* SDL_egl.c */; };
		5C2EF6FF1FC9EE65003F5197 /* SDL_rect_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 5C2EF6F41FC9EE34003F5197 /* SDL_rect_c.h */; };
		5C2EF7011FC9EF10003F5197 /* SDL_egl.h in Headers */ = {isa = PBXBuildFile; fileRef = 5C2EF7001FC9EF0F003F5197 /* SDL_egl.h */; };
		A704170920F09A9800A82227 /* hid.c in Sources */ = {isa = PBXBuildFile; fileRef = A704170820F09A9800A82227 /* hid.c */; };
		A704170A20F09A9800A82227 /* hid.c in Sources */ = {isa = PBXBuildFile; fileRef = A704170820F09A9800A82227 /* hid.c */; };
		A704170B20F09A9800A82227 /* hid.c in Sources */ = {isa = PBXBuildFile; fileRef = A704170820F09A9800A82227 /* hid.c */; };
		A704171420F09AC900A82227 /* SDL_hidapijoystick.c in Sources */ = {isa = PBXBuildFile; fileRef = A704170D20F09AC800A82227 /* SDL_hidapijoystick.c */; };
		A704171520F09AC900A82227 /* SDL_hidapijoystick.c in Sources */ = {isa = PBXBuildFile; fileRef = A704170D20F09AC800A82227 /* SDL_hidapijoystick.c */; };
		A704171620F09AC900A82227 /* SDL_hidapijoystick.c in Sources */ = {isa = PBXBuildFile; fileRef = A704170D20F09AC800A82227 /* SDL_hidapijoystick.c */; };
		A704171720F09AC900A82227 /* SDL_hidapijoystick_c.h in Headers */ = {isa = PBXBuildFile; fileRef = A704170E20F09AC800A82227 /* SDL_hidapijoystick_c.h */; };
		A704171820F09AC900A82227 /* SDL_hidapijoystick_c.h in Headers */ = {isa = PBXBuildFile; fileRef = A704170E20F09AC800A82227 /* SDL_hidapijoystick_c.h */; };
		A704171920F09AC900A82227 /* SDL_hidapijoystick_c.h in Headers */ = {isa = PBXBuildFile; fileRef = A704170E20F09AC800A82227 /* SDL_hidapijoystick_c.h */; };
		A704171A20F09AC900A82227 /* SDL_hidapi_switch.c in Sources */ = {isa = PBXBuildFile; fileRef = A704170F20F09AC800A82227 /* SDL_hidapi_switch.c */; };
		A704171B20F09AC900A82227 /* SDL_hidapi_switch.c in Sources */ = {isa = PBXBuildFile; fileRef = A704170F20F09AC800A82227 /* SDL_hidapi_switch.c */; };
		A704171C20F09AC900A82227 /* SDL_hidapi_switch.c in Sources */ = {isa = PBXBuildFile; fileRef = A704170F20F09AC800A82227 /* SDL_hidapi_switch.c */; };
		A704171D20F09AC900A82227 /* controller_type.h in Headers */ = {isa = PBXBuildFile; fileRef = A704171020F09AC900A82227 /* controller_type.h */; };
		A704171E20F09AC900A82227 /* controller_type.h in Headers */ = {isa = PBXBuildFile; fileRef = A704171020F09AC900A82227 /* controller_type.h */; };
		A704171F20F09AC900A82227 /* controller_type.h in Headers */ = {isa = PBXBuildFile; fileRef = A704171020F09AC900A82227 /* controller_type.h */; };
		A704172020F09AC900A82227 /* SDL_hidapi_ps4.c in Sources */ = {isa = PBXBuildFile; fileRef = A704171120F09AC900A82227 /* SDL_hidapi_ps4.c */; };
		A704172120F09AC900A82227 /* SDL_hidapi_ps4.c in Sources */ = {isa = PBXBuildFile; fileRef = A704171120F09AC900A82227 /* SDL_hidapi_ps4.c */; };
		A704172220F09AC900A82227 /* SDL_hidapi_ps4.c in Sources */ = {isa = PBXBuildFile; fileRef = A704171120F09AC900A82227 /* SDL_hidapi_ps4.c */; };
		A704172320F09AC900A82227 /* SDL_hidapi_xboxone.c in Sources */ = {isa = PBXBuildFile; fileRef = A704171220F09AC900A82227 /* SDL_hidapi_xboxone.c */; };
		A704172420F09AC900A82227 /* SDL_hidapi_xboxone.c in Sources */ = {isa = PBXBuildFile; fileRef = A704171220F09AC900A82227 /* SDL_hidapi_xboxone.c */; };
		A704172520F09AC900A82227 /* SDL_hidapi_xboxone.c in Sources */ = {isa = PBXBuildFile; fileRef = A704171220F09AC900A82227 /* SDL_hidapi_xboxone.c */; };
		A704172620F09AC900A82227 /* SDL_hidapi_xbox360.c in Sources */ = {isa = PBXBuildFile; fileRef = A704171320F09AC900A82227 /* SDL_hidapi_xbox360.c */; };
		A704172720F09AC900A82227 /* SDL_hidapi_xbox360.c in Sources */ = {isa = PBXBuildFile; fileRef = A704171320F09AC900A82227 /* SDL_hidapi_xbox360.c */; };
		A704172820F09AC900A82227 /* SDL_hidapi_xbox360.c in Sources */ = {isa = PBXBuildFile; fileRef = A704171320F09AC900A82227 /* SDL_hidapi_xbox360.c */; };
		A7381E961D8B69D600B177DD /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A7381E951D8B69D600B177DD /* CoreAudio.framework */; };
		A7381E971D8B6A0300B177DD /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A7381E931D8B69C300B177DD /* AudioToolbox.framework */; };
		A77E6EB4167AB0A90010E40B /* SDL_gamecontroller.h in Headers */ = {isa = PBXBuildFile; fileRef = A77E6EB3167AB0A90010E40B /* SDL_gamecontroller.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A77E6EB5167AB0A90010E40B /* SDL_gamecontroller.h in Headers */ = {isa = PBXBuildFile; fileRef = A77E6EB3167AB0A90010E40B /* SDL_gamecontroller.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA0AD09D16648D1700CE5896 /* SDL_gamecontroller.c in Sources */ = {isa = PBXBuildFile; fileRef = BBFC088A164C6514003E6A99 /* SDL_gamecontroller.c */; };
		AA0F8491178D5ECC00823F9D /* SDL_systls.c in Sources */ = {isa = PBXBuildFile; fileRef = AA0F8490178D5ECC00823F9D /* SDL_systls.c */; };
		AA0F8492178D5ECC00823F9D /* SDL_systls.c in Sources */ = {isa = PBXBuildFile; fileRef = AA0F8490178D5ECC00823F9D /* SDL_systls.c */; };
		AA0F8493178D5ECC00823F9D /* SDL_systls.c in Sources */ = {isa = PBXBuildFile; fileRef = AA0F8490178D5ECC00823F9D /* SDL_systls.c */; };
		AA41F88014B8F1F500993C4F /* SDL_dropevents.c in Sources */ = {isa = PBXBuildFile; fileRef = 566CDE8E148F0AC200C5A9BB /* SDL_dropevents.c */; };
		AA628ACA159367B7005138DD /* SDL_rotate.c in Sources */ = {isa = PBXBuildFile; fileRef = AA628AC8159367B7005138DD /* SDL_rotate.c */; };
		AA628ACB159367B7005138DD /* SDL_rotate.c in Sources */ = {isa = PBXBuildFile; fileRef = AA628AC8159367B7005138DD /* SDL_rotate.c */; };
		AA628ACC159367B7005138DD /* SDL_rotate.h in Headers */ = {isa = PBXBuildFile; fileRef = AA628AC9159367B7005138DD /* SDL_rotate.h */; };
		AA628ACD159367B7005138DD /* SDL_rotate.h in Headers */ = {isa = PBXBuildFile; fileRef = AA628AC9159367B7005138DD /* SDL_rotate.h */; };
		AA628AD1159367F2005138DD /* SDL_x11xinput2.c in Sources */ = {isa = PBXBuildFile; fileRef = AA628ACF159367F2005138DD /* SDL_x11xinput2.c */; };
		AA628AD2159367F2005138DD /* SDL_x11xinput2.c in Sources */ = {isa = PBXBuildFile; fileRef = AA628ACF159367F2005138DD /* SDL_x11xinput2.c */; };
		AA628AD3159367F2005138DD /* SDL_x11xinput2.h in Headers */ = {isa = PBXBuildFile; fileRef = AA628AD0159367F2005138DD /* SDL_x11xinput2.h */; };
		AA628AD4159367F2005138DD /* SDL_x11xinput2.h in Headers */ = {isa = PBXBuildFile; fileRef = AA628AD0159367F2005138DD /* SDL_x11xinput2.h */; };
		AA7557FA1595D4D800BBD41B /* begin_code.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557C71595D4D800BBD41B /* begin_code.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7557FB1595D4D800BBD41B /* begin_code.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557C71595D4D800BBD41B /* begin_code.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7557FC1595D4D800BBD41B /* close_code.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557C81595D4D800BBD41B /* close_code.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7557FD1595D4D800BBD41B /* close_code.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557C81595D4D800BBD41B /* close_code.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7557FE1595D4D800BBD41B /* SDL_assert.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557C91595D4D800BBD41B /* SDL_assert.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7557FF1595D4D800BBD41B /* SDL_assert.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557C91595D4D800BBD41B /* SDL_assert.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558001595D4D800BBD41B /* SDL_atomic.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557CA1595D4D800BBD41B /* SDL_atomic.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558011595D4D800BBD41B /* SDL_atomic.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557CA1595D4D800BBD41B /* SDL_atomic.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558021595D4D800BBD41B /* SDL_audio.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557CB1595D4D800BBD41B /* SDL_audio.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558031595D4D800BBD41B /* SDL_audio.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557CB1595D4D800BBD41B /* SDL_audio.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558041595D4D800BBD41B /* SDL_blendmode.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557CC1595D4D800BBD41B /* SDL_blendmode.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558051595D4D800BBD41B /* SDL_blendmode.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557CC1595D4D800BBD41B /* SDL_blendmode.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558061595D4D800BBD41B /* SDL_clipboard.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557CD1595D4D800BBD41B /* SDL_clipboard.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558071595D4D800BBD41B /* SDL_clipboard.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557CD1595D4D800BBD41B /* SDL_clipboard.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558081595D4D800BBD41B /* SDL_config_macosx.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557CE1595D4D800BBD41B /* SDL_config_macosx.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558091595D4D800BBD41B /* SDL_config_macosx.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557CE1595D4D800BBD41B /* SDL_config_macosx.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75580A1595D4D800BBD41B /* SDL_config.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557CF1595D4D800BBD41B /* SDL_config.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75580B1595D4D800BBD41B /* SDL_config.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557CF1595D4D800BBD41B /* SDL_config.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75580C1595D4D800BBD41B /* SDL_copying.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D01595D4D800BBD41B /* SDL_copying.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75580D1595D4D800BBD41B /* SDL_copying.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D01595D4D800BBD41B /* SDL_copying.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75580E1595D4D800BBD41B /* SDL_cpuinfo.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D11595D4D800BBD41B /* SDL_cpuinfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75580F1595D4D800BBD41B /* SDL_cpuinfo.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D11595D4D800BBD41B /* SDL_cpuinfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558101595D4D800BBD41B /* SDL_endian.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D21595D4D800BBD41B /* SDL_endian.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558111595D4D800BBD41B /* SDL_endian.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D21595D4D800BBD41B /* SDL_endian.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558121595D4D800BBD41B /* SDL_error.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D31595D4D800BBD41B /* SDL_error.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558131595D4D800BBD41B /* SDL_error.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D31595D4D800BBD41B /* SDL_error.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558141595D4D800BBD41B /* SDL_events.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D41595D4D800BBD41B /* SDL_events.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558151595D4D800BBD41B /* SDL_events.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D41595D4D800BBD41B /* SDL_events.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558161595D4D800BBD41B /* SDL_gesture.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D51595D4D800BBD41B /* SDL_gesture.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558171595D4D800BBD41B /* SDL_gesture.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D51595D4D800BBD41B /* SDL_gesture.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558181595D4D800BBD41B /* SDL_haptic.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D61595D4D800BBD41B /* SDL_haptic.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558191595D4D800BBD41B /* SDL_haptic.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D61595D4D800BBD41B /* SDL_haptic.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75581A1595D4D800BBD41B /* SDL_hints.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D71595D4D800BBD41B /* SDL_hints.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75581B1595D4D800BBD41B /* SDL_hints.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D71595D4D800BBD41B /* SDL_hints.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75581E1595D4D800BBD41B /* SDL_joystick.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D91595D4D800BBD41B /* SDL_joystick.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75581F1595D4D800BBD41B /* SDL_joystick.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D91595D4D800BBD41B /* SDL_joystick.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558201595D4D800BBD41B /* SDL_keyboard.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557DA1595D4D800BBD41B /* SDL_keyboard.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558211595D4D800BBD41B /* SDL_keyboard.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557DA1595D4D800BBD41B /* SDL_keyboard.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558221595D4D800BBD41B /* SDL_keycode.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557DB1595D4D800BBD41B /* SDL_keycode.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558231595D4D800BBD41B /* SDL_keycode.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557DB1595D4D800BBD41B /* SDL_keycode.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558241595D4D800BBD41B /* SDL_loadso.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557DC1595D4D800BBD41B /* SDL_loadso.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558251595D4D800BBD41B /* SDL_loadso.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557DC1595D4D800BBD41B /* SDL_loadso.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558261595D4D800BBD41B /* SDL_log.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557DD1595D4D800BBD41B /* SDL_log.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558271595D4D800BBD41B /* SDL_log.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557DD1595D4D800BBD41B /* SDL_log.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558281595D4D800BBD41B /* SDL_main.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557DE1595D4D800BBD41B /* SDL_main.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558291595D4D800BBD41B /* SDL_main.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557DE1595D4D800BBD41B /* SDL_main.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75582A1595D4D800BBD41B /* SDL_mouse.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557DF1595D4D800BBD41B /* SDL_mouse.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75582B1595D4D800BBD41B /* SDL_mouse.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557DF1595D4D800BBD41B /* SDL_mouse.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75582C1595D4D800BBD41B /* SDL_mutex.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E01595D4D800BBD41B /* SDL_mutex.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75582D1595D4D800BBD41B /* SDL_mutex.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E01595D4D800BBD41B /* SDL_mutex.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75582E1595D4D800BBD41B /* SDL_name.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E11595D4D800BBD41B /* SDL_name.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75582F1595D4D800BBD41B /* SDL_name.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E11595D4D800BBD41B /* SDL_name.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558301595D4D800BBD41B /* SDL_opengl.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E21595D4D800BBD41B /* SDL_opengl.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558311595D4D800BBD41B /* SDL_opengl.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E21595D4D800BBD41B /* SDL_opengl.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558321595D4D800BBD41B /* SDL_opengles.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E31595D4D800BBD41B /* SDL_opengles.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558331595D4D800BBD41B /* SDL_opengles.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E31595D4D800BBD41B /* SDL_opengles.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558341595D4D800BBD41B /* SDL_opengles2.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E41595D4D800BBD41B /* SDL_opengles2.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558351595D4D800BBD41B /* SDL_opengles2.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E41595D4D800BBD41B /* SDL_opengles2.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558361595D4D800BBD41B /* SDL_pixels.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E51595D4D800BBD41B /* SDL_pixels.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558371595D4D800BBD41B /* SDL_pixels.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E51595D4D800BBD41B /* SDL_pixels.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558381595D4D800BBD41B /* SDL_platform.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E61595D4D800BBD41B /* SDL_platform.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558391595D4D800BBD41B /* SDL_platform.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E61595D4D800BBD41B /* SDL_platform.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75583A1595D4D800BBD41B /* SDL_power.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E71595D4D800BBD41B /* SDL_power.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75583B1595D4D800BBD41B /* SDL_power.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E71595D4D800BBD41B /* SDL_power.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75583C1595D4D800BBD41B /* SDL_quit.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E81595D4D800BBD41B /* SDL_quit.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75583D1595D4D800BBD41B /* SDL_quit.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E81595D4D800BBD41B /* SDL_quit.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75583E1595D4D800BBD41B /* SDL_rect.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E91595D4D800BBD41B /* SDL_rect.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75583F1595D4D800BBD41B /* SDL_rect.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E91595D4D800BBD41B /* SDL_rect.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558401595D4D800BBD41B /* SDL_render.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557EA1595D4D800BBD41B /* SDL_render.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558411595D4D800BBD41B /* SDL_render.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557EA1595D4D800BBD41B /* SDL_render.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558421595D4D800BBD41B /* SDL_revision.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557EB1595D4D800BBD41B /* SDL_revision.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558431595D4D800BBD41B /* SDL_revision.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557EB1595D4D800BBD41B /* SDL_revision.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558441595D4D800BBD41B /* SDL_rwops.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557EC1595D4D800BBD41B /* SDL_rwops.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558451595D4D800BBD41B /* SDL_rwops.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557EC1595D4D800BBD41B /* SDL_rwops.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558461595D4D800BBD41B /* SDL_scancode.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557ED1595D4D800BBD41B /* SDL_scancode.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558471595D4D800BBD41B /* SDL_scancode.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557ED1595D4D800BBD41B /* SDL_scancode.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558481595D4D800BBD41B /* SDL_shape.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557EE1595D4D800BBD41B /* SDL_shape.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558491595D4D800BBD41B /* SDL_shape.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557EE1595D4D800BBD41B /* SDL_shape.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75584A1595D4D800BBD41B /* SDL_stdinc.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557EF1595D4D800BBD41B /* SDL_stdinc.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75584B1595D4D800BBD41B /* SDL_stdinc.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557EF1595D4D800BBD41B /* SDL_stdinc.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75584C1595D4D800BBD41B /* SDL_surface.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F01595D4D800BBD41B /* SDL_surface.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75584D1595D4D800BBD41B /* SDL_surface.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F01595D4D800BBD41B /* SDL_surface.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75584E1595D4D800BBD41B /* SDL_system.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F11595D4D800BBD41B /* SDL_system.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75584F1595D4D800BBD41B /* SDL_system.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F11595D4D800BBD41B /* SDL_system.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558501595D4D800BBD41B /* SDL_syswm.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F21595D4D800BBD41B /* SDL_syswm.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558511595D4D800BBD41B /* SDL_syswm.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F21595D4D800BBD41B /* SDL_syswm.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558521595D4D800BBD41B /* SDL_thread.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F31595D4D800BBD41B /* SDL_thread.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558531595D4D800BBD41B /* SDL_thread.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F31595D4D800BBD41B /* SDL_thread.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558541595D4D800BBD41B /* SDL_timer.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F41595D4D800BBD41B /* SDL_timer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558551595D4D800BBD41B /* SDL_timer.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F41595D4D800BBD41B /* SDL_timer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558561595D4D800BBD41B /* SDL_touch.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F51595D4D800BBD41B /* SDL_touch.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558571595D4D800BBD41B /* SDL_touch.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F51595D4D800BBD41B /* SDL_touch.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558581595D4D800BBD41B /* SDL_types.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F61595D4D800BBD41B /* SDL_types.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA7558591595D4D800BBD41B /* SDL_types.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F61595D4D800BBD41B /* SDL_types.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75585A1595D4D800BBD41B /* SDL_version.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F71595D4D800BBD41B /* SDL_version.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75585B1595D4D800BBD41B /* SDL_version.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F71595D4D800BBD41B /* SDL_version.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75585C1595D4D800BBD41B /* SDL_video.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F81595D4D800BBD41B /* SDL_video.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75585D1595D4D800BBD41B /* SDL_video.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F81595D4D800BBD41B /* SDL_video.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75585E1595D4D800BBD41B /* SDL.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F91595D4D800BBD41B /* SDL.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA75585F1595D4D800BBD41B /* SDL.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F91595D4D800BBD41B /* SDL.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA8167541F5E727800518735 /* SDL_vulkan.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D4820431F0F10B400EDC31C /* SDL_vulkan.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AA9A7F111FB0206400FED37F /* yuv_rgb.h in Headers */ = {isa = PBXBuildFile; fileRef = AA9A7F0F1FB0206300FED37F /* yuv_rgb.h */; };
		AA9A7F121FB0206400FED37F /* yuv_rgb.c in Sources */ = {isa = PBXBuildFile; fileRef = AA9A7F101FB0206300FED37F /* yuv_rgb.c */; };
		AA9A7F151FB0209D00FED37F /* SDL_yuv.c in Sources */ = {isa = PBXBuildFile; fileRef = AA9A7F131FB0209C00FED37F /* SDL_yuv.c */; };
		AA9A7F161FB0209D00FED37F /* SDL_yuv_c.h in Headers */ = {isa = PBXBuildFile; fileRef = AA9A7F141FB0209C00FED37F /* SDL_yuv_c.h */; };
		AA9E4093163BE51E007A2AD0 /* SDL_x11messagebox.c in Sources */ = {isa = PBXBuildFile; fileRef = AA9E4092163BE51E007A2AD0 /* SDL_x11messagebox.c */; };
		AA9E4094163BE51E007A2AD0 /* SDL_x11messagebox.c in Sources */ = {isa = PBXBuildFile; fileRef = AA9E4092163BE51E007A2AD0 /* SDL_x11messagebox.c */; };
		AA9FF95A1637CBF9000DF050 /* SDL_messagebox.h in Headers */ = {isa = PBXBuildFile; fileRef = AA9FF9591637CBF9000DF050 /* SDL_messagebox.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AABCC38D164063D200AB8930 /* SDL_cocoamessagebox.h in Headers */ = {isa = PBXBuildFile; fileRef = AABCC38B164063D200AB8930 /* SDL_cocoamessagebox.h */; };
		AABCC38E164063D200AB8930 /* SDL_cocoamessagebox.h in Headers */ = {isa = PBXBuildFile; fileRef = AABCC38B164063D200AB8930 /* SDL_cocoamessagebox.h */; };
		AABCC38F164063D200AB8930 /* SDL_cocoamessagebox.m in Sources */ = {isa = PBXBuildFile; fileRef = AABCC38C164063D200AB8930 /* SDL_cocoamessagebox.m */; };
		AABCC390164063D200AB8930 /* SDL_cocoamessagebox.m in Sources */ = {isa = PBXBuildFile; fileRef = AABCC38C164063D200AB8930 /* SDL_cocoamessagebox.m */; };
		AAC070F9195606770073DCDF /* SDL_opengl_glext.h in Headers */ = {isa = PBXBuildFile; fileRef = AAC070F4195606770073DCDF /* SDL_opengl_glext.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AAC070FA195606770073DCDF /* SDL_opengl_glext.h in Headers */ = {isa = PBXBuildFile; fileRef = AAC070F4195606770073DCDF /* SDL_opengl_glext.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AAC070FB195606770073DCDF /* SDL_opengl_glext.h in Headers */ = {isa = PBXBuildFile; fileRef = AAC070F4195606770073DCDF /* SDL_opengl_glext.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AAC070FC195606770073DCDF /* SDL_opengles2_gl2.h in Headers */ = {isa = PBXBuildFile; fileRef = AAC070F5195606770073DCDF /* SDL_opengles2_gl2.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AAC070FD195606770073DCDF /* SDL_opengles2_gl2.h in Headers */ = {isa = PBXBuildFile; fileRef = AAC070F5195606770073DCDF /* SDL_opengles2_gl2.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AAC070FE195606770073DCDF /* SDL_opengles2_gl2.h in Headers */ = {isa = PBXBuildFile; fileRef = AAC070F5195606770073DCDF /* SDL_opengles2_gl2.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AAC070FF195606770073DCDF /* SDL_opengles2_gl2ext.h in Headers */ = {isa = PBXBuildFile; fileRef = AAC070F6195606770073DCDF /* SDL_opengles2_gl2ext.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AAC07100195606770073DCDF /* SDL_opengles2_gl2ext.h in Headers */ = {isa = PBXBuildFile; fileRef = AAC070F6195606770073DCDF /* SDL_opengles2_gl2ext.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AAC07101195606770073DCDF /* SDL_opengles2_gl2ext.h in Headers */ = {isa = PBXBuildFile; fileRef = AAC070F6195606770073DCDF /* SDL_opengles2_gl2ext.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AAC07102195606770073DCDF /* SDL_opengles2_gl2platform.h in Headers */ = {isa = PBXBuildFile; fileRef = AAC070F7195606770073DCDF /* SDL_opengles2_gl2platform.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AAC07103195606770073DCDF /* SDL_opengles2_gl2platform.h in Headers */ = {isa = PBXBuildFile; fileRef = AAC070F7195606770073DCDF /* SDL_opengles2_gl2platform.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AAC07104195606770073DCDF /* SDL_opengles2_gl2platform.h in Headers */ = {isa = PBXBuildFile; fileRef = AAC070F7195606770073DCDF /* SDL_opengles2_gl2platform.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AAC07105195606770073DCDF /* SDL_opengles2_khrplatform.h in Headers */ = {isa = PBXBuildFile; fileRef = AAC070F8195606770073DCDF /* SDL_opengles2_khrplatform.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AAC07106195606770073DCDF /* SDL_opengles2_khrplatform.h in Headers */ = {isa = PBXBuildFile; fileRef = AAC070F8195606770073DCDF /* SDL_opengles2_khrplatform.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AAC07107195606770073DCDF /* SDL_opengles2_khrplatform.h in Headers */ = {isa = PBXBuildFile; fileRef = AAC070F8195606770073DCDF /* SDL_opengles2_khrplatform.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AADA5B8716CCAB3000107CF7 /* SDL_bits.h in Headers */ = {isa = PBXBuildFile; fileRef = AADA5B8616CCAB3000107CF7 /* SDL_bits.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AADA5B8816CCAB3000107CF7 /* SDL_bits.h in Headers */ = {isa = PBXBuildFile; fileRef = AADA5B8616CCAB3000107CF7 /* SDL_bits.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AADC5A431FDA035D00960936 /* SDL_shaders_metal_osx.h in Headers */ = {isa = PBXBuildFile; fileRef = AADC5A411FDA035D00960936 /* SDL_shaders_metal_osx.h */; };
		AADC5A441FDA035D00960936 /* SDL_render_metal.m in Sources */ = {isa = PBXBuildFile; fileRef = AADC5A421FDA035D00960936 /* SDL_render_metal.m */; };
		AADC5A451FDA047900960936 /* SDL_render_metal.m in Sources */ = {isa = PBXBuildFile; fileRef = AADC5A421FDA035D00960936 /* SDL_render_metal.m */; };
		AADC5A461FDA047B00960936 /* SDL_shaders_metal_osx.h in Headers */ = {isa = PBXBuildFile; fileRef = AADC5A411FDA035D00960936 /* SDL_shaders_metal_osx.h */; };
		AADC5A471FDA047E00960936 /* SDL_shaders_metal_osx.h in Headers */ = {isa = PBXBuildFile; fileRef = AADC5A411FDA035D00960936 /* SDL_shaders_metal_osx.h */; };
		AADC5A481FDA048100960936 /* SDL_render_metal.m in Sources */ = {isa = PBXBuildFile; fileRef = AADC5A421FDA035D00960936 /* SDL_render_metal.m */; };
		AADC5A491FDA05C100960936 /* yuv_rgb.h in Headers */ = {isa = PBXBuildFile; fileRef = AA9A7F0F1FB0206300FED37F /* yuv_rgb.h */; };
		AADC5A4A1FDA05C200960936 /* yuv_rgb.h in Headers */ = {isa = PBXBuildFile; fileRef = AA9A7F0F1FB0206300FED37F /* yuv_rgb.h */; };
		AADC5A4B1FDA05CF00960936 /* SDL_yuv_c.h in Headers */ = {isa = PBXBuildFile; fileRef = AA9A7F141FB0209C00FED37F /* SDL_yuv_c.h */; };
		AADC5A4C1FDA05CF00960936 /* SDL_yuv_c.h in Headers */ = {isa = PBXBuildFile; fileRef = AA9A7F141FB0209C00FED37F /* SDL_yuv_c.h */; };
		AADC5A4D1FDA05D300960936 /* SDL_vulkan_internal.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D16644C1EDD6023003DE88E /* SDL_vulkan_internal.h */; };
		AADC5A4E1FDA05D400960936 /* SDL_vulkan_internal.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D16644C1EDD6023003DE88E /* SDL_vulkan_internal.h */; };
		AADC5A4F1FDA05E600960936 /* SDL_cocoavulkan.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D1664511EDD60AD003DE88E /* SDL_cocoavulkan.h */; };
		AADC5A501FDA05E600960936 /* SDL_cocoavulkan.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D1664511EDD60AD003DE88E /* SDL_cocoavulkan.h */; };
		BBFC088D164C6647003E6A99 /* SDL_gamecontroller.c in Sources */ = {isa = PBXBuildFile; fileRef = BBFC088A164C6514003E6A99 /* SDL_gamecontroller.c */; };
		D55A1B81179F262300625D7C /* SDL_cocoamousetap.h in Headers */ = {isa = PBXBuildFile; fileRef = D55A1B7F179F262300625D7C /* SDL_cocoamousetap.h */; };
		D55A1B82179F262300625D7C /* SDL_cocoamousetap.m in Sources */ = {isa = PBXBuildFile; fileRef = D55A1B80179F262300625D7C /* SDL_cocoamousetap.m */; };
		D55A1B83179F263500625D7C /* SDL_cocoamousetap.m in Sources */ = {isa = PBXBuildFile; fileRef = D55A1B80179F262300625D7C /* SDL_cocoamousetap.m */; };
		D55A1B84179F263600625D7C /* SDL_cocoamousetap.m in Sources */ = {isa = PBXBuildFile; fileRef = D55A1B80179F262300625D7C /* SDL_cocoamousetap.m */; };
		D55A1B85179F278E00625D7C /* SDL_cocoamousetap.h in Headers */ = {isa = PBXBuildFile; fileRef = D55A1B7F179F262300625D7C /* SDL_cocoamousetap.h */; };
		D55A1B86179F278F00625D7C /* SDL_cocoamousetap.h in Headers */ = {isa = PBXBuildFile; fileRef = D55A1B7F179F262300625D7C /* SDL_cocoamousetap.h */; };
		DB0F489317C400E6008798C5 /* SDL_messagebox.h in Headers */ = {isa = PBXBuildFile; fileRef = AA9FF9591637CBF9000DF050 /* SDL_messagebox.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB0F489417C400ED008798C5 /* SDL_messagebox.h in Headers */ = {isa = PBXBuildFile; fileRef = AA9FF9591637CBF9000DF050 /* SDL_messagebox.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB0F490817CA5292008798C5 /* SDL_sysfilesystem.m in Sources */ = {isa = PBXBuildFile; fileRef = 567E2F1B17C44BB2005F1892 /* SDL_sysfilesystem.m */; };
		DB0F490A17CA5293008798C5 /* SDL_sysfilesystem.m in Sources */ = {isa = PBXBuildFile; fileRef = 567E2F1B17C44BB2005F1892 /* SDL_sysfilesystem.m */; };
		DB0F490B17CA57ED008798C5 /* SDL_filesystem.h in Headers */ = {isa = PBXBuildFile; fileRef = 567E2F2017C44C35005F1892 /* SDL_filesystem.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB0F490C17CA57ED008798C5 /* SDL_filesystem.h in Headers */ = {isa = PBXBuildFile; fileRef = 567E2F2017C44C35005F1892 /* SDL_filesystem.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313F7417554B71006C0E22 /* SDL_diskaudio.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFD8912E6671700899322 /* SDL_diskaudio.h */; };
		DB313F7517554B71006C0E22 /* SDL_dummyaudio.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFD9512E6671700899322 /* SDL_dummyaudio.h */; };
		DB313F7617554B71006C0E22 /* SDL_coreaudio.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDA112E6671700899322 /* SDL_coreaudio.h */; };
		DB313F7717554B71006C0E22 /* SDL_audio_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDB512E6671700899322 /* SDL_audio_c.h */; };
		DB313F7817554B71006C0E22 /* SDL_audiodev_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDB812E6671700899322 /* SDL_audiodev_c.h */; };
		DB313F7A17554B71006C0E22 /* SDL_sysaudio.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDC212E6671700899322 /* SDL_sysaudio.h */; };
		DB313F7B17554B71006C0E22 /* SDL_wave.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDC412E6671700899322 /* SDL_wave.h */; };
		DB313F7C17554B71006C0E22 /* blank_cursor.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDD612E6671700899322 /* blank_cursor.h */; };
		DB313F7D17554B71006C0E22 /* default_cursor.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDD712E6671700899322 /* default_cursor.h */; };
		DB313F7E17554B71006C0E22 /* scancodes_darwin.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDD812E6671700899322 /* scancodes_darwin.h */; };
		DB313F7F17554B71006C0E22 /* scancodes_linux.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDD912E6671700899322 /* scancodes_linux.h */; };
		DB313F8017554B71006C0E22 /* scancodes_xfree86.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDDB12E6671700899322 /* scancodes_xfree86.h */; };
		DB313F8117554B71006C0E22 /* SDL_clipboardevents_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDDD12E6671700899322 /* SDL_clipboardevents_c.h */; };
		DB313F8217554B71006C0E22 /* SDL_events_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDDF12E6671700899322 /* SDL_events_c.h */; };
		DB313F8317554B71006C0E22 /* SDL_gesture_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDE112E6671700899322 /* SDL_gesture_c.h */; };
		DB313F8417554B71006C0E22 /* SDL_keyboard_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDE312E6671700899322 /* SDL_keyboard_c.h */; };
		DB313F8517554B71006C0E22 /* SDL_mouse_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDE512E6671700899322 /* SDL_mouse_c.h */; };
		DB313F8617554B71006C0E22 /* SDL_sysevents.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDE712E6671700899322 /* SDL_sysevents.h */; };
		DB313F8717554B71006C0E22 /* SDL_touch_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDE912E6671700899322 /* SDL_touch_c.h */; };
		DB313F8817554B71006C0E22 /* SDL_windowevents_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDEB12E6671700899322 /* SDL_windowevents_c.h */; };
		DB313F8917554B71006C0E22 /* SDL_rwopsbundlesupport.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDEE12E6671700899322 /* SDL_rwopsbundlesupport.h */; };
		DB313F8A17554B71006C0E22 /* SDL_haptic_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDFB12E6671700899322 /* SDL_haptic_c.h */; };
		DB313F8B17554B71006C0E22 /* SDL_syshaptic.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFDFC12E6671700899322 /* SDL_syshaptic.h */; };
		DB313F8C17554B71006C0E22 /* SDL_sysjoystick_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE0812E6671700899322 /* SDL_sysjoystick_c.h */; };
		DB313F8D17554B71006C0E22 /* SDL_joystick_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE1712E6671700899322 /* SDL_joystick_c.h */; };
		DB313F8E17554B71006C0E22 /* SDL_sysjoystick.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE1812E6671700899322 /* SDL_sysjoystick.h */; };
		DB313F8F17554B71006C0E22 /* SDL_assert_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE5512E6671700899322 /* SDL_assert_c.h */; };
		DB313F9017554B71006C0E22 /* SDL_error_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE5812E6671700899322 /* SDL_error_c.h */; };
		DB313F9217554B71006C0E22 /* SDL_sysmutex_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE8012E6671800899322 /* SDL_sysmutex_c.h */; };
		DB313F9317554B71006C0E22 /* SDL_systhread_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE8312E6671800899322 /* SDL_systhread_c.h */; };
		DB313F9417554B71006C0E22 /* SDL_systhread.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE8B12E6671800899322 /* SDL_systhread.h */; };
		DB313F9517554B71006C0E22 /* SDL_thread_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFE8D12E6671800899322 /* SDL_thread_c.h */; };
		DB313F9617554B71006C0E22 /* SDL_timer_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFEA012E6671800899322 /* SDL_timer_c.h */; };
		DB313F9717554B71006C0E22 /* SDL_cocoaclipboard.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFEC212E6671800899322 /* SDL_cocoaclipboard.h */; };
		DB313F9817554B71006C0E22 /* SDL_cocoaevents.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFEC412E6671800899322 /* SDL_cocoaevents.h */; };
		DB313F9917554B71006C0E22 /* SDL_cocoakeyboard.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFEC612E6671800899322 /* SDL_cocoakeyboard.h */; };
		DB313F9A17554B71006C0E22 /* SDL_cocoamodes.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFEC812E6671800899322 /* SDL_cocoamodes.h */; };
		DB313F9B17554B71006C0E22 /* SDL_cocoamouse.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFECA12E6671800899322 /* SDL_cocoamouse.h */; };
		DB313F9C17554B71006C0E22 /* SDL_cocoaopengl.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFECC12E6671800899322 /* SDL_cocoaopengl.h */; };
		DB313F9D17554B71006C0E22 /* SDL_cocoashape.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFECE12E6671800899322 /* SDL_cocoashape.h */; };
		DB313F9E17554B71006C0E22 /* SDL_cocoavideo.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFED012E6671800899322 /* SDL_cocoavideo.h */; };
		DB313F9F17554B71006C0E22 /* SDL_cocoawindow.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFED212E6671800899322 /* SDL_cocoawindow.h */; };
		DB313FA017554B71006C0E22 /* SDL_nullevents_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFEE912E6671800899322 /* SDL_nullevents_c.h */; };
		DB313FA117554B71006C0E22 /* SDL_nullvideo.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFEED12E6671800899322 /* SDL_nullvideo.h */; };
		DB313FA217554B71006C0E22 /* SDL_blit.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFF4F12E6671800899322 /* SDL_blit.h */; };
		DB313FA317554B71006C0E22 /* SDL_blit_auto.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFF5412E6671800899322 /* SDL_blit_auto.h */; };
		DB313FA417554B71006C0E22 /* SDL_blit_copy.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFF5612E6671800899322 /* SDL_blit_copy.h */; };
		DB313FA517554B71006C0E22 /* SDL_blit_slow.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFF5912E6671800899322 /* SDL_blit_slow.h */; };
		DB313FA617554B71006C0E22 /* SDL_pixels_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFF6612E6671800899322 /* SDL_pixels_c.h */; };
		DB313FA717554B71006C0E22 /* SDL_RLEaccel_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFF7012E6671800899322 /* SDL_RLEaccel_c.h */; };
		DB313FA817554B71006C0E22 /* SDL_shape_internals.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFF7212E6671800899322 /* SDL_shape_internals.h */; };
		DB313FA917554B71006C0E22 /* SDL_sysvideo.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFF7512E6671800899322 /* SDL_sysvideo.h */; };
		DB313FAA17554B71006C0E22 /* imKStoUCS.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFB912E6671800899322 /* imKStoUCS.h */; };
		DB313FAB17554B71006C0E22 /* SDL_x11clipboard.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFBB12E6671800899322 /* SDL_x11clipboard.h */; };
		DB313FAC17554B71006C0E22 /* SDL_x11dyn.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFBD12E6671800899322 /* SDL_x11dyn.h */; };
		DB313FAD17554B71006C0E22 /* SDL_x11events.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFBF12E6671800899322 /* SDL_x11events.h */; };
		DB313FAE17554B71006C0E22 /* SDL_x11keyboard.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFC312E6671800899322 /* SDL_x11keyboard.h */; };
		DB313FAF17554B71006C0E22 /* SDL_x11modes.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFC512E6671800899322 /* SDL_x11modes.h */; };
		DB313FB017554B71006C0E22 /* SDL_x11mouse.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFC712E6671800899322 /* SDL_x11mouse.h */; };
		DB313FB117554B71006C0E22 /* SDL_x11opengl.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFC912E6671800899322 /* SDL_x11opengl.h */; };
		DB313FB217554B71006C0E22 /* SDL_x11opengles.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFCB12E6671800899322 /* SDL_x11opengles.h */; };
		DB313FB317554B71006C0E22 /* SDL_x11shape.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFCF12E6671800899322 /* SDL_x11shape.h */; };
		DB313FB417554B71006C0E22 /* SDL_x11sym.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFD012E6671800899322 /* SDL_x11sym.h */; };
		DB313FB517554B71006C0E22 /* SDL_x11touch.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFD212E6671800899322 /* SDL_x11touch.h */; };
		DB313FB617554B71006C0E22 /* SDL_x11video.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFD412E6671800899322 /* SDL_x11video.h */; };
		DB313FB717554B71006C0E22 /* SDL_x11window.h in Headers */ = {isa = PBXBuildFile; fileRef = 04BDFFD612E6671800899322 /* SDL_x11window.h */; };
		DB313FB817554B71006C0E22 /* SDL_sysrender.h in Headers */ = {isa = PBXBuildFile; fileRef = 041B2C9F12FA0D680087D585 /* SDL_sysrender.h */; };
		DB313FBA17554B71006C0E22 /* SDL_yuv_sw_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04409B8F12FA97ED00FB9AA8 /* SDL_yuv_sw_c.h */; };
		DB313FBB17554B71006C0E22 /* SDL_nullframebuffer_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 04F7803712FB748500FC43C0 /* SDL_nullframebuffer_c.h */; };
		DB313FBC17554B71006C0E22 /* SDL_blendfillrect.h in Headers */ = {isa = PBXBuildFile; fileRef = 04F7803E12FB74A200FC43C0 /* SDL_blendfillrect.h */; };
		DB313FBD17554B71006C0E22 /* SDL_blendline.h in Headers */ = {isa = PBXBuildFile; fileRef = 04F7804012FB74A200FC43C0 /* SDL_blendline.h */; };
		DB313FBE17554B71006C0E22 /* SDL_blendpoint.h in Headers */ = {isa = PBXBuildFile; fileRef = 04F7804212FB74A200FC43C0 /* SDL_blendpoint.h */; };
		DB313FBF17554B71006C0E22 /* SDL_draw.h in Headers */ = {isa = PBXBuildFile; fileRef = 04F7804312FB74A200FC43C0 /* SDL_draw.h */; };
		DB313FC017554B71006C0E22 /* SDL_drawline.h in Headers */ = {isa = PBXBuildFile; fileRef = 04F7804512FB74A200FC43C0 /* SDL_drawline.h */; };
		DB313FC117554B71006C0E22 /* SDL_drawpoint.h in Headers */ = {isa = PBXBuildFile; fileRef = 04F7804712FB74A200FC43C0 /* SDL_drawpoint.h */; };
		DB313FC217554B71006C0E22 /* SDL_render_sw_c.h in Headers */ = {isa = PBXBuildFile; fileRef = 0442EC1A12FE1BCB004C9285 /* SDL_render_sw_c.h */; };
		DB313FC317554B71006C0E22 /* SDL_x11framebuffer.h in Headers */ = {isa = PBXBuildFile; fileRef = 0442EC5912FE1C60004C9285 /* SDL_x11framebuffer.h */; };
		DB313FC417554B71006C0E22 /* SDL_glfuncs.h in Headers */ = {isa = PBXBuildFile; fileRef = 04043BBA12FEB1BE0076DB1F /* SDL_glfuncs.h */; };
		DB313FC517554B71006C0E22 /* SDL_shaders_gl.h in Headers */ = {isa = PBXBuildFile; fileRef = 0435673D1303160F00BA5428 /* SDL_shaders_gl.h */; };
		DB313FC617554B71006C0E22 /* SDL_rotate.h in Headers */ = {isa = PBXBuildFile; fileRef = AA628AC9159367B7005138DD /* SDL_rotate.h */; };
		DB313FC717554B71006C0E22 /* SDL_x11xinput2.h in Headers */ = {isa = PBXBuildFile; fileRef = AA628AD0159367F2005138DD /* SDL_x11xinput2.h */; };
		DB313FC817554B71006C0E22 /* begin_code.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557C71595D4D800BBD41B /* begin_code.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FC917554B71006C0E22 /* close_code.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557C81595D4D800BBD41B /* close_code.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FCA17554B71006C0E22 /* SDL_assert.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557C91595D4D800BBD41B /* SDL_assert.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FCB17554B71006C0E22 /* SDL_atomic.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557CA1595D4D800BBD41B /* SDL_atomic.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FCC17554B71006C0E22 /* SDL_audio.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557CB1595D4D800BBD41B /* SDL_audio.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FCD17554B71006C0E22 /* SDL_blendmode.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557CC1595D4D800BBD41B /* SDL_blendmode.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FCE17554B71006C0E22 /* SDL_clipboard.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557CD1595D4D800BBD41B /* SDL_clipboard.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FCF17554B71006C0E22 /* SDL_config_macosx.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557CE1595D4D800BBD41B /* SDL_config_macosx.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FD017554B71006C0E22 /* SDL_config.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557CF1595D4D800BBD41B /* SDL_config.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FD117554B71006C0E22 /* SDL_copying.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D01595D4D800BBD41B /* SDL_copying.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FD217554B71006C0E22 /* SDL_cpuinfo.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D11595D4D800BBD41B /* SDL_cpuinfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FD317554B71006C0E22 /* SDL_endian.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D21595D4D800BBD41B /* SDL_endian.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FD417554B71006C0E22 /* SDL_error.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D31595D4D800BBD41B /* SDL_error.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FD517554B71006C0E22 /* SDL_events.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D41595D4D800BBD41B /* SDL_events.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FD617554B71006C0E22 /* SDL_gesture.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D51595D4D800BBD41B /* SDL_gesture.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FD717554B71006C0E22 /* SDL_haptic.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D61595D4D800BBD41B /* SDL_haptic.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FD817554B71006C0E22 /* SDL_hints.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D71595D4D800BBD41B /* SDL_hints.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FD917554B71006C0E22 /* SDL_joystick.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557D91595D4D800BBD41B /* SDL_joystick.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FDA17554B71006C0E22 /* SDL_keyboard.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557DA1595D4D800BBD41B /* SDL_keyboard.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FDB17554B71006C0E22 /* SDL_keycode.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557DB1595D4D800BBD41B /* SDL_keycode.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FDC17554B71006C0E22 /* SDL_loadso.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557DC1595D4D800BBD41B /* SDL_loadso.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FDD17554B71006C0E22 /* SDL_log.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557DD1595D4D800BBD41B /* SDL_log.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FDE17554B71006C0E22 /* SDL_main.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557DE1595D4D800BBD41B /* SDL_main.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FDF17554B71006C0E22 /* SDL_mouse.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557DF1595D4D800BBD41B /* SDL_mouse.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FE017554B71006C0E22 /* SDL_mutex.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E01595D4D800BBD41B /* SDL_mutex.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FE117554B71006C0E22 /* SDL_name.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E11595D4D800BBD41B /* SDL_name.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FE217554B71006C0E22 /* SDL_opengl.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E21595D4D800BBD41B /* SDL_opengl.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FE317554B71006C0E22 /* SDL_opengles.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E31595D4D800BBD41B /* SDL_opengles.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FE417554B71006C0E22 /* SDL_opengles2.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E41595D4D800BBD41B /* SDL_opengles2.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FE517554B71006C0E22 /* SDL_pixels.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E51595D4D800BBD41B /* SDL_pixels.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FE617554B71006C0E22 /* SDL_platform.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E61595D4D800BBD41B /* SDL_platform.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FE717554B71006C0E22 /* SDL_power.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E71595D4D800BBD41B /* SDL_power.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FE817554B71006C0E22 /* SDL_quit.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E81595D4D800BBD41B /* SDL_quit.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FE917554B71006C0E22 /* SDL_rect.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557E91595D4D800BBD41B /* SDL_rect.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FEA17554B71006C0E22 /* SDL_render.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557EA1595D4D800BBD41B /* SDL_render.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FEB17554B71006C0E22 /* SDL_revision.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557EB1595D4D800BBD41B /* SDL_revision.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FEC17554B71006C0E22 /* SDL_rwops.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557EC1595D4D800BBD41B /* SDL_rwops.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FED17554B71006C0E22 /* SDL_scancode.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557ED1595D4D800BBD41B /* SDL_scancode.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FEE17554B71006C0E22 /* SDL_shape.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557EE1595D4D800BBD41B /* SDL_shape.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FEF17554B71006C0E22 /* SDL_stdinc.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557EF1595D4D800BBD41B /* SDL_stdinc.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FF017554B71006C0E22 /* SDL_surface.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F01595D4D800BBD41B /* SDL_surface.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FF117554B71006C0E22 /* SDL_system.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F11595D4D800BBD41B /* SDL_system.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FF217554B71006C0E22 /* SDL_syswm.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F21595D4D800BBD41B /* SDL_syswm.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FF317554B71006C0E22 /* SDL_thread.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F31595D4D800BBD41B /* SDL_thread.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FF417554B71006C0E22 /* SDL_timer.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F41595D4D800BBD41B /* SDL_timer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FF517554B71006C0E22 /* SDL_touch.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F51595D4D800BBD41B /* SDL_touch.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FF617554B71006C0E22 /* SDL_types.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F61595D4D800BBD41B /* SDL_types.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FF717554B71006C0E22 /* SDL_version.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F71595D4D800BBD41B /* SDL_version.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FF817554B71006C0E22 /* SDL_video.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F81595D4D800BBD41B /* SDL_video.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FF917554B71006C0E22 /* SDL.h in Headers */ = {isa = PBXBuildFile; fileRef = AA7557F91595D4D800BBD41B /* SDL.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FFA17554B71006C0E22 /* SDL_cocoamessagebox.h in Headers */ = {isa = PBXBuildFile; fileRef = AABCC38B164063D200AB8930 /* SDL_cocoamessagebox.h */; };
		DB313FFB17554B71006C0E22 /* SDL_gamecontroller.h in Headers */ = {isa = PBXBuildFile; fileRef = A77E6EB3167AB0A90010E40B /* SDL_gamecontroller.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FFC17554B71006C0E22 /* SDL_bits.h in Headers */ = {isa = PBXBuildFile; fileRef = AADA5B8616CCAB3000107CF7 /* SDL_bits.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DB313FFE17554B71006C0E22 /* SDL_atomic.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFD7412E6671700899322 /* SDL_atomic.c */; };
		DB313FFF17554B71006C0E22 /* SDL_spinlock.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFD7512E6671700899322 /* SDL_spinlock.c */; };
		DB31400017554B71006C0E22 /* SDL_diskaudio.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFD8812E6671700899322 /* SDL_diskaudio.c */; };
		DB31400117554B71006C0E22 /* SDL_dummyaudio.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFD9412E6671700899322 /* SDL_dummyaudio.c */; };
		DB31400317554B71006C0E22 /* SDL_audio.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDB412E6671700899322 /* SDL_audio.c */; };
		DB31400417554B71006C0E22 /* SDL_audiocvt.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDB612E6671700899322 /* SDL_audiocvt.c */; };
		DB31400517554B71006C0E22 /* SDL_audiodev.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDB712E6671700899322 /* SDL_audiodev.c */; };
		DB31400617554B71006C0E22 /* SDL_audiotypecvt.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDBA12E6671700899322 /* SDL_audiotypecvt.c */; };
		DB31400717554B71006C0E22 /* SDL_mixer.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDBB12E6671700899322 /* SDL_mixer.c */; };
		DB31400817554B71006C0E22 /* SDL_wave.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDC312E6671700899322 /* SDL_wave.c */; };
		DB31400917554B71006C0E22 /* SDL_cpuinfo.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDD412E6671700899322 /* SDL_cpuinfo.c */; };
		DB31400A17554B71006C0E22 /* SDL_clipboardevents.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDDC12E6671700899322 /* SDL_clipboardevents.c */; };
		DB31400B17554B71006C0E22 /* SDL_events.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDDE12E6671700899322 /* SDL_events.c */; };
		DB31400C17554B71006C0E22 /* SDL_dropevents.c in Sources */ = {isa = PBXBuildFile; fileRef = 566CDE8E148F0AC200C5A9BB /* SDL_dropevents.c */; };
		DB31400D17554B71006C0E22 /* SDL_gesture.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDE012E6671700899322 /* SDL_gesture.c */; };
		DB31400E17554B71006C0E22 /* SDL_keyboard.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDE212E6671700899322 /* SDL_keyboard.c */; };
		DB31400F17554B71006C0E22 /* SDL_mouse.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDE412E6671700899322 /* SDL_mouse.c */; };
		DB31401017554B71006C0E22 /* SDL_quit.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDE612E6671700899322 /* SDL_quit.c */; };
		DB31401117554B71006C0E22 /* SDL_touch.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDE812E6671700899322 /* SDL_touch.c */; };
		DB31401217554B71006C0E22 /* SDL_windowevents.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDEA12E6671700899322 /* SDL_windowevents.c */; };
		DB31401317554B71006C0E22 /* SDL_rwopsbundlesupport.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDEF12E6671700899322 /* SDL_rwopsbundlesupport.m */; };
		DB31401417554B71006C0E22 /* SDL_rwops.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDF012E6671700899322 /* SDL_rwops.c */; };
		DB31401517554B71006C0E22 /* SDL_syshaptic.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDF312E6671700899322 /* SDL_syshaptic.c */; };
		DB31401617554B71006C0E22 /* SDL_haptic.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFDFA12E6671700899322 /* SDL_haptic.c */; };
		DB31401717554B71006C0E22 /* SDL_sysjoystick.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE0712E6671700899322 /* SDL_sysjoystick.c */; };
		DB31401817554B71006C0E22 /* SDL_gamecontroller.c in Sources */ = {isa = PBXBuildFile; fileRef = BBFC088A164C6514003E6A99 /* SDL_gamecontroller.c */; };
		DB31401917554B71006C0E22 /* SDL_joystick.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE1612E6671700899322 /* SDL_joystick.c */; };
		DB31401A17554B71006C0E22 /* SDL_sysloadso.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE3312E6671700899322 /* SDL_sysloadso.c */; };
		DB31401B17554B71006C0E22 /* SDL_syspower.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE4B12E6671700899322 /* SDL_syspower.c */; };
		DB31401C17554B71006C0E22 /* SDL_power.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE4E12E6671700899322 /* SDL_power.c */; };
		DB31401D17554B71006C0E22 /* SDL_assert.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE5612E6671700899322 /* SDL_assert.c */; };
		DB31401E17554B71006C0E22 /* SDL_error.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE5912E6671700899322 /* SDL_error.c */; };
		DB31402017554B71006C0E22 /* SDL.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE5C12E6671700899322 /* SDL.c */; };
		DB31402117554B71006C0E22 /* SDL_getenv.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE5E12E6671700899322 /* SDL_getenv.c */; };
		DB31402217554B71006C0E22 /* SDL_iconv.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE5F12E6671700899322 /* SDL_iconv.c */; };
		DB31402317554B71006C0E22 /* SDL_malloc.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE6012E6671700899322 /* SDL_malloc.c */; };
		DB31402417554B71006C0E22 /* SDL_qsort.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE6112E6671700899322 /* SDL_qsort.c */; };
		DB31402517554B71006C0E22 /* SDL_stdlib.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE6212E6671700899322 /* SDL_stdlib.c */; };
		DB31402617554B71006C0E22 /* SDL_string.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE6312E6671700899322 /* SDL_string.c */; };
		DB31402717554B71006C0E22 /* SDL_syscond.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE7E12E6671800899322 /* SDL_syscond.c */; };
		DB31402817554B71006C0E22 /* SDL_sysmutex.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE7F12E6671800899322 /* SDL_sysmutex.c */; };
		DB31402917554B71006C0E22 /* SDL_syssem.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE8112E6671800899322 /* SDL_syssem.c */; };
		DB31402A17554B71006C0E22 /* SDL_systhread.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE8212E6671800899322 /* SDL_systhread.c */; };
		DB31402B17554B71006C0E22 /* SDL_thread.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE8C12E6671800899322 /* SDL_thread.c */; };
		DB31402C17554B71006C0E22 /* SDL_timer.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFE9F12E6671800899322 /* SDL_timer.c */; };
		DB31402D17554B71006C0E22 /* SDL_systimer.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFEA212E6671800899322 /* SDL_systimer.c */; };
		DB31402E17554B71006C0E22 /* SDL_cocoaclipboard.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFEC312E6671800899322 /* SDL_cocoaclipboard.m */; };
		DB31402F17554B71006C0E22 /* SDL_cocoaevents.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFEC512E6671800899322 /* SDL_cocoaevents.m */; };
		DB31403017554B71006C0E22 /* SDL_cocoakeyboard.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFEC712E6671800899322 /* SDL_cocoakeyboard.m */; };
		DB31403117554B71006C0E22 /* SDL_cocoamodes.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFEC912E6671800899322 /* SDL_cocoamodes.m */; };
		DB31403217554B71006C0E22 /* SDL_cocoamouse.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFECB12E6671800899322 /* SDL_cocoamouse.m */; };
		DB31403317554B71006C0E22 /* SDL_cocoaopengl.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFECD12E6671800899322 /* SDL_cocoaopengl.m */; };
		DB31403417554B71006C0E22 /* SDL_cocoashape.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFECF12E6671800899322 /* SDL_cocoashape.m */; };
		DB31403517554B71006C0E22 /* SDL_cocoavideo.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFED112E6671800899322 /* SDL_cocoavideo.m */; };
		DB31403617554B71006C0E22 /* SDL_cocoawindow.m in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFED312E6671800899322 /* SDL_cocoawindow.m */; };
		DB31403717554B71006C0E22 /* SDL_nullevents.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFEE812E6671800899322 /* SDL_nullevents.c */; };
		DB31403817554B71006C0E22 /* SDL_nullvideo.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFEEC12E6671800899322 /* SDL_nullvideo.c */; };
		DB31403917554B71006C0E22 /* SDL_blit.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF4E12E6671800899322 /* SDL_blit.c */; };
		DB31403A17554B71006C0E22 /* SDL_blit_0.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5012E6671800899322 /* SDL_blit_0.c */; };
		DB31403B17554B71006C0E22 /* SDL_blit_1.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5112E6671800899322 /* SDL_blit_1.c */; };
		DB31403C17554B71006C0E22 /* SDL_blit_A.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5212E6671800899322 /* SDL_blit_A.c */; };
		DB31403D17554B71006C0E22 /* SDL_blit_auto.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5312E6671800899322 /* SDL_blit_auto.c */; };
		DB31403E17554B71006C0E22 /* SDL_blit_copy.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5512E6671800899322 /* SDL_blit_copy.c */; };
		DB31403F17554B71006C0E22 /* SDL_blit_N.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5712E6671800899322 /* SDL_blit_N.c */; };
		DB31404017554B71006C0E22 /* SDL_blit_slow.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5812E6671800899322 /* SDL_blit_slow.c */; };
		DB31404117554B71006C0E22 /* SDL_bmp.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5A12E6671800899322 /* SDL_bmp.c */; };
		DB31404217554B71006C0E22 /* SDL_clipboard.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF5B12E6671800899322 /* SDL_clipboard.c */; };
		DB31404317554B71006C0E22 /* SDL_fillrect.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF6012E6671800899322 /* SDL_fillrect.c */; };
		DB31404417554B71006C0E22 /* SDL_pixels.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF6512E6671800899322 /* SDL_pixels.c */; };
		DB31404517554B71006C0E22 /* SDL_rect.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF6712E6671800899322 /* SDL_rect.c */; };
		DB31404617554B71006C0E22 /* SDL_RLEaccel.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF6F12E6671800899322 /* SDL_RLEaccel.c */; };
		DB31404717554B71006C0E22 /* SDL_shape.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF7112E6671800899322 /* SDL_shape.c */; };
		DB31404817554B71006C0E22 /* SDL_stretch.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF7312E6671800899322 /* SDL_stretch.c */; };
		DB31404917554B71006C0E22 /* SDL_surface.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF7412E6671800899322 /* SDL_surface.c */; };
		DB31404A17554B71006C0E22 /* SDL_video.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFF7612E6671800899322 /* SDL_video.c */; };
		DB31404B17554B71006C0E22 /* imKStoUCS.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFB812E6671800899322 /* imKStoUCS.c */; };
		DB31404C17554B71006C0E22 /* SDL_x11clipboard.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFBA12E6671800899322 /* SDL_x11clipboard.c */; };
		DB31404D17554B71006C0E22 /* SDL_x11dyn.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFBC12E6671800899322 /* SDL_x11dyn.c */; };
		DB31404E17554B71006C0E22 /* SDL_x11events.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFBE12E6671800899322 /* SDL_x11events.c */; };
		DB31404F17554B71006C0E22 /* SDL_x11keyboard.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFC212E6671800899322 /* SDL_x11keyboard.c */; };
		DB31405017554B71006C0E22 /* SDL_x11modes.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFC412E6671800899322 /* SDL_x11modes.c */; };
		DB31405117554B71006C0E22 /* SDL_x11mouse.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFC612E6671800899322 /* SDL_x11mouse.c */; };
		DB31405217554B71006C0E22 /* SDL_x11opengl.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFC812E6671800899322 /* SDL_x11opengl.c */; };
		DB31405317554B71006C0E22 /* SDL_x11opengles.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFCA12E6671800899322 /* SDL_x11opengles.c */; };
		DB31405417554B71006C0E22 /* SDL_x11shape.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFCE12E6671800899322 /* SDL_x11shape.c */; };
		DB31405517554B71006C0E22 /* SDL_x11touch.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFD112E6671800899322 /* SDL_x11touch.c */; };
		DB31405617554B71006C0E22 /* SDL_x11video.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFD312E6671800899322 /* SDL_x11video.c */; };
		DB31405717554B71006C0E22 /* SDL_x11window.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BDFFD512E6671800899322 /* SDL_x11window.c */; };
		DB31405817554B71006C0E22 /* SDL_render.c in Sources */ = {isa = PBXBuildFile; fileRef = 041B2C9E12FA0D680087D585 /* SDL_render.c */; };
		DB31405A17554B71006C0E22 /* SDL_yuv_sw.c in Sources */ = {isa = PBXBuildFile; fileRef = 04409B9012FA97ED00FB9AA8 /* SDL_yuv_sw.c */; };
		DB31405B17554B71006C0E22 /* SDL_nullframebuffer.c in Sources */ = {isa = PBXBuildFile; fileRef = 04F7803812FB748500FC43C0 /* SDL_nullframebuffer.c */; };
		DB31405C17554B71006C0E22 /* SDL_blendfillrect.c in Sources */ = {isa = PBXBuildFile; fileRef = 04F7803D12FB74A200FC43C0 /* SDL_blendfillrect.c */; };
		DB31405D17554B71006C0E22 /* SDL_blendline.c in Sources */ = {isa = PBXBuildFile; fileRef = 04F7803F12FB74A200FC43C0 /* SDL_blendline.c */; };
		DB31405E17554B71006C0E22 /* SDL_blendpoint.c in Sources */ = {isa = PBXBuildFile; fileRef = 04F7804112FB74A200FC43C0 /* SDL_blendpoint.c */; };
		DB31405F17554B71006C0E22 /* SDL_drawline.c in Sources */ = {isa = PBXBuildFile; fileRef = 04F7804412FB74A200FC43C0 /* SDL_drawline.c */; };
		DB31406017554B71006C0E22 /* SDL_drawpoint.c in Sources */ = {isa = PBXBuildFile; fileRef = 04F7804612FB74A200FC43C0 /* SDL_drawpoint.c */; };
		DB31406117554B71006C0E22 /* SDL_render_gl.c in Sources */ = {isa = PBXBuildFile; fileRef = 0442EC1712FE1BBA004C9285 /* SDL_render_gl.c */; };
		DB31406217554B71006C0E22 /* SDL_render_sw.c in Sources */ = {isa = PBXBuildFile; fileRef = 0442EC1B12FE1BCB004C9285 /* SDL_render_sw.c */; };
		DB31406317554B71006C0E22 /* SDL_x11framebuffer.c in Sources */ = {isa = PBXBuildFile; fileRef = 0442EC5812FE1C60004C9285 /* SDL_x11framebuffer.c */; };
		DB31406417554B71006C0E22 /* SDL_hints.c in Sources */ = {isa = PBXBuildFile; fileRef = 0442EC5E12FE1C75004C9285 /* SDL_hints.c */; };
		DB31406517554B71006C0E22 /* SDL_log.c in Sources */ = {isa = PBXBuildFile; fileRef = 04BAC0C71300C2160055DE28 /* SDL_log.c */; };
		DB31406617554B71006C0E22 /* SDL_shaders_gl.c in Sources */ = {isa = PBXBuildFile; fileRef = 0435673C1303160F00BA5428 /* SDL_shaders_gl.c */; };
		DB31406717554B71006C0E22 /* SDL_rotate.c in Sources */ = {isa = PBXBuildFile; fileRef = AA628AC8159367B7005138DD /* SDL_rotate.c */; };
		DB31406817554B71006C0E22 /* SDL_x11xinput2.c in Sources */ = {isa = PBXBuildFile; fileRef = AA628ACF159367F2005138DD /* SDL_x11xinput2.c */; };
		DB31406917554B71006C0E22 /* SDL_x11messagebox.c in Sources */ = {isa = PBXBuildFile; fileRef = AA9E4092163BE51E007A2AD0 /* SDL_x11messagebox.c */; };
		DB31406A17554B71006C0E22 /* SDL_cocoamessagebox.m in Sources */ = {isa = PBXBuildFile; fileRef = AABCC38C164063D200AB8930 /* SDL_cocoamessagebox.m */; };
		DB31406E17554B71006C0E22 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0073179D0858DECD00B2BC32 /* Cocoa.framework */; };
		DB31407017554B71006C0E22 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0073179F0858DECD00B2BC32 /* IOKit.framework */; };
		DB31407217554B71006C0E22 /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 007317C10858E15000B2BC32 /* Carbon.framework */; };
		DB31408B17554D37006C0E22 /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 00CFA89C106B4BA100758660 /* ForceFeedback.framework */; };
		DB31408D17554D3C006C0E22 /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 00CFA89C106B4BA100758660 /* ForceFeedback.framework */; };
		F30D9C84212BC94F0047DF2E /* SDL_sensor_c.h in Headers */ = {isa = PBXBuildFile; fileRef = F30D9C81212BC94E0047DF2E /* SDL_sensor_c.h */; };
		F30D9C85212BC94F0047DF2E /* SDL_sensor_c.h in Headers */ = {isa = PBXBuildFile; fileRef = F30D9C81212BC94E0047DF2E /* SDL_sensor_c.h */; };
		F30D9C86212BC94F0047DF2E /* SDL_sensor_c.h in Headers */ = {isa = PBXBuildFile; fileRef = F30D9C81212BC94E0047DF2E /* SDL_sensor_c.h */; };
		F30D9C87212BC94F0047DF2E /* SDL_syssensor.h in Headers */ = {isa = PBXBuildFile; fileRef = F30D9C82212BC94F0047DF2E /* SDL_syssensor.h */; };
		F30D9C88212BC94F0047DF2E /* SDL_syssensor.h in Headers */ = {isa = PBXBuildFile; fileRef = F30D9C82212BC94F0047DF2E /* SDL_syssensor.h */; };
		F30D9C89212BC94F0047DF2E /* SDL_syssensor.h in Headers */ = {isa = PBXBuildFile; fileRef = F30D9C82212BC94F0047DF2E /* SDL_syssensor.h */; };
		F30D9C8A212BC94F0047DF2E /* SDL_sensor.c in Sources */ = {isa = PBXBuildFile; fileRef = F30D9C83212BC94F0047DF2E /* SDL_sensor.c */; };
		F30D9C8B212BC94F0047DF2E /* SDL_sensor.c in Sources */ = {isa = PBXBuildFile; fileRef = F30D9C83212BC94F0047DF2E /* SDL_sensor.c */; };
		F30D9C8C212BC94F0047DF2E /* SDL_sensor.c in Sources */ = {isa = PBXBuildFile; fileRef = F30D9C83212BC94F0047DF2E /* SDL_sensor.c */; };
		F30D9C90212CABDC0047DF2E /* SDL_dummysensor.h in Headers */ = {isa = PBXBuildFile; fileRef = F30D9C8E212CABDB0047DF2E /* SDL_dummysensor.h */; };
		F30D9C91212CABDC0047DF2E /* SDL_dummysensor.h in Headers */ = {isa = PBXBuildFile; fileRef = F30D9C8E212CABDB0047DF2E /* SDL_dummysensor.h */; };
		F30D9C92212CABDC0047DF2E /* SDL_dummysensor.h in Headers */ = {isa = PBXBuildFile; fileRef = F30D9C8E212CABDB0047DF2E /* SDL_dummysensor.h */; };
		F30D9C93212CABDC0047DF2E /* SDL_dummysensor.c in Sources */ = {isa = PBXBuildFile; fileRef = F30D9C8F212CABDB0047DF2E /* SDL_dummysensor.c */; };
		F30D9C94212CABDC0047DF2E /* SDL_dummysensor.c in Sources */ = {isa = PBXBuildFile; fileRef = F30D9C8F212CABDB0047DF2E /* SDL_dummysensor.c */; };
		F30D9C95212CABDC0047DF2E /* SDL_dummysensor.c in Sources */ = {isa = PBXBuildFile; fileRef = F30D9C8F212CABDB0047DF2E /* SDL_dummysensor.c */; };
		F30D9CCD212EB4810047DF2E /* SDL_displayevents_c.h in Headers */ = {isa = PBXBuildFile; fileRef = F30D9CCB212EB4810047DF2E /* SDL_displayevents_c.h */; };
		F30D9CCE212EB4810047DF2E /* SDL_displayevents_c.h in Headers */ = {isa = PBXBuildFile; fileRef = F30D9CCB212EB4810047DF2E /* SDL_displayevents_c.h */; };
		F30D9CCF212EB4810047DF2E /* SDL_displayevents_c.h in Headers */ = {isa = PBXBuildFile; fileRef = F30D9CCB212EB4810047DF2E /* SDL_displayevents_c.h */; };
		F30D9CD0212EB4810047DF2E /* SDL_displayevents.c in Sources */ = {isa = PBXBuildFile; fileRef = F30D9CCC212EB4810047DF2E /* SDL_displayevents.c */; };
		F30D9CD1212EB4810047DF2E /* SDL_displayevents.c in Sources */ = {isa = PBXBuildFile; fileRef = F30D9CCC212EB4810047DF2E /* SDL_displayevents.c */; };
		F30D9CD2212EB4810047DF2E /* SDL_displayevents.c in Sources */ = {isa = PBXBuildFile; fileRef = F30D9CCC212EB4810047DF2E /* SDL_displayevents.c */; };
		F3950CD8212BC88D00F51292 /* SDL_sensor.h in Headers */ = {isa = PBXBuildFile; fileRef = F3950CD7212BC88D00F51292 /* SDL_sensor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F3950CD9212BC88D00F51292 /* SDL_sensor.h in Headers */ = {isa = PBXBuildFile; fileRef = F3950CD7212BC88D00F51292 /* SDL_sensor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F3950CDA212BC88D00F51292 /* SDL_sensor.h in Headers */ = {isa = PBXBuildFile; fileRef = F3950CD7212BC88D00F51292 /* SDL_sensor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FA73671D19A540EF004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73671C19A540EF004122E4 /* CoreVideo.framework */; };
		FA73671E19A54140004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73671C19A540EF004122E4 /* CoreVideo.framework */; };
		FA73671F19A54144004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73671C19A540EF004122E4 /* CoreVideo.framework */; };
		FABA34C71D8B5DB100915323 /* SDL_coreaudio.m in Sources */ = {isa = PBXBuildFile; fileRef = FABA34C61D8B5DB100915323 /* SDL_coreaudio.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		BECDF6C50761BA81005FE872 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 0867D690FE84028FC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BECDF5FE0761BA81005FE872;
			remoteInfo = "Framework (Upgraded)";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0073179D0858DECD00B2BC32 /* Cocoa.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Cocoa.framework; path = System/Library/Frameworks/Cocoa.framework; sourceTree = SDKROOT; };
		0073179F0858DECD00B2BC32 /* IOKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = IOKit.framework; path = System/Library/Frameworks/IOKit.framework; sourceTree = SDKROOT; };
		007317C10858E15000B2BC32 /* Carbon.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Carbon.framework; path = System/Library/Frameworks/Carbon.framework; sourceTree = SDKROOT; };
		00794D3F09D0C461003FC8A1 /* License.txt */ = {isa = PBXFileReference; lastKnownFileType = text; path = License.txt; sourceTree = "<group>"; };
		00CFA89C106B4BA100758660 /* ForceFeedback.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ForceFeedback.framework; path = System/Library/Frameworks/ForceFeedback.framework; sourceTree = SDKROOT; };
		00D0D08310675DD9004B05EF /* CoreFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreFoundation.framework; path = System/Library/Frameworks/CoreFoundation.framework; sourceTree = SDKROOT; };
		04043BBA12FEB1BE0076DB1F /* SDL_glfuncs.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_glfuncs.h; sourceTree = "<group>"; };
		041B2C9E12FA0D680087D585 /* SDL_render.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_render.c; sourceTree = "<group>"; };
		041B2C9F12FA0D680087D585 /* SDL_sysrender.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_sysrender.h; sourceTree = "<group>"; };
		0435673C1303160F00BA5428 /* SDL_shaders_gl.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_shaders_gl.c; sourceTree = "<group>"; };
		0435673D1303160F00BA5428 /* SDL_shaders_gl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_shaders_gl.h; sourceTree = "<group>"; };
		04409B8F12FA97ED00FB9AA8 /* SDL_yuv_sw_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_yuv_sw_c.h; sourceTree = "<group>"; };
		04409B9012FA97ED00FB9AA8 /* SDL_yuv_sw.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_yuv_sw.c; sourceTree = "<group>"; };
		0442EC1712FE1BBA004C9285 /* SDL_render_gl.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_render_gl.c; sourceTree = "<group>"; };
		0442EC1A12FE1BCB004C9285 /* SDL_render_sw_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_render_sw_c.h; sourceTree = "<group>"; };
		0442EC1B12FE1BCB004C9285 /* SDL_render_sw.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_render_sw.c; sourceTree = "<group>"; };
		0442EC5812FE1C60004C9285 /* SDL_x11framebuffer.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_x11framebuffer.c; sourceTree = "<group>"; };
		0442EC5912FE1C60004C9285 /* SDL_x11framebuffer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_x11framebuffer.h; sourceTree = "<group>"; };
		0442EC5E12FE1C75004C9285 /* SDL_hints.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_hints.c; sourceTree = "<group>"; };
		04BAC0C71300C2160055DE28 /* SDL_log.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_log.c; sourceTree = "<group>"; };
		04BDFD7412E6671700899322 /* SDL_atomic.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_atomic.c; sourceTree = "<group>"; };
		04BDFD7512E6671700899322 /* SDL_spinlock.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_spinlock.c; sourceTree = "<group>"; };
		04BDFD8812E6671700899322 /* SDL_diskaudio.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_diskaudio.c; sourceTree = "<group>"; };
		04BDFD8912E6671700899322 /* SDL_diskaudio.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_diskaudio.h; sourceTree = "<group>"; };
		04BDFD9412E6671700899322 /* SDL_dummyaudio.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_dummyaudio.c; sourceTree = "<group>"; };
		04BDFD9512E6671700899322 /* SDL_dummyaudio.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_dummyaudio.h; sourceTree = "<group>"; };
		04BDFDA112E6671700899322 /* SDL_coreaudio.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_coreaudio.h; sourceTree = "<group>"; };
		04BDFDB412E6671700899322 /* SDL_audio.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_audio.c; sourceTree = "<group>"; };
		04BDFDB512E6671700899322 /* SDL_audio_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_audio_c.h; sourceTree = "<group>"; };
		04BDFDB612E6671700899322 /* SDL_audiocvt.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_audiocvt.c; sourceTree = "<group>"; };
		04BDFDB712E6671700899322 /* SDL_audiodev.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_audiodev.c; sourceTree = "<group>"; };
		04BDFDB812E6671700899322 /* SDL_audiodev_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_audiodev_c.h; sourceTree = "<group>"; };
		04BDFDBA12E6671700899322 /* SDL_audiotypecvt.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_audiotypecvt.c; sourceTree = "<group>"; };
		04BDFDBB12E6671700899322 /* SDL_mixer.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_mixer.c; sourceTree = "<group>"; };
		04BDFDC212E6671700899322 /* SDL_sysaudio.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_sysaudio.h; sourceTree = "<group>"; };
		04BDFDC312E6671700899322 /* SDL_wave.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_wave.c; sourceTree = "<group>"; };
		04BDFDC412E6671700899322 /* SDL_wave.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_wave.h; sourceTree = "<group>"; };
		04BDFDD412E6671700899322 /* SDL_cpuinfo.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_cpuinfo.c; sourceTree = "<group>"; };
		04BDFDD612E6671700899322 /* blank_cursor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = blank_cursor.h; sourceTree = "<group>"; };
		04BDFDD712E6671700899322 /* default_cursor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = default_cursor.h; sourceTree = "<group>"; };
		04BDFDD812E6671700899322 /* scancodes_darwin.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = scancodes_darwin.h; sourceTree = "<group>"; };
		04BDFDD912E6671700899322 /* scancodes_linux.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = scancodes_linux.h; sourceTree = "<group>"; };
		04BDFDDB12E6671700899322 /* scancodes_xfree86.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = scancodes_xfree86.h; sourceTree = "<group>"; };
		04BDFDDC12E6671700899322 /* SDL_clipboardevents.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_clipboardevents.c; sourceTree = "<group>"; };
		04BDFDDD12E6671700899322 /* SDL_clipboardevents_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_clipboardevents_c.h; sourceTree = "<group>"; };
		04BDFDDE12E6671700899322 /* SDL_events.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_events.c; sourceTree = "<group>"; };
		04BDFDDF12E6671700899322 /* SDL_events_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_events_c.h; sourceTree = "<group>"; };
		04BDFDE012E6671700899322 /* SDL_gesture.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_gesture.c; sourceTree = "<group>"; };
		04BDFDE112E6671700899322 /* SDL_gesture_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_gesture_c.h; sourceTree = "<group>"; };
		04BDFDE212E6671700899322 /* SDL_keyboard.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_keyboard.c; sourceTree = "<group>"; };
		04BDFDE312E6671700899322 /* SDL_keyboard_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_keyboard_c.h; sourceTree = "<group>"; };
		04BDFDE412E6671700899322 /* SDL_mouse.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_mouse.c; sourceTree = "<group>"; };
		04BDFDE512E6671700899322 /* SDL_mouse_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_mouse_c.h; sourceTree = "<group>"; };
		04BDFDE612E6671700899322 /* SDL_quit.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_quit.c; sourceTree = "<group>"; };
		04BDFDE712E6671700899322 /* SDL_sysevents.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_sysevents.h; sourceTree = "<group>"; };
		04BDFDE812E6671700899322 /* SDL_touch.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_touch.c; sourceTree = "<group>"; };
		04BDFDE912E6671700899322 /* SDL_touch_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_touch_c.h; sourceTree = "<group>"; };
		04BDFDEA12E6671700899322 /* SDL_windowevents.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_windowevents.c; sourceTree = "<group>"; };
		04BDFDEB12E6671700899322 /* SDL_windowevents_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_windowevents_c.h; sourceTree = "<group>"; };
		04BDFDEE12E6671700899322 /* SDL_rwopsbundlesupport.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_rwopsbundlesupport.h; sourceTree = "<group>"; };
		04BDFDEF12E6671700899322 /* SDL_rwopsbundlesupport.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDL_rwopsbundlesupport.m; sourceTree = "<group>"; };
		04BDFDF012E6671700899322 /* SDL_rwops.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_rwops.c; sourceTree = "<group>"; };
		04BDFDF312E6671700899322 /* SDL_syshaptic.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_syshaptic.c; sourceTree = "<group>"; };
		04BDFDFA12E6671700899322 /* SDL_haptic.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_haptic.c; sourceTree = "<group>"; };
		04BDFDFB12E6671700899322 /* SDL_haptic_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_haptic_c.h; sourceTree = "<group>"; };
		04BDFDFC12E6671700899322 /* SDL_syshaptic.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_syshaptic.h; sourceTree = "<group>"; };
		04BDFE0712E6671700899322 /* SDL_sysjoystick.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_sysjoystick.c; sourceTree = "<group>"; };
		04BDFE0812E6671700899322 /* SDL_sysjoystick_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_sysjoystick_c.h; sourceTree = "<group>"; };
		04BDFE1612E6671700899322 /* SDL_joystick.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_joystick.c; sourceTree = "<group>"; };
		04BDFE1712E6671700899322 /* SDL_joystick_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_joystick_c.h; sourceTree = "<group>"; };
		04BDFE1812E6671700899322 /* SDL_sysjoystick.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_sysjoystick.h; sourceTree = "<group>"; };
		04BDFE3312E6671700899322 /* SDL_sysloadso.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_sysloadso.c; sourceTree = "<group>"; };
		04BDFE4B12E6671700899322 /* SDL_syspower.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_syspower.c; sourceTree = "<group>"; };
		04BDFE4E12E6671700899322 /* SDL_power.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_power.c; sourceTree = "<group>"; };
		04BDFE5512E6671700899322 /* SDL_assert_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_assert_c.h; sourceTree = "<group>"; };
		04BDFE5612E6671700899322 /* SDL_assert.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_assert.c; sourceTree = "<group>"; };
		04BDFE5812E6671700899322 /* SDL_error_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_error_c.h; sourceTree = "<group>"; };
		04BDFE5912E6671700899322 /* SDL_error.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_error.c; sourceTree = "<group>"; };
		04BDFE5C12E6671700899322 /* SDL.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL.c; sourceTree = "<group>"; };
		04BDFE5E12E6671700899322 /* SDL_getenv.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_getenv.c; sourceTree = "<group>"; };
		04BDFE5F12E6671700899322 /* SDL_iconv.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_iconv.c; sourceTree = "<group>"; };
		04BDFE6012E6671700899322 /* SDL_malloc.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_malloc.c; sourceTree = "<group>"; };
		04BDFE6112E6671700899322 /* SDL_qsort.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_qsort.c; sourceTree = "<group>"; };
		04BDFE6212E6671700899322 /* SDL_stdlib.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_stdlib.c; sourceTree = "<group>"; };
		04BDFE6312E6671700899322 /* SDL_string.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_string.c; sourceTree = "<group>"; };
		04BDFE7E12E6671800899322 /* SDL_syscond.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_syscond.c; sourceTree = "<group>"; };
		04BDFE7F12E6671800899322 /* SDL_sysmutex.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_sysmutex.c; sourceTree = "<group>"; };
		04BDFE8012E6671800899322 /* SDL_sysmutex_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_sysmutex_c.h; sourceTree = "<group>"; };
		04BDFE8112E6671800899322 /* SDL_syssem.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_syssem.c; sourceTree = "<group>"; };
		04BDFE8212E6671800899322 /* SDL_systhread.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_systhread.c; sourceTree = "<group>"; };
		04BDFE8312E6671800899322 /* SDL_systhread_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_systhread_c.h; sourceTree = "<group>"; };
		04BDFE8B12E6671800899322 /* SDL_systhread.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_systhread.h; sourceTree = "<group>"; };
		04BDFE8C12E6671800899322 /* SDL_thread.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_thread.c; sourceTree = "<group>"; };
		04BDFE8D12E6671800899322 /* SDL_thread_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_thread_c.h; sourceTree = "<group>"; };
		04BDFE9F12E6671800899322 /* SDL_timer.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_timer.c; sourceTree = "<group>"; };
		04BDFEA012E6671800899322 /* SDL_timer_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_timer_c.h; sourceTree = "<group>"; };
		04BDFEA212E6671800899322 /* SDL_systimer.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_systimer.c; sourceTree = "<group>"; };
		04BDFEC212E6671800899322 /* SDL_cocoaclipboard.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_cocoaclipboard.h; sourceTree = "<group>"; };
		04BDFEC312E6671800899322 /* SDL_cocoaclipboard.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDL_cocoaclipboard.m; sourceTree = "<group>"; };
		04BDFEC412E6671800899322 /* SDL_cocoaevents.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_cocoaevents.h; sourceTree = "<group>"; };
		04BDFEC512E6671800899322 /* SDL_cocoaevents.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDL_cocoaevents.m; sourceTree = "<group>"; };
		04BDFEC612E6671800899322 /* SDL_cocoakeyboard.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_cocoakeyboard.h; sourceTree = "<group>"; };
		04BDFEC712E6671800899322 /* SDL_cocoakeyboard.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDL_cocoakeyboard.m; sourceTree = "<group>"; };
		04BDFEC812E6671800899322 /* SDL_cocoamodes.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_cocoamodes.h; sourceTree = "<group>"; };
		04BDFEC912E6671800899322 /* SDL_cocoamodes.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDL_cocoamodes.m; sourceTree = "<group>"; };
		04BDFECA12E6671800899322 /* SDL_cocoamouse.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_cocoamouse.h; sourceTree = "<group>"; };
		04BDFECB12E6671800899322 /* SDL_cocoamouse.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDL_cocoamouse.m; sourceTree = "<group>"; };
		04BDFECC12E6671800899322 /* SDL_cocoaopengl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_cocoaopengl.h; sourceTree = "<group>"; };
		04BDFECD12E6671800899322 /* SDL_cocoaopengl.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDL_cocoaopengl.m; sourceTree = "<group>"; };
		04BDFECE12E6671800899322 /* SDL_cocoashape.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_cocoashape.h; sourceTree = "<group>"; };
		04BDFECF12E6671800899322 /* SDL_cocoashape.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDL_cocoashape.m; sourceTree = "<group>"; };
		04BDFED012E6671800899322 /* SDL_cocoavideo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_cocoavideo.h; sourceTree = "<group>"; };
		04BDFED112E6671800899322 /* SDL_cocoavideo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDL_cocoavideo.m; sourceTree = "<group>"; };
		04BDFED212E6671800899322 /* SDL_cocoawindow.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_cocoawindow.h; sourceTree = "<group>"; };
		04BDFED312E6671800899322 /* SDL_cocoawindow.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDL_cocoawindow.m; sourceTree = "<group>"; };
		04BDFEE812E6671800899322 /* SDL_nullevents.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_nullevents.c; sourceTree = "<group>"; };
		04BDFEE912E6671800899322 /* SDL_nullevents_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_nullevents_c.h; sourceTree = "<group>"; };
		04BDFEEC12E6671800899322 /* SDL_nullvideo.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_nullvideo.c; sourceTree = "<group>"; };
		04BDFEED12E6671800899322 /* SDL_nullvideo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_nullvideo.h; sourceTree = "<group>"; };
		04BDFF4E12E6671800899322 /* SDL_blit.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_blit.c; sourceTree = "<group>"; };
		04BDFF4F12E6671800899322 /* SDL_blit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_blit.h; sourceTree = "<group>"; };
		04BDFF5012E6671800899322 /* SDL_blit_0.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_blit_0.c; sourceTree = "<group>"; };
		04BDFF5112E6671800899322 /* SDL_blit_1.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_blit_1.c; sourceTree = "<group>"; };
		04BDFF5212E6671800899322 /* SDL_blit_A.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_blit_A.c; sourceTree = "<group>"; };
		04BDFF5312E6671800899322 /* SDL_blit_auto.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_blit_auto.c; sourceTree = "<group>"; };
		04BDFF5412E6671800899322 /* SDL_blit_auto.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_blit_auto.h; sourceTree = "<group>"; };
		04BDFF5512E6671800899322 /* SDL_blit_copy.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_blit_copy.c; sourceTree = "<group>"; };
		04BDFF5612E6671800899322 /* SDL_blit_copy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_blit_copy.h; sourceTree = "<group>"; };
		04BDFF5712E6671800899322 /* SDL_blit_N.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_blit_N.c; sourceTree = "<group>"; };
		04BDFF5812E6671800899322 /* SDL_blit_slow.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_blit_slow.c; sourceTree = "<group>"; };
		04BDFF5912E6671800899322 /* SDL_blit_slow.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_blit_slow.h; sourceTree = "<group>"; };
		04BDFF5A12E6671800899322 /* SDL_bmp.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_bmp.c; sourceTree = "<group>"; };
		04BDFF5B12E6671800899322 /* SDL_clipboard.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_clipboard.c; sourceTree = "<group>"; };
		04BDFF6012E6671800899322 /* SDL_fillrect.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_fillrect.c; sourceTree = "<group>"; };
		04BDFF6512E6671800899322 /* SDL_pixels.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_pixels.c; sourceTree = "<group>"; };
		04BDFF6612E6671800899322 /* SDL_pixels_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_pixels_c.h; sourceTree = "<group>"; };
		04BDFF6712E6671800899322 /* SDL_rect.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_rect.c; sourceTree = "<group>"; };
		04BDFF6F12E6671800899322 /* SDL_RLEaccel.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_RLEaccel.c; sourceTree = "<group>"; };
		04BDFF7012E6671800899322 /* SDL_RLEaccel_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_RLEaccel_c.h; sourceTree = "<group>"; };
		04BDFF7112E6671800899322 /* SDL_shape.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_shape.c; sourceTree = "<group>"; };
		04BDFF7212E6671800899322 /* SDL_shape_internals.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_shape_internals.h; sourceTree = "<group>"; };
		04BDFF7312E6671800899322 /* SDL_stretch.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_stretch.c; sourceTree = "<group>"; };
		04BDFF7412E6671800899322 /* SDL_surface.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_surface.c; sourceTree = "<group>"; };
		04BDFF7512E6671800899322 /* SDL_sysvideo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_sysvideo.h; sourceTree = "<group>"; };
		04BDFF7612E6671800899322 /* SDL_video.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_video.c; sourceTree = "<group>"; };
		04BDFFB812E6671800899322 /* imKStoUCS.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = imKStoUCS.c; sourceTree = "<group>"; };
		04BDFFB912E6671800899322 /* imKStoUCS.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = imKStoUCS.h; sourceTree = "<group>"; };
		04BDFFBA12E6671800899322 /* SDL_x11clipboard.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_x11clipboard.c; sourceTree = "<group>"; };
		04BDFFBB12E6671800899322 /* SDL_x11clipboard.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_x11clipboard.h; sourceTree = "<group>"; };
		04BDFFBC12E6671800899322 /* SDL_x11dyn.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_x11dyn.c; sourceTree = "<group>"; };
		04BDFFBD12E6671800899322 /* SDL_x11dyn.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_x11dyn.h; sourceTree = "<group>"; };
		04BDFFBE12E6671800899322 /* SDL_x11events.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_x11events.c; sourceTree = "<group>"; };
		04BDFFBF12E6671800899322 /* SDL_x11events.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_x11events.h; sourceTree = "<group>"; };
		04BDFFC212E6671800899322 /* SDL_x11keyboard.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_x11keyboard.c; sourceTree = "<group>"; };
		04BDFFC312E6671800899322 /* SDL_x11keyboard.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_x11keyboard.h; sourceTree = "<group>"; };
		04BDFFC412E6671800899322 /* SDL_x11modes.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_x11modes.c; sourceTree = "<group>"; };
		04BDFFC512E6671800899322 /* SDL_x11modes.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_x11modes.h; sourceTree = "<group>"; };
		04BDFFC612E6671800899322 /* SDL_x11mouse.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_x11mouse.c; sourceTree = "<group>"; };
		04BDFFC712E6671800899322 /* SDL_x11mouse.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_x11mouse.h; sourceTree = "<group>"; };
		04BDFFC812E6671800899322 /* SDL_x11opengl.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_x11opengl.c; sourceTree = "<group>"; };
		04BDFFC912E6671800899322 /* SDL_x11opengl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_x11opengl.h; sourceTree = "<group>"; };
		04BDFFCA12E6671800899322 /* SDL_x11opengles.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_x11opengles.c; sourceTree = "<group>"; };
		04BDFFCB12E6671800899322 /* SDL_x11opengles.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_x11opengles.h; sourceTree = "<group>"; };
		04BDFFCE12E6671800899322 /* SDL_x11shape.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_x11shape.c; sourceTree = "<group>"; };
		04BDFFCF12E6671800899322 /* SDL_x11shape.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_x11shape.h; sourceTree = "<group>"; };
		04BDFFD012E6671800899322 /* SDL_x11sym.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_x11sym.h; sourceTree = "<group>"; };
		04BDFFD112E6671800899322 /* SDL_x11touch.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_x11touch.c; sourceTree = "<group>"; };
		04BDFFD212E6671800899322 /* SDL_x11touch.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_x11touch.h; sourceTree = "<group>"; };
		04BDFFD312E6671800899322 /* SDL_x11video.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_x11video.c; sourceTree = "<group>"; };
		04BDFFD412E6671800899322 /* SDL_x11video.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_x11video.h; sourceTree = "<group>"; };
		04BDFFD512E6671800899322 /* SDL_x11window.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_x11window.c; sourceTree = "<group>"; };
		04BDFFD612E6671800899322 /* SDL_x11window.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_x11window.h; sourceTree = "<group>"; };
		04F7803712FB748500FC43C0 /* SDL_nullframebuffer_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_nullframebuffer_c.h; sourceTree = "<group>"; };
		04F7803812FB748500FC43C0 /* SDL_nullframebuffer.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_nullframebuffer.c; sourceTree = "<group>"; };
		04F7803D12FB74A200FC43C0 /* SDL_blendfillrect.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_blendfillrect.c; sourceTree = "<group>"; };
		04F7803E12FB74A200FC43C0 /* SDL_blendfillrect.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_blendfillrect.h; sourceTree = "<group>"; };
		04F7803F12FB74A200FC43C0 /* SDL_blendline.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_blendline.c; sourceTree = "<group>"; };
		04F7804012FB74A200FC43C0 /* SDL_blendline.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_blendline.h; sourceTree = "<group>"; };
		04F7804112FB74A200FC43C0 /* SDL_blendpoint.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_blendpoint.c; sourceTree = "<group>"; };
		04F7804212FB74A200FC43C0 /* SDL_blendpoint.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_blendpoint.h; sourceTree = "<group>"; };
		04F7804312FB74A200FC43C0 /* SDL_draw.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_draw.h; sourceTree = "<group>"; };
		04F7804412FB74A200FC43C0 /* SDL_drawline.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_drawline.c; sourceTree = "<group>"; };
		04F7804512FB74A200FC43C0 /* SDL_drawline.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_drawline.h; sourceTree = "<group>"; };
		04F7804612FB74A200FC43C0 /* SDL_drawpoint.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_drawpoint.c; sourceTree = "<group>"; };
		04F7804712FB74A200FC43C0 /* SDL_drawpoint.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_drawpoint.h; sourceTree = "<group>"; };
		4D16644C1EDD6023003DE88E /* SDL_vulkan_internal.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_vulkan_internal.h; sourceTree = "<group>"; };
		4D16644D1EDD6023003DE88E /* SDL_vulkan_utils.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_vulkan_utils.c; sourceTree = "<group>"; };
		4D1664501EDD60AD003DE88E /* SDL_cocoametalview.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDL_cocoametalview.m; sourceTree = "<group>"; };
		4D1664511EDD60AD003DE88E /* SDL_cocoavulkan.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_cocoavulkan.h; sourceTree = "<group>"; };
		4D1664521EDD60AD003DE88E /* SDL_cocoavulkan.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDL_cocoavulkan.m; sourceTree = "<group>"; };
		4D4820431F0F10B400EDC31C /* SDL_vulkan.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDL_vulkan.h; sourceTree = "<group>"; };
		4D7517281EE2562B00820EEA /* SDL_cocoametalview.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_cocoametalview.h; sourceTree = "<group>"; };
		56115BB91DF72C6D00F47E1E /* SDL_dataqueue.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_dataqueue.c; sourceTree = "<group>"; };
		56115BBA1DF72C6D00F47E1E /* SDL_dataqueue.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_dataqueue.h; sourceTree = "<group>"; };
		564624351FF821B80074AC87 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		564624371FF821CB0074AC87 /* Metal.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Metal.framework; path = System/Library/Frameworks/Metal.framework; sourceTree = SDKROOT; };
		566CDE8D148F0AC200C5A9BB /* SDL_dropevents_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_dropevents_c.h; sourceTree = "<group>"; };
		566CDE8E148F0AC200C5A9BB /* SDL_dropevents.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_dropevents.c; sourceTree = "<group>"; };
		567E2F1B17C44BB2005F1892 /* SDL_sysfilesystem.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = SDL_sysfilesystem.m; path = cocoa/SDL_sysfilesystem.m; sourceTree = "<group>"; };
		567E2F2017C44C35005F1892 /* SDL_filesystem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_filesystem.h; sourceTree = "<group>"; };
		56A670081856545C0007D20F /* SDL_internal.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_internal.h; sourceTree = "<group>"; };
		56A6701D185654B40007D20F /* SDL_dynapi_procs.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_dynapi_procs.h; sourceTree = "<group>"; };
		56A6701E185654B40007D20F /* SDL_dynapi.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_dynapi.c; sourceTree = "<group>"; };
		56A6701F185654B40007D20F /* SDL_dynapi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_dynapi.h; sourceTree = "<group>"; };
		56A67020185654B40007D20F /* SDL_dynapi_overrides.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_dynapi_overrides.h; sourceTree = "<group>"; };
		5C2EF69B1FC987C6003F5197 /* SDL_gles2funcs.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_gles2funcs.h; sourceTree = "<group>"; };
		5C2EF69C1FC987C6003F5197 /* SDL_render_gles2.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_render_gles2.c; sourceTree = "<group>"; };
		5C2EF69D1FC987C6003F5197 /* SDL_shaders_gles2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_shaders_gles2.h; sourceTree = "<group>"; };
		5C2EF69E1FC987C6003F5197 /* SDL_shaders_gles2.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_shaders_gles2.c; sourceTree = "<group>"; };
		5C2EF6EC1FC9D0EC003F5197 /* SDL_cocoaopengles.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDL_cocoaopengles.m; sourceTree = "<group>"; };
		5C2EF6ED1FC9D0ED003F5197 /* SDL_cocoaopengles.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_cocoaopengles.h; sourceTree = "<group>"; };
		5C2EF6F41FC9EE34003F5197 /* SDL_rect_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_rect_c.h; sourceTree = "<group>"; };
		5C2EF6F51FC9EE35003F5197 /* SDL_egl.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_egl.c; sourceTree = "<group>"; };
		5C2EF6F61FC9EE35003F5197 /* SDL_egl_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_egl_c.h; sourceTree = "<group>"; };
		5C2EF7001FC9EF0F003F5197 /* SDL_egl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_egl.h; sourceTree = "<group>"; };
		A704170820F09A9800A82227 /* hid.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = hid.c; sourceTree = "<group>"; };
		A704170D20F09AC800A82227 /* SDL_hidapijoystick.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_hidapijoystick.c; sourceTree = "<group>"; };
		A704170E20F09AC800A82227 /* SDL_hidapijoystick_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_hidapijoystick_c.h; sourceTree = "<group>"; };
		A704170F20F09AC800A82227 /* SDL_hidapi_switch.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_hidapi_switch.c; sourceTree = "<group>"; };
		A704171020F09AC900A82227 /* controller_type.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = controller_type.h; sourceTree = "<group>"; };
		A704171120F09AC900A82227 /* SDL_hidapi_ps4.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_hidapi_ps4.c; sourceTree = "<group>"; };
		A704171220F09AC900A82227 /* SDL_hidapi_xboxone.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_hidapi_xboxone.c; sourceTree = "<group>"; };
		A704171320F09AC900A82227 /* SDL_hidapi_xbox360.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_hidapi_xbox360.c; sourceTree = "<group>"; };
		A7381E931D8B69C300B177DD /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
		A7381E951D8B69D600B177DD /* CoreAudio.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreAudio.framework; path = System/Library/Frameworks/CoreAudio.framework; sourceTree = SDKROOT; };
		A77E6EB3167AB0A90010E40B /* SDL_gamecontroller.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_gamecontroller.h; sourceTree = "<group>"; };
		AA0F8490178D5ECC00823F9D /* SDL_systls.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_systls.c; sourceTree = "<group>"; };
		AA628AC8159367B7005138DD /* SDL_rotate.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_rotate.c; sourceTree = "<group>"; };
		AA628AC9159367B7005138DD /* SDL_rotate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_rotate.h; sourceTree = "<group>"; };
		AA628ACF159367F2005138DD /* SDL_x11xinput2.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_x11xinput2.c; sourceTree = "<group>"; };
		AA628AD0159367F2005138DD /* SDL_x11xinput2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_x11xinput2.h; sourceTree = "<group>"; };
		AA7557C71595D4D800BBD41B /* begin_code.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = begin_code.h; sourceTree = "<group>"; };
		AA7557C81595D4D800BBD41B /* close_code.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = close_code.h; sourceTree = "<group>"; };
		AA7557C91595D4D800BBD41B /* SDL_assert.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_assert.h; sourceTree = "<group>"; };
		AA7557CA1595D4D800BBD41B /* SDL_atomic.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_atomic.h; sourceTree = "<group>"; };
		AA7557CB1595D4D800BBD41B /* SDL_audio.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_audio.h; sourceTree = "<group>"; };
		AA7557CC1595D4D800BBD41B /* SDL_blendmode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_blendmode.h; sourceTree = "<group>"; };
		AA7557CD1595D4D800BBD41B /* SDL_clipboard.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_clipboard.h; sourceTree = "<group>"; };
		AA7557CE1595D4D800BBD41B /* SDL_config_macosx.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_config_macosx.h; sourceTree = "<group>"; };
		AA7557CF1595D4D800BBD41B /* SDL_config.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_config.h; sourceTree = "<group>"; };
		AA7557D01595D4D800BBD41B /* SDL_copying.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_copying.h; sourceTree = "<group>"; };
		AA7557D11595D4D800BBD41B /* SDL_cpuinfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_cpuinfo.h; sourceTree = "<group>"; };
		AA7557D21595D4D800BBD41B /* SDL_endian.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_endian.h; sourceTree = "<group>"; };
		AA7557D31595D4D800BBD41B /* SDL_error.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_error.h; sourceTree = "<group>"; };
		AA7557D41595D4D800BBD41B /* SDL_events.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_events.h; sourceTree = "<group>"; };
		AA7557D51595D4D800BBD41B /* SDL_gesture.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_gesture.h; sourceTree = "<group>"; };
		AA7557D61595D4D800BBD41B /* SDL_haptic.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_haptic.h; sourceTree = "<group>"; };
		AA7557D71595D4D800BBD41B /* SDL_hints.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_hints.h; sourceTree = "<group>"; };
		AA7557D91595D4D800BBD41B /* SDL_joystick.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_joystick.h; sourceTree = "<group>"; };
		AA7557DA1595D4D800BBD41B /* SDL_keyboard.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_keyboard.h; sourceTree = "<group>"; };
		AA7557DB1595D4D800BBD41B /* SDL_keycode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_keycode.h; sourceTree = "<group>"; };
		AA7557DC1595D4D800BBD41B /* SDL_loadso.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_loadso.h; sourceTree = "<group>"; };
		AA7557DD1595D4D800BBD41B /* SDL_log.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_log.h; sourceTree = "<group>"; };
		AA7557DE1595D4D800BBD41B /* SDL_main.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_main.h; sourceTree = "<group>"; };
		AA7557DF1595D4D800BBD41B /* SDL_mouse.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_mouse.h; sourceTree = "<group>"; };
		AA7557E01595D4D800BBD41B /* SDL_mutex.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_mutex.h; sourceTree = "<group>"; };
		AA7557E11595D4D800BBD41B /* SDL_name.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_name.h; sourceTree = "<group>"; };
		AA7557E21595D4D800BBD41B /* SDL_opengl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_opengl.h; sourceTree = "<group>"; };
		AA7557E31595D4D800BBD41B /* SDL_opengles.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_opengles.h; sourceTree = "<group>"; };
		AA7557E41595D4D800BBD41B /* SDL_opengles2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_opengles2.h; sourceTree = "<group>"; };
		AA7557E51595D4D800BBD41B /* SDL_pixels.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_pixels.h; sourceTree = "<group>"; };
		AA7557E61595D4D800BBD41B /* SDL_platform.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_platform.h; sourceTree = "<group>"; };
		AA7557E71595D4D800BBD41B /* SDL_power.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_power.h; sourceTree = "<group>"; };
		AA7557E81595D4D800BBD41B /* SDL_quit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_quit.h; sourceTree = "<group>"; };
		AA7557E91595D4D800BBD41B /* SDL_rect.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_rect.h; sourceTree = "<group>"; };
		AA7557EA1595D4D800BBD41B /* SDL_render.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_render.h; sourceTree = "<group>"; };
		AA7557EB1595D4D800BBD41B /* SDL_revision.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_revision.h; sourceTree = "<group>"; };
		AA7557EC1595D4D800BBD41B /* SDL_rwops.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_rwops.h; sourceTree = "<group>"; };
		AA7557ED1595D4D800BBD41B /* SDL_scancode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_scancode.h; sourceTree = "<group>"; };
		AA7557EE1595D4D800BBD41B /* SDL_shape.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_shape.h; sourceTree = "<group>"; };
		AA7557EF1595D4D800BBD41B /* SDL_stdinc.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_stdinc.h; sourceTree = "<group>"; };
		AA7557F01595D4D800BBD41B /* SDL_surface.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_surface.h; sourceTree = "<group>"; };
		AA7557F11595D4D800BBD41B /* SDL_system.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_system.h; sourceTree = "<group>"; };
		AA7557F21595D4D800BBD41B /* SDL_syswm.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_syswm.h; sourceTree = "<group>"; };
		AA7557F31595D4D800BBD41B /* SDL_thread.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_thread.h; sourceTree = "<group>"; };
		AA7557F41595D4D800BBD41B /* SDL_timer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_timer.h; sourceTree = "<group>"; };
		AA7557F51595D4D800BBD41B /* SDL_touch.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_touch.h; sourceTree = "<group>"; };
		AA7557F61595D4D800BBD41B /* SDL_types.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_types.h; sourceTree = "<group>"; };
		AA7557F71595D4D800BBD41B /* SDL_version.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_version.h; sourceTree = "<group>"; };
		AA7557F81595D4D800BBD41B /* SDL_video.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_video.h; sourceTree = "<group>"; };
		AA7557F91595D4D800BBD41B /* SDL.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL.h; sourceTree = "<group>"; };
		AA9A7F0F1FB0206300FED37F /* yuv_rgb.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = yuv_rgb.h; sourceTree = "<group>"; };
		AA9A7F101FB0206300FED37F /* yuv_rgb.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = yuv_rgb.c; sourceTree = "<group>"; };
		AA9A7F131FB0209C00FED37F /* SDL_yuv.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_yuv.c; sourceTree = "<group>"; };
		AA9A7F141FB0209C00FED37F /* SDL_yuv_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_yuv_c.h; sourceTree = "<group>"; };
		AA9E4092163BE51E007A2AD0 /* SDL_x11messagebox.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_x11messagebox.c; sourceTree = "<group>"; };
		AA9FF9591637CBF9000DF050 /* SDL_messagebox.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_messagebox.h; sourceTree = "<group>"; };
		AABCC38B164063D200AB8930 /* SDL_cocoamessagebox.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_cocoamessagebox.h; sourceTree = "<group>"; };
		AABCC38C164063D200AB8930 /* SDL_cocoamessagebox.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDL_cocoamessagebox.m; sourceTree = "<group>"; };
		AAC070F4195606770073DCDF /* SDL_opengl_glext.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_opengl_glext.h; sourceTree = "<group>"; };
		AAC070F5195606770073DCDF /* SDL_opengles2_gl2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_opengles2_gl2.h; sourceTree = "<group>"; };
		AAC070F6195606770073DCDF /* SDL_opengles2_gl2ext.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_opengles2_gl2ext.h; sourceTree = "<group>"; };
		AAC070F7195606770073DCDF /* SDL_opengles2_gl2platform.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_opengles2_gl2platform.h; sourceTree = "<group>"; };
		AAC070F8195606770073DCDF /* SDL_opengles2_khrplatform.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_opengles2_khrplatform.h; sourceTree = "<group>"; };
		AADA5B8616CCAB3000107CF7 /* SDL_bits.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_bits.h; sourceTree = "<group>"; };
		AADC5A411FDA035D00960936 /* SDL_shaders_metal_osx.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_shaders_metal_osx.h; sourceTree = "<group>"; };
		AADC5A421FDA035D00960936 /* SDL_render_metal.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDL_render_metal.m; sourceTree = "<group>"; };
		BBFC088A164C6514003E6A99 /* SDL_gamecontroller.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_gamecontroller.c; sourceTree = "<group>"; };
		BECDF66B0761BA81005FE872 /* Info-Framework.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "Info-Framework.plist"; sourceTree = "<group>"; };
		BECDF66C0761BA81005FE872 /* SDL2.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = SDL2.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		BECDF6B30761BA81005FE872 /* libSDL2.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libSDL2.a; sourceTree = BUILT_PRODUCTS_DIR; };
		BECDF6BE0761BA81005FE872 /* Standard DMG */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = "Standard DMG"; sourceTree = BUILT_PRODUCTS_DIR; };
		D55A1B7F179F262300625D7C /* SDL_cocoamousetap.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_cocoamousetap.h; sourceTree = "<group>"; };
		D55A1B80179F262300625D7C /* SDL_cocoamousetap.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDL_cocoamousetap.m; sourceTree = "<group>"; };
		DB31407717554B71006C0E22 /* libSDL2.dylib */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.dylib"; includeInIndex = 0; path = libSDL2.dylib; sourceTree = BUILT_PRODUCTS_DIR; };
		DB89958518A1A5C50092407C /* SDL_syshaptic_c.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDL_syshaptic_c.h; sourceTree = "<group>"; };
		F30D9C81212BC94E0047DF2E /* SDL_sensor_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_sensor_c.h; sourceTree = "<group>"; };
		F30D9C82212BC94F0047DF2E /* SDL_syssensor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_syssensor.h; sourceTree = "<group>"; };
		F30D9C83212BC94F0047DF2E /* SDL_sensor.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_sensor.c; sourceTree = "<group>"; };
		F30D9C8E212CABDB0047DF2E /* SDL_dummysensor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_dummysensor.h; sourceTree = "<group>"; };
		F30D9C8F212CABDB0047DF2E /* SDL_dummysensor.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_dummysensor.c; sourceTree = "<group>"; };
		F30D9CCB212EB4810047DF2E /* SDL_displayevents_c.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_displayevents_c.h; sourceTree = "<group>"; };
		F30D9CCC212EB4810047DF2E /* SDL_displayevents.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_displayevents.c; sourceTree = "<group>"; };
		F3950CD7212BC88D00F51292 /* SDL_sensor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDL_sensor.h; sourceTree = "<group>"; };
		F59C710300D5CB5801000001 /* ReadMe.txt */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = text; path = ReadMe.txt; sourceTree = "<group>"; };
		F59C710600D5CB5801000001 /* SDL.info */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = text; path = SDL.info; sourceTree = "<group>"; };
		F5A2EF3900C6A39A01000001 /* BUGS.txt */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = text; name = BUGS.txt; path = ../../BUGS.txt; sourceTree = SOURCE_ROOT; };
		FA73671C19A540EF004122E4 /* CoreVideo.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreVideo.framework; path = System/Library/Frameworks/CoreVideo.framework; sourceTree = SDKROOT; };
		FABA34C61D8B5DB100915323 /* SDL_coreaudio.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDL_coreaudio.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		BECDF6680761BA81005FE872 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				564624381FF821DA0074AC87 /* Metal.framework in Frameworks */,
				564624361FF821C20074AC87 /* QuartzCore.framework in Frameworks */,
				A7381E971D8B6A0300B177DD /* AudioToolbox.framework in Frameworks */,
				00D0D0D810675E46004B05EF /* Carbon.framework in Frameworks */,
				007317A40858DECD00B2BC32 /* Cocoa.framework in Frameworks */,
				A7381E961D8B69D600B177DD /* CoreAudio.framework in Frameworks */,
				00D0D08410675DD9004B05EF /* CoreFoundation.framework in Frameworks */,
				FA73671D19A540EF004122E4 /* CoreVideo.framework in Frameworks */,
				00CFA89D106B4BA100758660 /* ForceFeedback.framework in Frameworks */,
				007317A60858DECD00B2BC32 /* IOKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BECDF6B10761BA81005FE872 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				5646243A1FF821FF0074AC87 /* Metal.framework in Frameworks */,
				564624391FF821EF0074AC87 /* QuartzCore.framework in Frameworks */,
				56C5237E1D8F4985001F2F30 /* CoreAudio.framework in Frameworks */,
				FA73671E19A54140004122E4 /* CoreVideo.framework in Frameworks */,
				007317AB0858DECD00B2BC32 /* Cocoa.framework in Frameworks */,
				007317AD0858DECD00B2BC32 /* IOKit.framework in Frameworks */,
				56C523801D8F498B001F2F30 /* CoreFoundation.framework in Frameworks */,
				007317C30858E15000B2BC32 /* Carbon.framework in Frameworks */,
				DB31408B17554D37006C0E22 /* ForceFeedback.framework in Frameworks */,
				562C4AE91D8F496200AF9EBE /* AudioToolbox.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB31406B17554B71006C0E22 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				5646243C1FF822170074AC87 /* Metal.framework in Frameworks */,
				5646243B1FF822100074AC87 /* QuartzCore.framework in Frameworks */,
				56C5237F1D8F4985001F2F30 /* CoreAudio.framework in Frameworks */,
				FA73671F19A54144004122E4 /* CoreVideo.framework in Frameworks */,
				DB31406E17554B71006C0E22 /* Cocoa.framework in Frameworks */,
				DB31407017554B71006C0E22 /* IOKit.framework in Frameworks */,
				56C523811D8F498C001F2F30 /* CoreFoundation.framework in Frameworks */,
				DB31407217554B71006C0E22 /* Carbon.framework in Frameworks */,
				DB31408D17554D3C006C0E22 /* ForceFeedback.framework in Frameworks */,
				562C4AEA1D8F496300AF9EBE /* AudioToolbox.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0153844A006D81B07F000001 /* Public Headers */ = {
			isa = PBXGroup;
			children = (
				AA7557C71595D4D800BBD41B /* begin_code.h */,
				AA7557C81595D4D800BBD41B /* close_code.h */,
				AA7557C91595D4D800BBD41B /* SDL_assert.h */,
				AA7557CA1595D4D800BBD41B /* SDL_atomic.h */,
				AA7557CB1595D4D800BBD41B /* SDL_audio.h */,
				AADA5B8616CCAB3000107CF7 /* SDL_bits.h */,
				AA7557CC1595D4D800BBD41B /* SDL_blendmode.h */,
				AA7557CD1595D4D800BBD41B /* SDL_clipboard.h */,
				AA7557CE1595D4D800BBD41B /* SDL_config_macosx.h */,
				AA7557CF1595D4D800BBD41B /* SDL_config.h */,
				AA7557D01595D4D800BBD41B /* SDL_copying.h */,
				AA7557D11595D4D800BBD41B /* SDL_cpuinfo.h */,
				5C2EF7001FC9EF0F003F5197 /* SDL_egl.h */,
				AA7557D21595D4D800BBD41B /* SDL_endian.h */,
				AA7557D31595D4D800BBD41B /* SDL_error.h */,
				AA7557D41595D4D800BBD41B /* SDL_events.h */,
				567E2F2017C44C35005F1892 /* SDL_filesystem.h */,
				A77E6EB3167AB0A90010E40B /* SDL_gamecontroller.h */,
				AA7557D51595D4D800BBD41B /* SDL_gesture.h */,
				AA7557D61595D4D800BBD41B /* SDL_haptic.h */,
				AA7557D71595D4D800BBD41B /* SDL_hints.h */,
				AA7557D91595D4D800BBD41B /* SDL_joystick.h */,
				AA7557DA1595D4D800BBD41B /* SDL_keyboard.h */,
				AA7557DB1595D4D800BBD41B /* SDL_keycode.h */,
				AA7557DC1595D4D800BBD41B /* SDL_loadso.h */,
				AA7557DD1595D4D800BBD41B /* SDL_log.h */,
				AA7557DE1595D4D800BBD41B /* SDL_main.h */,
				AA9FF9591637CBF9000DF050 /* SDL_messagebox.h */,
				AA7557DF1595D4D800BBD41B /* SDL_mouse.h */,
				AA7557E01595D4D800BBD41B /* SDL_mutex.h */,
				AA7557E11595D4D800BBD41B /* SDL_name.h */,
				AAC070F4195606770073DCDF /* SDL_opengl_glext.h */,
				AA7557E21595D4D800BBD41B /* SDL_opengl.h */,
				AA7557E31595D4D800BBD41B /* SDL_opengles.h */,
				AAC070F5195606770073DCDF /* SDL_opengles2_gl2.h */,
				AAC070F6195606770073DCDF /* SDL_opengles2_gl2ext.h */,
				AAC070F7195606770073DCDF /* SDL_opengles2_gl2platform.h */,
				AAC070F8195606770073DCDF /* SDL_opengles2_khrplatform.h */,
				AA7557E41595D4D800BBD41B /* SDL_opengles2.h */,
				AA7557E51595D4D800BBD41B /* SDL_pixels.h */,
				AA7557E61595D4D800BBD41B /* SDL_platform.h */,
				AA7557E71595D4D800BBD41B /* SDL_power.h */,
				AA7557E81595D4D800BBD41B /* SDL_quit.h */,
				AA7557E91595D4D800BBD41B /* SDL_rect.h */,
				AA7557EA1595D4D800BBD41B /* SDL_render.h */,
				AA7557EB1595D4D800BBD41B /* SDL_revision.h */,
				AA7557EC1595D4D800BBD41B /* SDL_rwops.h */,
				AA7557ED1595D4D800BBD41B /* SDL_scancode.h */,
				F3950CD7212BC88D00F51292 /* SDL_sensor.h */,
				AA7557EE1595D4D800BBD41B /* SDL_shape.h */,
				AA7557EF1595D4D800BBD41B /* SDL_stdinc.h */,
				AA7557F01595D4D800BBD41B /* SDL_surface.h */,
				AA7557F11595D4D800BBD41B /* SDL_system.h */,
				AA7557F21595D4D800BBD41B /* SDL_syswm.h */,
				AA7557F31595D4D800BBD41B /* SDL_thread.h */,
				AA7557F41595D4D800BBD41B /* SDL_timer.h */,
				AA7557F51595D4D800BBD41B /* SDL_touch.h */,
				AA7557F61595D4D800BBD41B /* SDL_types.h */,
				AA7557F71595D4D800BBD41B /* SDL_version.h */,
				AA7557F81595D4D800BBD41B /* SDL_video.h */,
				4D4820431F0F10B400EDC31C /* SDL_vulkan.h */,
				AA7557F91595D4D800BBD41B /* SDL.h */,
			);
			name = "Public Headers";
			path = ../../include;
			sourceTree = "<group>";
		};
		034768DDFF38A45A11DB9C8B /* Products */ = {
			isa = PBXGroup;
			children = (
				BECDF66C0761BA81005FE872 /* SDL2.framework */,
				BECDF6B30761BA81005FE872 /* libSDL2.a */,
				BECDF6BE0761BA81005FE872 /* Standard DMG */,
				DB31407717554B71006C0E22 /* libSDL2.dylib */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		041B2C9712FA0D680087D585 /* render */ = {
			isa = PBXGroup;
			children = (
				AADC5A401FDA030E00960936 /* metal */,
				5C2EF6921FC986D8003F5197 /* opengles2 */,
				041B2C9A12FA0D680087D585 /* opengl */,
				041B2CA012FA0D680087D585 /* software */,
				041B2C9E12FA0D680087D585 /* SDL_render.c */,
				041B2C9F12FA0D680087D585 /* SDL_sysrender.h */,
				04409B8F12FA97ED00FB9AA8 /* SDL_yuv_sw_c.h */,
				04409B9012FA97ED00FB9AA8 /* SDL_yuv_sw.c */,
			);
			path = render;
			sourceTree = "<group>";
		};
		041B2C9A12FA0D680087D585 /* opengl */ = {
			isa = PBXGroup;
			children = (
				04043BBA12FEB1BE0076DB1F /* SDL_glfuncs.h */,
				0442EC1712FE1BBA004C9285 /* SDL_render_gl.c */,
				0435673C1303160F00BA5428 /* SDL_shaders_gl.c */,
				0435673D1303160F00BA5428 /* SDL_shaders_gl.h */,
			);
			path = opengl;
			sourceTree = "<group>";
		};
		041B2CA012FA0D680087D585 /* software */ = {
			isa = PBXGroup;
			children = (
				04F7803D12FB74A200FC43C0 /* SDL_blendfillrect.c */,
				04F7803E12FB74A200FC43C0 /* SDL_blendfillrect.h */,
				04F7803F12FB74A200FC43C0 /* SDL_blendline.c */,
				04F7804012FB74A200FC43C0 /* SDL_blendline.h */,
				04F7804112FB74A200FC43C0 /* SDL_blendpoint.c */,
				04F7804212FB74A200FC43C0 /* SDL_blendpoint.h */,
				04F7804312FB74A200FC43C0 /* SDL_draw.h */,
				04F7804412FB74A200FC43C0 /* SDL_drawline.c */,
				04F7804512FB74A200FC43C0 /* SDL_drawline.h */,
				04F7804612FB74A200FC43C0 /* SDL_drawpoint.c */,
				04F7804712FB74A200FC43C0 /* SDL_drawpoint.h */,
				0442EC1B12FE1BCB004C9285 /* SDL_render_sw.c */,
				0442EC1A12FE1BCB004C9285 /* SDL_render_sw_c.h */,
				AA628AC8159367B7005138DD /* SDL_rotate.c */,
				AA628AC9159367B7005138DD /* SDL_rotate.h */,
			);
			path = software;
			sourceTree = "<group>";
		};
		04BDFD7312E6671700899322 /* atomic */ = {
			isa = PBXGroup;
			children = (
				04BDFD7412E6671700899322 /* SDL_atomic.c */,
				04BDFD7512E6671700899322 /* SDL_spinlock.c */,
			);
			path = atomic;
			sourceTree = "<group>";
		};
		04BDFD7612E6671700899322 /* audio */ = {
			isa = PBXGroup;
			children = (
				04BDFD8712E6671700899322 /* disk */,
				04BDFD9312E6671700899322 /* dummy */,
				04BDFD9F12E6671700899322 /* coreaudio */,
				04BDFDB412E6671700899322 /* SDL_audio.c */,
				04BDFDB512E6671700899322 /* SDL_audio_c.h */,
				04BDFDB612E6671700899322 /* SDL_audiocvt.c */,
				04BDFDB712E6671700899322 /* SDL_audiodev.c */,
				04BDFDB812E6671700899322 /* SDL_audiodev_c.h */,
				04BDFDBA12E6671700899322 /* SDL_audiotypecvt.c */,
				04BDFDBB12E6671700899322 /* SDL_mixer.c */,
				04BDFDC212E6671700899322 /* SDL_sysaudio.h */,
				04BDFDC312E6671700899322 /* SDL_wave.c */,
				04BDFDC412E6671700899322 /* SDL_wave.h */,
			);
			path = audio;
			sourceTree = "<group>";
		};
		04BDFD8712E6671700899322 /* disk */ = {
			isa = PBXGroup;
			children = (
				04BDFD8812E6671700899322 /* SDL_diskaudio.c */,
				04BDFD8912E6671700899322 /* SDL_diskaudio.h */,
			);
			path = disk;
			sourceTree = "<group>";
		};
		04BDFD9312E6671700899322 /* dummy */ = {
			isa = PBXGroup;
			children = (
				04BDFD9412E6671700899322 /* SDL_dummyaudio.c */,
				04BDFD9512E6671700899322 /* SDL_dummyaudio.h */,
			);
			path = dummy;
			sourceTree = "<group>";
		};
		04BDFD9F12E6671700899322 /* coreaudio */ = {
			isa = PBXGroup;
			children = (
				04BDFDA112E6671700899322 /* SDL_coreaudio.h */,
				FABA34C61D8B5DB100915323 /* SDL_coreaudio.m */,
			);
			path = coreaudio;
			sourceTree = "<group>";
		};
		04BDFDD312E6671700899322 /* cpuinfo */ = {
			isa = PBXGroup;
			children = (
				04BDFDD412E6671700899322 /* SDL_cpuinfo.c */,
			);
			path = cpuinfo;
			sourceTree = "<group>";
		};
		04BDFDD512E6671700899322 /* events */ = {
			isa = PBXGroup;
			children = (
				04BDFDD612E6671700899322 /* blank_cursor.h */,
				04BDFDD712E6671700899322 /* default_cursor.h */,
				04BDFDD812E6671700899322 /* scancodes_darwin.h */,
				04BDFDD912E6671700899322 /* scancodes_linux.h */,
				04BDFDDB12E6671700899322 /* scancodes_xfree86.h */,
				04BDFDDD12E6671700899322 /* SDL_clipboardevents_c.h */,
				04BDFDDC12E6671700899322 /* SDL_clipboardevents.c */,
				F30D9CCB212EB4810047DF2E /* SDL_displayevents_c.h */,
				F30D9CCC212EB4810047DF2E /* SDL_displayevents.c */,
				566CDE8D148F0AC200C5A9BB /* SDL_dropevents_c.h */,
				566CDE8E148F0AC200C5A9BB /* SDL_dropevents.c */,
				04BDFDDF12E6671700899322 /* SDL_events_c.h */,
				04BDFDDE12E6671700899322 /* SDL_events.c */,
				04BDFDE112E6671700899322 /* SDL_gesture_c.h */,
				04BDFDE012E6671700899322 /* SDL_gesture.c */,
				04BDFDE312E6671700899322 /* SDL_keyboard_c.h */,
				04BDFDE212E6671700899322 /* SDL_keyboard.c */,
				04BDFDE512E6671700899322 /* SDL_mouse_c.h */,
				04BDFDE412E6671700899322 /* SDL_mouse.c */,
				04BDFDE612E6671700899322 /* SDL_quit.c */,
				04BDFDE712E6671700899322 /* SDL_sysevents.h */,
				04BDFDE912E6671700899322 /* SDL_touch_c.h */,
				04BDFDE812E6671700899322 /* SDL_touch.c */,
				04BDFDEB12E6671700899322 /* SDL_windowevents_c.h */,
				04BDFDEA12E6671700899322 /* SDL_windowevents.c */,
			);
			path = events;
			sourceTree = "<group>";
		};
		04BDFDEC12E6671700899322 /* file */ = {
			isa = PBXGroup;
			children = (
				04BDFDED12E6671700899322 /* cocoa */,
				04BDFDF012E6671700899322 /* SDL_rwops.c */,
			);
			path = file;
			sourceTree = "<group>";
		};
		04BDFDED12E6671700899322 /* cocoa */ = {
			isa = PBXGroup;
			children = (
				04BDFDEE12E6671700899322 /* SDL_rwopsbundlesupport.h */,
				04BDFDEF12E6671700899322 /* SDL_rwopsbundlesupport.m */,
			);
			path = cocoa;
			sourceTree = "<group>";
		};
		04BDFDF112E6671700899322 /* haptic */ = {
			isa = PBXGroup;
			children = (
				04BDFDF212E6671700899322 /* darwin */,
				04BDFDFA12E6671700899322 /* SDL_haptic.c */,
				04BDFDFB12E6671700899322 /* SDL_haptic_c.h */,
				04BDFDFC12E6671700899322 /* SDL_syshaptic.h */,
			);
			path = haptic;
			sourceTree = "<group>";
		};
		04BDFDF212E6671700899322 /* darwin */ = {
			isa = PBXGroup;
			children = (
				04BDFDF312E6671700899322 /* SDL_syshaptic.c */,
				DB89958518A1A5C50092407C /* SDL_syshaptic_c.h */,
			);
			path = darwin;
			sourceTree = "<group>";
		};
		04BDFDFF12E6671700899322 /* joystick */ = {
			isa = PBXGroup;
			children = (
				A704170C20F09AA600A82227 /* hidapi */,
				04BDFE0612E6671700899322 /* darwin */,
				04BDFE1612E6671700899322 /* SDL_joystick.c */,
				04BDFE1712E6671700899322 /* SDL_joystick_c.h */,
				BBFC088A164C6514003E6A99 /* SDL_gamecontroller.c */,
				04BDFE1812E6671700899322 /* SDL_sysjoystick.h */,
			);
			path = joystick;
			sourceTree = "<group>";
		};
		04BDFE0612E6671700899322 /* darwin */ = {
			isa = PBXGroup;
			children = (
				04BDFE0712E6671700899322 /* SDL_sysjoystick.c */,
				04BDFE0812E6671700899322 /* SDL_sysjoystick_c.h */,
			);
			path = darwin;
			sourceTree = "<group>";
		};
		04BDFE2F12E6671700899322 /* loadso */ = {
			isa = PBXGroup;
			children = (
				04BDFE3212E6671700899322 /* dlopen */,
			);
			path = loadso;
			sourceTree = "<group>";
		};
		04BDFE3212E6671700899322 /* dlopen */ = {
			isa = PBXGroup;
			children = (
				04BDFE3312E6671700899322 /* SDL_sysloadso.c */,
			);
			path = dlopen;
			sourceTree = "<group>";
		};
		04BDFE4512E6671700899322 /* power */ = {
			isa = PBXGroup;
			children = (
				04BDFE4A12E6671700899322 /* macosx */,
				04BDFE4E12E6671700899322 /* SDL_power.c */,
			);
			path = power;
			sourceTree = "<group>";
		};
		04BDFE4A12E6671700899322 /* macosx */ = {
			isa = PBXGroup;
			children = (
				04BDFE4B12E6671700899322 /* SDL_syspower.c */,
			);
			path = macosx;
			sourceTree = "<group>";
		};
		04BDFE5D12E6671700899322 /* stdlib */ = {
			isa = PBXGroup;
			children = (
				04BDFE5E12E6671700899322 /* SDL_getenv.c */,
				04BDFE5F12E6671700899322 /* SDL_iconv.c */,
				04BDFE6012E6671700899322 /* SDL_malloc.c */,
				04BDFE6112E6671700899322 /* SDL_qsort.c */,
				04BDFE6212E6671700899322 /* SDL_stdlib.c */,
				04BDFE6312E6671700899322 /* SDL_string.c */,
			);
			path = stdlib;
			sourceTree = "<group>";
		};
		04BDFE6412E6671800899322 /* thread */ = {
			isa = PBXGroup;
			children = (
				04BDFE7D12E6671800899322 /* pthread */,
				04BDFE8B12E6671800899322 /* SDL_systhread.h */,
				04BDFE8C12E6671800899322 /* SDL_thread.c */,
				04BDFE8D12E6671800899322 /* SDL_thread_c.h */,
			);
			path = thread;
			sourceTree = "<group>";
		};
		04BDFE7D12E6671800899322 /* pthread */ = {
			isa = PBXGroup;
			children = (
				04BDFE7E12E6671800899322 /* SDL_syscond.c */,
				04BDFE7F12E6671800899322 /* SDL_sysmutex.c */,
				04BDFE8012E6671800899322 /* SDL_sysmutex_c.h */,
				04BDFE8112E6671800899322 /* SDL_syssem.c */,
				04BDFE8212E6671800899322 /* SDL_systhread.c */,
				04BDFE8312E6671800899322 /* SDL_systhread_c.h */,
				AA0F8490178D5ECC00823F9D /* SDL_systls.c */,
			);
			path = pthread;
			sourceTree = "<group>";
		};
		04BDFE9512E6671800899322 /* timer */ = {
			isa = PBXGroup;
			children = (
				04BDFEA112E6671800899322 /* unix */,
				04BDFE9F12E6671800899322 /* SDL_timer.c */,
				04BDFEA012E6671800899322 /* SDL_timer_c.h */,
			);
			path = timer;
			sourceTree = "<group>";
		};
		04BDFEA112E6671800899322 /* unix */ = {
			isa = PBXGroup;
			children = (
				04BDFEA212E6671800899322 /* SDL_systimer.c */,
			);
			path = unix;
			sourceTree = "<group>";
		};
		04BDFEA712E6671800899322 /* video */ = {
			isa = PBXGroup;
			children = (
				04BDFEC112E6671800899322 /* cocoa */,
				04BDFEE712E6671800899322 /* dummy */,
				04BDFFB712E6671800899322 /* x11 */,
				AA9A7F0E1FB0200B00FED37F /* yuv2rgb */,
				04BDFF5012E6671800899322 /* SDL_blit_0.c */,
				04BDFF5112E6671800899322 /* SDL_blit_1.c */,
				04BDFF5212E6671800899322 /* SDL_blit_A.c */,
				04BDFF5312E6671800899322 /* SDL_blit_auto.c */,
				04BDFF5412E6671800899322 /* SDL_blit_auto.h */,
				04BDFF5512E6671800899322 /* SDL_blit_copy.c */,
				04BDFF5612E6671800899322 /* SDL_blit_copy.h */,
				04BDFF5712E6671800899322 /* SDL_blit_N.c */,
				04BDFF5812E6671800899322 /* SDL_blit_slow.c */,
				04BDFF5912E6671800899322 /* SDL_blit_slow.h */,
				04BDFF4E12E6671800899322 /* SDL_blit.c */,
				04BDFF4F12E6671800899322 /* SDL_blit.h */,
				04BDFF5A12E6671800899322 /* SDL_bmp.c */,
				04BDFF5B12E6671800899322 /* SDL_clipboard.c */,
				5C2EF6F61FC9EE35003F5197 /* SDL_egl_c.h */,
				5C2EF6F51FC9EE35003F5197 /* SDL_egl.c */,
				04BDFF6012E6671800899322 /* SDL_fillrect.c */,
				04BDFF6612E6671800899322 /* SDL_pixels_c.h */,
				04BDFF6512E6671800899322 /* SDL_pixels.c */,
				5C2EF6F41FC9EE34003F5197 /* SDL_rect_c.h */,
				04BDFF6712E6671800899322 /* SDL_rect.c */,
				04BDFF7012E6671800899322 /* SDL_RLEaccel_c.h */,
				04BDFF6F12E6671800899322 /* SDL_RLEaccel.c */,
				04BDFF7212E6671800899322 /* SDL_shape_internals.h */,
				04BDFF7112E6671800899322 /* SDL_shape.c */,
				04BDFF7312E6671800899322 /* SDL_stretch.c */,
				04BDFF7412E6671800899322 /* SDL_surface.c */,
				04BDFF7512E6671800899322 /* SDL_sysvideo.h */,
				04BDFF7612E6671800899322 /* SDL_video.c */,
				4D16644C1EDD6023003DE88E /* SDL_vulkan_internal.h */,
				4D16644D1EDD6023003DE88E /* SDL_vulkan_utils.c */,
				AA9A7F141FB0209C00FED37F /* SDL_yuv_c.h */,
				AA9A7F131FB0209C00FED37F /* SDL_yuv.c */,
			);
			path = video;
			sourceTree = "<group>";
		};
		04BDFEC112E6671800899322 /* cocoa */ = {
			isa = PBXGroup;
			children = (
				04BDFEC212E6671800899322 /* SDL_cocoaclipboard.h */,
				04BDFEC312E6671800899322 /* SDL_cocoaclipboard.m */,
				04BDFEC412E6671800899322 /* SDL_cocoaevents.h */,
				04BDFEC512E6671800899322 /* SDL_cocoaevents.m */,
				04BDFEC612E6671800899322 /* SDL_cocoakeyboard.h */,
				04BDFEC712E6671800899322 /* SDL_cocoakeyboard.m */,
				AABCC38B164063D200AB8930 /* SDL_cocoamessagebox.h */,
				AABCC38C164063D200AB8930 /* SDL_cocoamessagebox.m */,
				4D7517281EE2562B00820EEA /* SDL_cocoametalview.h */,
				4D1664501EDD60AD003DE88E /* SDL_cocoametalview.m */,
				04BDFEC812E6671800899322 /* SDL_cocoamodes.h */,
				04BDFEC912E6671800899322 /* SDL_cocoamodes.m */,
				04BDFECA12E6671800899322 /* SDL_cocoamouse.h */,
				04BDFECB12E6671800899322 /* SDL_cocoamouse.m */,
				D55A1B7F179F262300625D7C /* SDL_cocoamousetap.h */,
				D55A1B80179F262300625D7C /* SDL_cocoamousetap.m */,
				04BDFECC12E6671800899322 /* SDL_cocoaopengl.h */,
				04BDFECD12E6671800899322 /* SDL_cocoaopengl.m */,
				5C2EF6ED1FC9D0ED003F5197 /* SDL_cocoaopengles.h */,
				5C2EF6EC1FC9D0EC003F5197 /* SDL_cocoaopengles.m */,
				04BDFECE12E6671800899322 /* SDL_cocoashape.h */,
				04BDFECF12E6671800899322 /* SDL_cocoashape.m */,
				04BDFED012E6671800899322 /* SDL_cocoavideo.h */,
				04BDFED112E6671800899322 /* SDL_cocoavideo.m */,
				4D1664511EDD60AD003DE88E /* SDL_cocoavulkan.h */,
				4D1664521EDD60AD003DE88E /* SDL_cocoavulkan.m */,
				04BDFED212E6671800899322 /* SDL_cocoawindow.h */,
				04BDFED312E6671800899322 /* SDL_cocoawindow.m */,
			);
			path = cocoa;
			sourceTree = "<group>";
		};
		04BDFEE712E6671800899322 /* dummy */ = {
			isa = PBXGroup;
			children = (
				04BDFEE812E6671800899322 /* SDL_nullevents.c */,
				04BDFEE912E6671800899322 /* SDL_nullevents_c.h */,
				04F7803712FB748500FC43C0 /* SDL_nullframebuffer_c.h */,
				04F7803812FB748500FC43C0 /* SDL_nullframebuffer.c */,
				04BDFEEC12E6671800899322 /* SDL_nullvideo.c */,
				04BDFEED12E6671800899322 /* SDL_nullvideo.h */,
			);
			path = dummy;
			sourceTree = "<group>";
		};
		04BDFFB712E6671800899322 /* x11 */ = {
			isa = PBXGroup;
			children = (
				04BDFFB812E6671800899322 /* imKStoUCS.c */,
				04BDFFB912E6671800899322 /* imKStoUCS.h */,
				04BDFFBA12E6671800899322 /* SDL_x11clipboard.c */,
				04BDFFBB12E6671800899322 /* SDL_x11clipboard.h */,
				04BDFFBC12E6671800899322 /* SDL_x11dyn.c */,
				04BDFFBD12E6671800899322 /* SDL_x11dyn.h */,
				04BDFFBE12E6671800899322 /* SDL_x11events.c */,
				04BDFFBF12E6671800899322 /* SDL_x11events.h */,
				0442EC5812FE1C60004C9285 /* SDL_x11framebuffer.c */,
				0442EC5912FE1C60004C9285 /* SDL_x11framebuffer.h */,
				04BDFFC212E6671800899322 /* SDL_x11keyboard.c */,
				04BDFFC312E6671800899322 /* SDL_x11keyboard.h */,
				AA9E4092163BE51E007A2AD0 /* SDL_x11messagebox.c */,
				04BDFFC412E6671800899322 /* SDL_x11modes.c */,
				04BDFFC512E6671800899322 /* SDL_x11modes.h */,
				04BDFFC612E6671800899322 /* SDL_x11mouse.c */,
				04BDFFC712E6671800899322 /* SDL_x11mouse.h */,
				04BDFFC812E6671800899322 /* SDL_x11opengl.c */,
				04BDFFC912E6671800899322 /* SDL_x11opengl.h */,
				04BDFFCA12E6671800899322 /* SDL_x11opengles.c */,
				04BDFFCB12E6671800899322 /* SDL_x11opengles.h */,
				04BDFFCE12E6671800899322 /* SDL_x11shape.c */,
				04BDFFCF12E6671800899322 /* SDL_x11shape.h */,
				04BDFFD012E6671800899322 /* SDL_x11sym.h */,
				04BDFFD112E6671800899322 /* SDL_x11touch.c */,
				04BDFFD212E6671800899322 /* SDL_x11touch.h */,
				04BDFFD312E6671800899322 /* SDL_x11video.c */,
				04BDFFD412E6671800899322 /* SDL_x11video.h */,
				04BDFFD512E6671800899322 /* SDL_x11window.c */,
				04BDFFD612E6671800899322 /* SDL_x11window.h */,
				AA628ACF159367F2005138DD /* SDL_x11xinput2.c */,
				AA628AD0159367F2005138DD /* SDL_x11xinput2.h */,
			);
			path = x11;
			sourceTree = "<group>";
		};
		0867D691FE84028FC02AAC07 /* SDLFramework */ = {
			isa = PBXGroup;
			children = (
				F5A2EF3900C6A39A01000001 /* BUGS.txt */,
				F59C70FC00D5CB5801000001 /* pkg-support */,
				0153844A006D81B07F000001 /* Public Headers */,
				08FB77ACFE841707C02AAC07 /* Library Source */,
				034768DDFF38A45A11DB9C8B /* Products */,
				BECDF66B0761BA81005FE872 /* Info-Framework.plist */,
				564624341FF821B70074AC87 /* Frameworks */,
			);
			comments = "To build Universal Binaries, we have experimented with a variety of different options.\nThe complication is that we must retain compatibility with at least 10.2. \nThe Universal Binary defaults only work for > 10.3.9\n\nSo far, we have found:\ngcc 4.0.0 with Xcode 2.1 always links against libgcc_s. gcc 4.0.1 from Xcode 2.2 fixes this problem.\n\nBut gcc 4.0 will not work with < 10.3.9 because we continue to get an undefined symbol to _fprintf$LDBL128.\nSo we must use gcc 3.3 on PPC to accomplish 10.2 support. (But 4.0 is required for i386.)\n\nSetting the deployment target to 10.4 will disable prebinding, so for PPC, we set it less than 10.4 to preserve prebinding for legacy support.\n\nSetting the PPC SDKROOT to /Developers/SDKs/MacOSX10.2.8.sdk will link to 63.0.0 libSystem.B.dylib. Leaving it at current or 10.4u links to 88.1.2. However, as long as we are using gcc 3.3, it doesn't seem to matter as testing has demonstrated both will run. We have decided not to invoke the 10.2.8 SDK because it is not a default installed component with Xcode which will probably cause most people problems. However, rather than deleting the SDKROOT_ppc entry entirely, we have mapped it to 10.4u in case we decide we need to change this setting.\n\nTo use Altivec or SSE, we needed architecture specific flags:\nOTHER_CFLAGS_ppc\nOTHER_CFLAGS_i386\nOTHER_CFLAGS=$(OTHER_CFLAGS_($CURRENT_ARCH))\n\nThe general OTHER_CFLAGS needed to be manually mapped to architecture specific options because Xcode didn't do this automatically for us.\n\n\n";
			indentWidth = 4;
			name = SDLFramework;
			sourceTree = "<group>";
			tabWidth = 4;
			usesTabs = 0;
		};
		08FB77ACFE841707C02AAC07 /* Library Source */ = {
			isa = PBXGroup;
			children = (
				04BDFD7312E6671700899322 /* atomic */,
				04BDFD7612E6671700899322 /* audio */,
				04BDFDD312E6671700899322 /* cpuinfo */,
				56A6701C1856549B0007D20F /* dynapi */,
				04BDFDD512E6671700899322 /* events */,
				567E2F1F17C44BBB005F1892 /* filesystem */,
				04BDFDEC12E6671700899322 /* file */,
				04BDFDF112E6671700899322 /* haptic */,
				A73EBCD520F099C10043B449 /* hidapi */,
				04BDFDFF12E6671700899322 /* joystick */,
				04BDFE2F12E6671700899322 /* loadso */,
				04BDFE4512E6671700899322 /* power */,
				041B2C9712FA0D680087D585 /* render */,
				F3950CDB212BC8BC00F51292 /* sensor */,
				04BDFE5D12E6671700899322 /* stdlib */,
				04BDFE6412E6671800899322 /* thread */,
				04BDFE9512E6671800899322 /* timer */,
				04BDFEA712E6671800899322 /* video */,
				56A670081856545C0007D20F /* SDL_internal.h */,
				04BDFE5512E6671700899322 /* SDL_assert_c.h */,
				04BDFE5612E6671700899322 /* SDL_assert.c */,
				56115BB91DF72C6D00F47E1E /* SDL_dataqueue.c */,
				56115BBA1DF72C6D00F47E1E /* SDL_dataqueue.h */,
				04BDFE5812E6671700899322 /* SDL_error_c.h */,
				04BDFE5912E6671700899322 /* SDL_error.c */,
				0442EC5E12FE1C75004C9285 /* SDL_hints.c */,
				04BAC0C71300C2160055DE28 /* SDL_log.c */,
				04BDFE5C12E6671700899322 /* SDL.c */,
			);
			name = "Library Source";
			path = ../../src;
			sourceTree = "<group>";
		};
		564624341FF821B70074AC87 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				A7381E931D8B69C300B177DD /* AudioToolbox.framework */,
				007317C10858E15000B2BC32 /* Carbon.framework */,
				0073179D0858DECD00B2BC32 /* Cocoa.framework */,
				A7381E951D8B69D600B177DD /* CoreAudio.framework */,
				00D0D08310675DD9004B05EF /* CoreFoundation.framework */,
				FA73671C19A540EF004122E4 /* CoreVideo.framework */,
				00CFA89C106B4BA100758660 /* ForceFeedback.framework */,
				0073179F0858DECD00B2BC32 /* IOKit.framework */,
				564624371FF821CB0074AC87 /* Metal.framework */,
				564624351FF821B80074AC87 /* QuartzCore.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		567E2F1F17C44BBB005F1892 /* filesystem */ = {
			isa = PBXGroup;
			children = (
				567E2F1B17C44BB2005F1892 /* SDL_sysfilesystem.m */,
			);
			path = filesystem;
			sourceTree = "<group>";
		};
		56A6701C1856549B0007D20F /* dynapi */ = {
			isa = PBXGroup;
			children = (
				56A6701D185654B40007D20F /* SDL_dynapi_procs.h */,
				56A6701E185654B40007D20F /* SDL_dynapi.c */,
				56A6701F185654B40007D20F /* SDL_dynapi.h */,
				56A67020185654B40007D20F /* SDL_dynapi_overrides.h */,
			);
			path = dynapi;
			sourceTree = "<group>";
		};
		5C2EF6921FC986D8003F5197 /* opengles2 */ = {
			isa = PBXGroup;
			children = (
				5C2EF69B1FC987C6003F5197 /* SDL_gles2funcs.h */,
				5C2EF69C1FC987C6003F5197 /* SDL_render_gles2.c */,
				5C2EF69E1FC987C6003F5197 /* SDL_shaders_gles2.c */,
				5C2EF69D1FC987C6003F5197 /* SDL_shaders_gles2.h */,
			);
			path = opengles2;
			sourceTree = "<group>";
		};
		A704170720F09A6700A82227 /* mac */ = {
			isa = PBXGroup;
			children = (
				A704170820F09A9800A82227 /* hid.c */,
			);
			path = mac;
			sourceTree = "<group>";
		};
		A704170C20F09AA600A82227 /* hidapi */ = {
			isa = PBXGroup;
			children = (
				A704171020F09AC900A82227 /* controller_type.h */,
				A704171120F09AC900A82227 /* SDL_hidapi_ps4.c */,
				A704170F20F09AC800A82227 /* SDL_hidapi_switch.c */,
				A704171320F09AC900A82227 /* SDL_hidapi_xbox360.c */,
				A704171220F09AC900A82227 /* SDL_hidapi_xboxone.c */,
				A704170E20F09AC800A82227 /* SDL_hidapijoystick_c.h */,
				A704170D20F09AC800A82227 /* SDL_hidapijoystick.c */,
			);
			path = hidapi;
			sourceTree = "<group>";
		};
		A73EBCD520F099C10043B449 /* hidapi */ = {
			isa = PBXGroup;
			children = (
				A704170720F09A6700A82227 /* mac */,
			);
			path = hidapi;
			sourceTree = "<group>";
		};
		AA9A7F0E1FB0200B00FED37F /* yuv2rgb */ = {
			isa = PBXGroup;
			children = (
				AA9A7F101FB0206300FED37F /* yuv_rgb.c */,
				AA9A7F0F1FB0206300FED37F /* yuv_rgb.h */,
			);
			path = yuv2rgb;
			sourceTree = "<group>";
		};
		AADC5A401FDA030E00960936 /* metal */ = {
			isa = PBXGroup;
			children = (
				AADC5A421FDA035D00960936 /* SDL_render_metal.m */,
				AADC5A411FDA035D00960936 /* SDL_shaders_metal_osx.h */,
			);
			path = metal;
			sourceTree = "<group>";
		};
		F30D9C8D212CABB40047DF2E /* dummy */ = {
			isa = PBXGroup;
			children = (
				F30D9C8F212CABDB0047DF2E /* SDL_dummysensor.c */,
				F30D9C8E212CABDB0047DF2E /* SDL_dummysensor.h */,
			);
			path = dummy;
			sourceTree = "<group>";
		};
		F3950CDB212BC8BC00F51292 /* sensor */ = {
			isa = PBXGroup;
			children = (
				F30D9C8D212CABB40047DF2E /* dummy */,
				F30D9C81212BC94E0047DF2E /* SDL_sensor_c.h */,
				F30D9C83212BC94F0047DF2E /* SDL_sensor.c */,
				F30D9C82212BC94F0047DF2E /* SDL_syssensor.h */,
			);
			name = sensor;
			path = ../../src/sensor;
			sourceTree = SOURCE_ROOT;
		};
		F59C70FC00D5CB5801000001 /* pkg-support */ = {
			isa = PBXGroup;
			children = (
				F59C710100D5CB5801000001 /* resources */,
				F59C710600D5CB5801000001 /* SDL.info */,
			);
			path = "pkg-support";
			sourceTree = SOURCE_ROOT;
		};
		F59C710100D5CB5801000001 /* resources */ = {
			isa = PBXGroup;
			children = (
				00794D3F09D0C461003FC8A1 /* License.txt */,
				F59C710300D5CB5801000001 /* ReadMe.txt */,
			);
			path = resources;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		BECDF5FF0761BA81005FE872 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				AA7557FA1595D4D800BBD41B /* begin_code.h in Headers */,
				AADC5A431FDA035D00960936 /* SDL_shaders_metal_osx.h in Headers */,
				AA7557FC1595D4D800BBD41B /* close_code.h in Headers */,
				AA75585E1595D4D800BBD41B /* SDL.h in Headers */,
				AA7557FE1595D4D800BBD41B /* SDL_assert.h in Headers */,
				AA7558001595D4D800BBD41B /* SDL_atomic.h in Headers */,
				F30D9C87212BC94F0047DF2E /* SDL_syssensor.h in Headers */,
				AA7558021595D4D800BBD41B /* SDL_audio.h in Headers */,
				AADA5B8716CCAB3000107CF7 /* SDL_bits.h in Headers */,
				AA7558041595D4D800BBD41B /* SDL_blendmode.h in Headers */,
				5C2EF7011FC9EF10003F5197 /* SDL_egl.h in Headers */,
				AA7558061595D4D800BBD41B /* SDL_clipboard.h in Headers */,
				AA7558081595D4D800BBD41B /* SDL_config_macosx.h in Headers */,
				AA75580A1595D4D800BBD41B /* SDL_config.h in Headers */,
				56A670091856545C0007D20F /* SDL_internal.h in Headers */,
				AA75580C1595D4D800BBD41B /* SDL_copying.h in Headers */,
				AA75580E1595D4D800BBD41B /* SDL_cpuinfo.h in Headers */,
				AA7558101595D4D800BBD41B /* SDL_endian.h in Headers */,
				AA7558121595D4D800BBD41B /* SDL_error.h in Headers */,
				AA7558141595D4D800BBD41B /* SDL_events.h in Headers */,
				567E2F2117C44C35005F1892 /* SDL_filesystem.h in Headers */,
				A77E6EB4167AB0A90010E40B /* SDL_gamecontroller.h in Headers */,
				AA7558161595D4D800BBD41B /* SDL_gesture.h in Headers */,
				AA7558181595D4D800BBD41B /* SDL_haptic.h in Headers */,
				AA75581A1595D4D800BBD41B /* SDL_hints.h in Headers */,
				AA75581E1595D4D800BBD41B /* SDL_joystick.h in Headers */,
				AA7558201595D4D800BBD41B /* SDL_keyboard.h in Headers */,
				F3950CD8212BC88D00F51292 /* SDL_sensor.h in Headers */,
				AA7558221595D4D800BBD41B /* SDL_keycode.h in Headers */,
				F30D9C84212BC94F0047DF2E /* SDL_sensor_c.h in Headers */,
				AA7558241595D4D800BBD41B /* SDL_loadso.h in Headers */,
				AA7558261595D4D800BBD41B /* SDL_log.h in Headers */,
				5C2EF6F91FC9EE35003F5197 /* SDL_egl_c.h in Headers */,
				AA7558281595D4D800BBD41B /* SDL_main.h in Headers */,
				AA9FF95A1637CBF9000DF050 /* SDL_messagebox.h in Headers */,
				AA75582A1595D4D800BBD41B /* SDL_mouse.h in Headers */,
				AA75582C1595D4D800BBD41B /* SDL_mutex.h in Headers */,
				AA75582E1595D4D800BBD41B /* SDL_name.h in Headers */,
				AA7558301595D4D800BBD41B /* SDL_opengl.h in Headers */,
				AAC070F9195606770073DCDF /* SDL_opengl_glext.h in Headers */,
				AA7558321595D4D800BBD41B /* SDL_opengles.h in Headers */,
				AA7558341595D4D800BBD41B /* SDL_opengles2.h in Headers */,
				AAC070FC195606770073DCDF /* SDL_opengles2_gl2.h in Headers */,
				AAC070FF195606770073DCDF /* SDL_opengles2_gl2ext.h in Headers */,
				AAC07102195606770073DCDF /* SDL_opengles2_gl2platform.h in Headers */,
				AAC07105195606770073DCDF /* SDL_opengles2_khrplatform.h in Headers */,
				AA7558361595D4D800BBD41B /* SDL_pixels.h in Headers */,
				AA7558381595D4D800BBD41B /* SDL_platform.h in Headers */,
				AA75583A1595D4D800BBD41B /* SDL_power.h in Headers */,
				AA75583C1595D4D800BBD41B /* SDL_quit.h in Headers */,
				AA75583E1595D4D800BBD41B /* SDL_rect.h in Headers */,
				5C2EF69F1FC987C6003F5197 /* SDL_gles2funcs.h in Headers */,
				AA7558401595D4D800BBD41B /* SDL_render.h in Headers */,
				AA7558421595D4D800BBD41B /* SDL_revision.h in Headers */,
				AA7558441595D4D800BBD41B /* SDL_rwops.h in Headers */,
				AA7558461595D4D800BBD41B /* SDL_scancode.h in Headers */,
				A704171720F09AC900A82227 /* SDL_hidapijoystick_c.h in Headers */,
				AA7558481595D4D800BBD41B /* SDL_shape.h in Headers */,
				AA75584A1595D4D800BBD41B /* SDL_stdinc.h in Headers */,
				AA75584C1595D4D800BBD41B /* SDL_surface.h in Headers */,
				AA75584E1595D4D800BBD41B /* SDL_system.h in Headers */,
				AA7558501595D4D800BBD41B /* SDL_syswm.h in Headers */,
				AA7558521595D4D800BBD41B /* SDL_thread.h in Headers */,
				AA7558541595D4D800BBD41B /* SDL_timer.h in Headers */,
				AA7558561595D4D800BBD41B /* SDL_touch.h in Headers */,
				AA7558581595D4D800BBD41B /* SDL_types.h in Headers */,
				AA75585A1595D4D800BBD41B /* SDL_version.h in Headers */,
				AA75585C1595D4D800BBD41B /* SDL_video.h in Headers */,
				5C2EF6A11FC987C6003F5197 /* SDL_shaders_gles2.h in Headers */,
				AA8167541F5E727800518735 /* SDL_vulkan.h in Headers */,
				4D7517291EE2562B00820EEA /* SDL_cocoametalview.h in Headers */,
				04BD000912E6671800899322 /* SDL_diskaudio.h in Headers */,
				04BD001112E6671800899322 /* SDL_dummyaudio.h in Headers */,
				04BD001912E6671800899322 /* SDL_coreaudio.h in Headers */,
				04BD002712E6671800899322 /* SDL_audio_c.h in Headers */,
				04BD002A12E6671800899322 /* SDL_audiodev_c.h in Headers */,
				04BD003412E6671800899322 /* SDL_sysaudio.h in Headers */,
				04BD003612E6671800899322 /* SDL_wave.h in Headers */,
				04BD004212E6671800899322 /* blank_cursor.h in Headers */,
				04BD004312E6671800899322 /* default_cursor.h in Headers */,
				04BD004412E6671800899322 /* scancodes_darwin.h in Headers */,
				04BD004512E6671800899322 /* scancodes_linux.h in Headers */,
				04BD004712E6671800899322 /* scancodes_xfree86.h in Headers */,
				04BD004912E6671800899322 /* SDL_clipboardevents_c.h in Headers */,
				56A6702A185654B40007D20F /* SDL_dynapi_overrides.h in Headers */,
				04BD004B12E6671800899322 /* SDL_events_c.h in Headers */,
				04BD004D12E6671800899322 /* SDL_gesture_c.h in Headers */,
				04BD004F12E6671800899322 /* SDL_keyboard_c.h in Headers */,
				AA9A7F111FB0206400FED37F /* yuv_rgb.h in Headers */,
				04BD005112E6671800899322 /* SDL_mouse_c.h in Headers */,
				04BD005312E6671800899322 /* SDL_sysevents.h in Headers */,
				04BD005512E6671800899322 /* SDL_touch_c.h in Headers */,
				04BD005712E6671800899322 /* SDL_windowevents_c.h in Headers */,
				04BD005812E6671800899322 /* SDL_rwopsbundlesupport.h in Headers */,
				04BD006012E6671800899322 /* SDL_haptic_c.h in Headers */,
				04BD006112E6671800899322 /* SDL_syshaptic.h in Headers */,
				04BD006712E6671800899322 /* SDL_sysjoystick_c.h in Headers */,
				04BD007112E6671800899322 /* SDL_joystick_c.h in Headers */,
				04BD007212E6671800899322 /* SDL_sysjoystick.h in Headers */,
				04BD009B12E6671800899322 /* SDL_assert_c.h in Headers */,
				04BD009E12E6671800899322 /* SDL_error_c.h in Headers */,
				04BD00BF12E6671800899322 /* SDL_sysmutex_c.h in Headers */,
				04BD00C212E6671800899322 /* SDL_systhread_c.h in Headers */,
				04BD00C912E6671800899322 /* SDL_systhread.h in Headers */,
				04BD00CB12E6671800899322 /* SDL_thread_c.h in Headers */,
				04BD00D812E6671800899322 /* SDL_timer_c.h in Headers */,
				04BD00F312E6671800899322 /* SDL_cocoaclipboard.h in Headers */,
				4D1664541EDD60AD003DE88E /* SDL_cocoavulkan.h in Headers */,
				04BD00F512E6671800899322 /* SDL_cocoaevents.h in Headers */,
				04BD00F712E6671800899322 /* SDL_cocoakeyboard.h in Headers */,
				5C2EF6EF1FC9D0ED003F5197 /* SDL_cocoaopengles.h in Headers */,
				04BD00F912E6671800899322 /* SDL_cocoamodes.h in Headers */,
				04BD00FB12E6671800899322 /* SDL_cocoamouse.h in Headers */,
				04BD00FD12E6671800899322 /* SDL_cocoaopengl.h in Headers */,
				04BD00FF12E6671800899322 /* SDL_cocoashape.h in Headers */,
				56115BBC1DF72C6D00F47E1E /* SDL_dataqueue.h in Headers */,
				04BD010112E6671800899322 /* SDL_cocoavideo.h in Headers */,
				04BD010312E6671800899322 /* SDL_cocoawindow.h in Headers */,
				5C2EF6F71FC9EE35003F5197 /* SDL_rect_c.h in Headers */,
				04BD011812E6671800899322 /* SDL_nullevents_c.h in Headers */,
				04BD011C12E6671800899322 /* SDL_nullvideo.h in Headers */,
				04BD017612E6671800899322 /* SDL_blit.h in Headers */,
				04BD017B12E6671800899322 /* SDL_blit_auto.h in Headers */,
				04BD017D12E6671800899322 /* SDL_blit_copy.h in Headers */,
				04BD018012E6671800899322 /* SDL_blit_slow.h in Headers */,
				04BD018D12E6671800899322 /* SDL_pixels_c.h in Headers */,
				04BD019712E6671800899322 /* SDL_RLEaccel_c.h in Headers */,
				04BD019912E6671800899322 /* SDL_shape_internals.h in Headers */,
				04BD019C12E6671800899322 /* SDL_sysvideo.h in Headers */,
				04BD01DC12E6671800899322 /* imKStoUCS.h in Headers */,
				F30D9CCD212EB4810047DF2E /* SDL_displayevents_c.h in Headers */,
				4D16644E1EDD6023003DE88E /* SDL_vulkan_internal.h in Headers */,
				04BD01DE12E6671800899322 /* SDL_x11clipboard.h in Headers */,
				04BD01E012E6671800899322 /* SDL_x11dyn.h in Headers */,
				04BD01E212E6671800899322 /* SDL_x11events.h in Headers */,
				04BD01E612E6671800899322 /* SDL_x11keyboard.h in Headers */,
				04BD01E812E6671800899322 /* SDL_x11modes.h in Headers */,
				04BD01EA12E6671800899322 /* SDL_x11mouse.h in Headers */,
				04BD01EC12E6671800899322 /* SDL_x11opengl.h in Headers */,
				04BD01EE12E6671800899322 /* SDL_x11opengles.h in Headers */,
				04BD01F212E6671800899322 /* SDL_x11shape.h in Headers */,
				04BD01F312E6671800899322 /* SDL_x11sym.h in Headers */,
				56A67021185654B40007D20F /* SDL_dynapi_procs.h in Headers */,
				04BD01F512E6671800899322 /* SDL_x11touch.h in Headers */,
				04BD01F712E6671800899322 /* SDL_x11video.h in Headers */,
				04BD01F912E6671800899322 /* SDL_x11window.h in Headers */,
				041B2CA612FA0D680087D585 /* SDL_sysrender.h in Headers */,
				AA9A7F161FB0209D00FED37F /* SDL_yuv_c.h in Headers */,
				A704171D20F09AC900A82227 /* controller_type.h in Headers */,
				04409B9312FA97ED00FB9AA8 /* SDL_yuv_sw_c.h in Headers */,
				04F7803912FB748500FC43C0 /* SDL_nullframebuffer_c.h in Headers */,
				04F7804A12FB74A200FC43C0 /* SDL_blendfillrect.h in Headers */,
				04F7804C12FB74A200FC43C0 /* SDL_blendline.h in Headers */,
				04F7804E12FB74A200FC43C0 /* SDL_blendpoint.h in Headers */,
				56A67027185654B40007D20F /* SDL_dynapi.h in Headers */,
				04F7804F12FB74A200FC43C0 /* SDL_draw.h in Headers */,
				F30D9C90212CABDC0047DF2E /* SDL_dummysensor.h in Headers */,
				04F7805112FB74A200FC43C0 /* SDL_drawline.h in Headers */,
				04F7805312FB74A200FC43C0 /* SDL_drawpoint.h in Headers */,
				0442EC1C12FE1BCB004C9285 /* SDL_render_sw_c.h in Headers */,
				0442EC5B12FE1C60004C9285 /* SDL_x11framebuffer.h in Headers */,
				04043BBB12FEB1BE0076DB1F /* SDL_glfuncs.h in Headers */,
				0435673F1303160F00BA5428 /* SDL_shaders_gl.h in Headers */,
				566CDE8F148F0AC200C5A9BB /* SDL_dropevents_c.h in Headers */,
				AA628ACC159367B7005138DD /* SDL_rotate.h in Headers */,
				AA628AD3159367F2005138DD /* SDL_x11xinput2.h in Headers */,
				AABCC38D164063D200AB8930 /* SDL_cocoamessagebox.h in Headers */,
				D55A1B81179F262300625D7C /* SDL_cocoamousetap.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BECDF66E0761BA81005FE872 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				DB0F490B17CA57ED008798C5 /* SDL_filesystem.h in Headers */,
				AA7557FB1595D4D800BBD41B /* begin_code.h in Headers */,
				AA7557FD1595D4D800BBD41B /* close_code.h in Headers */,
				AA75585F1595D4D800BBD41B /* SDL.h in Headers */,
				AA7557FF1595D4D800BBD41B /* SDL_assert.h in Headers */,
				AA7558011595D4D800BBD41B /* SDL_atomic.h in Headers */,
				AA7558031595D4D800BBD41B /* SDL_audio.h in Headers */,
				AADA5B8816CCAB3000107CF7 /* SDL_bits.h in Headers */,
				AA7558051595D4D800BBD41B /* SDL_blendmode.h in Headers */,
				AA7558071595D4D800BBD41B /* SDL_clipboard.h in Headers */,
				AA75580B1595D4D800BBD41B /* SDL_config.h in Headers */,
				F30D9C85212BC94F0047DF2E /* SDL_sensor_c.h in Headers */,
				AA7558091595D4D800BBD41B /* SDL_config_macosx.h in Headers */,
				AA75580D1595D4D800BBD41B /* SDL_copying.h in Headers */,
				AA75580F1595D4D800BBD41B /* SDL_cpuinfo.h in Headers */,
				AA7558111595D4D800BBD41B /* SDL_endian.h in Headers */,
				AA7558131595D4D800BBD41B /* SDL_error.h in Headers */,
				AA7558151595D4D800BBD41B /* SDL_events.h in Headers */,
				A77E6EB5167AB0A90010E40B /* SDL_gamecontroller.h in Headers */,
				AA7558171595D4D800BBD41B /* SDL_gesture.h in Headers */,
				AA7558191595D4D800BBD41B /* SDL_haptic.h in Headers */,
				AA75581B1595D4D800BBD41B /* SDL_hints.h in Headers */,
				AA75581F1595D4D800BBD41B /* SDL_joystick.h in Headers */,
				AA7558211595D4D800BBD41B /* SDL_keyboard.h in Headers */,
				AA7558231595D4D800BBD41B /* SDL_keycode.h in Headers */,
				A704171E20F09AC900A82227 /* controller_type.h in Headers */,
				AA7558251595D4D800BBD41B /* SDL_loadso.h in Headers */,
				AA7558271595D4D800BBD41B /* SDL_log.h in Headers */,
				AA7558291595D4D800BBD41B /* SDL_main.h in Headers */,
				AAC07106195606770073DCDF /* SDL_opengles2_khrplatform.h in Headers */,
				DB0F489417C400ED008798C5 /* SDL_messagebox.h in Headers */,
				AA75582B1595D4D800BBD41B /* SDL_mouse.h in Headers */,
				AA75582D1595D4D800BBD41B /* SDL_mutex.h in Headers */,
				AA75582F1595D4D800BBD41B /* SDL_name.h in Headers */,
				AA7558311595D4D800BBD41B /* SDL_opengl.h in Headers */,
				AA7558331595D4D800BBD41B /* SDL_opengles.h in Headers */,
				56A67028185654B40007D20F /* SDL_dynapi.h in Headers */,
				AA7558351595D4D800BBD41B /* SDL_opengles2.h in Headers */,
				AA7558371595D4D800BBD41B /* SDL_pixels.h in Headers */,
				AA7558391595D4D800BBD41B /* SDL_platform.h in Headers */,
				AA75583B1595D4D800BBD41B /* SDL_power.h in Headers */,
				AA75583D1595D4D800BBD41B /* SDL_quit.h in Headers */,
				AA75583F1595D4D800BBD41B /* SDL_rect.h in Headers */,
				AA7558411595D4D800BBD41B /* SDL_render.h in Headers */,
				AA7558431595D4D800BBD41B /* SDL_revision.h in Headers */,
				AA7558451595D4D800BBD41B /* SDL_rwops.h in Headers */,
				AA7558471595D4D800BBD41B /* SDL_scancode.h in Headers */,
				AA7558491595D4D800BBD41B /* SDL_shape.h in Headers */,
				56F9D55E1DF73B7C00C15B5D /* SDL_dataqueue.h in Headers */,
				56A6702B185654B40007D20F /* SDL_dynapi_overrides.h in Headers */,
				F30D9CCE212EB4810047DF2E /* SDL_displayevents_c.h in Headers */,
				AA75584B1595D4D800BBD41B /* SDL_stdinc.h in Headers */,
				AA75584D1595D4D800BBD41B /* SDL_surface.h in Headers */,
				AA75584F1595D4D800BBD41B /* SDL_system.h in Headers */,
				AA7558511595D4D800BBD41B /* SDL_syswm.h in Headers */,
				AAC070FA195606770073DCDF /* SDL_opengl_glext.h in Headers */,
				AA7558531595D4D800BBD41B /* SDL_thread.h in Headers */,
				F30D9C88212BC94F0047DF2E /* SDL_syssensor.h in Headers */,
				AA7558551595D4D800BBD41B /* SDL_timer.h in Headers */,
				AA7558571595D4D800BBD41B /* SDL_touch.h in Headers */,
				AA7558591595D4D800BBD41B /* SDL_types.h in Headers */,
				AA75585B1595D4D800BBD41B /* SDL_version.h in Headers */,
				AA75585D1595D4D800BBD41B /* SDL_video.h in Headers */,
				04BD022512E6671800899322 /* SDL_diskaudio.h in Headers */,
				56A6700A1856545C0007D20F /* SDL_internal.h in Headers */,
				04BD022D12E6671800899322 /* SDL_dummyaudio.h in Headers */,
				04BD023512E6671800899322 /* SDL_coreaudio.h in Headers */,
				04BD024312E6671800899322 /* SDL_audio_c.h in Headers */,
				04BD024612E6671800899322 /* SDL_audiodev_c.h in Headers */,
				AAC070FD195606770073DCDF /* SDL_opengles2_gl2.h in Headers */,
				04BD025012E6671800899322 /* SDL_sysaudio.h in Headers */,
				04BD025212E6671800899322 /* SDL_wave.h in Headers */,
				04BD025D12E6671800899322 /* blank_cursor.h in Headers */,
				04BD025E12E6671800899322 /* default_cursor.h in Headers */,
				04BD025F12E6671800899322 /* scancodes_darwin.h in Headers */,
				AADC5A4F1FDA05E600960936 /* SDL_cocoavulkan.h in Headers */,
				5C2EF6FA1FC9EE64003F5197 /* SDL_egl_c.h in Headers */,
				04BD026012E6671800899322 /* scancodes_linux.h in Headers */,
				04BD026212E6671800899322 /* scancodes_xfree86.h in Headers */,
				04BD026412E6671800899322 /* SDL_clipboardevents_c.h in Headers */,
				04BD026612E6671800899322 /* SDL_events_c.h in Headers */,
				56A67022185654B40007D20F /* SDL_dynapi_procs.h in Headers */,
				04BD026812E6671800899322 /* SDL_gesture_c.h in Headers */,
				AADC5A4D1FDA05D300960936 /* SDL_vulkan_internal.h in Headers */,
				04BD026A12E6671800899322 /* SDL_keyboard_c.h in Headers */,
				AADC5A491FDA05C100960936 /* yuv_rgb.h in Headers */,
				04BD026C12E6671800899322 /* SDL_mouse_c.h in Headers */,
				04BD026E12E6671800899322 /* SDL_sysevents.h in Headers */,
				04BD027012E6671800899322 /* SDL_touch_c.h in Headers */,
				04BD027212E6671800899322 /* SDL_windowevents_c.h in Headers */,
				04BD027312E6671800899322 /* SDL_rwopsbundlesupport.h in Headers */,
				5C2EF6FC1FC9EE64003F5197 /* SDL_rect_c.h in Headers */,
				F30D9C91212CABDC0047DF2E /* SDL_dummysensor.h in Headers */,
				04BD027B12E6671800899322 /* SDL_haptic_c.h in Headers */,
				04BD027C12E6671800899322 /* SDL_syshaptic.h in Headers */,
				04BD028212E6671800899322 /* SDL_sysjoystick_c.h in Headers */,
				04BD028C12E6671800899322 /* SDL_joystick_c.h in Headers */,
				04BD028D12E6671800899322 /* SDL_sysjoystick.h in Headers */,
				F3950CD9212BC88D00F51292 /* SDL_sensor.h in Headers */,
				04BD02B512E6671800899322 /* SDL_assert_c.h in Headers */,
				04BD02B812E6671800899322 /* SDL_error_c.h in Headers */,
				04BD02D912E6671800899322 /* SDL_sysmutex_c.h in Headers */,
				04BD02DC12E6671800899322 /* SDL_systhread_c.h in Headers */,
				04BD02E312E6671800899322 /* SDL_systhread.h in Headers */,
				04BD02E512E6671800899322 /* SDL_thread_c.h in Headers */,
				A704171820F09AC900A82227 /* SDL_hidapijoystick_c.h in Headers */,
				04BD02F212E6671800899322 /* SDL_timer_c.h in Headers */,
				04BD030D12E6671800899322 /* SDL_cocoaclipboard.h in Headers */,
				04BD030F12E6671800899322 /* SDL_cocoaevents.h in Headers */,
				04BD031112E6671800899322 /* SDL_cocoakeyboard.h in Headers */,
				04BD031312E6671800899322 /* SDL_cocoamodes.h in Headers */,
				04BD031512E6671800899322 /* SDL_cocoamouse.h in Headers */,
				04BD031712E6671800899322 /* SDL_cocoaopengl.h in Headers */,
				04BD031912E6671800899322 /* SDL_cocoashape.h in Headers */,
				AAC07103195606770073DCDF /* SDL_opengles2_gl2platform.h in Headers */,
				5C2EF6F01FC9D181003F5197 /* SDL_cocoaopengles.h in Headers */,
				04BD031B12E6671800899322 /* SDL_cocoavideo.h in Headers */,
				04BD031D12E6671800899322 /* SDL_cocoawindow.h in Headers */,
				04BD033212E6671800899322 /* SDL_nullevents_c.h in Headers */,
				04BD033612E6671800899322 /* SDL_nullvideo.h in Headers */,
				04BD039012E6671800899322 /* SDL_blit.h in Headers */,
				04BD039512E6671800899322 /* SDL_blit_auto.h in Headers */,
				04BD039712E6671800899322 /* SDL_blit_copy.h in Headers */,
				04BD039A12E6671800899322 /* SDL_blit_slow.h in Headers */,
				04BD03A712E6671800899322 /* SDL_pixels_c.h in Headers */,
				5C2EF6A71FC98D2D003F5197 /* SDL_gles2funcs.h in Headers */,
				04BD03B112E6671800899322 /* SDL_RLEaccel_c.h in Headers */,
				04BD03B312E6671800899322 /* SDL_shape_internals.h in Headers */,
				04BD03B612E6671800899322 /* SDL_sysvideo.h in Headers */,
				04BD03F412E6671800899322 /* imKStoUCS.h in Headers */,
				04BD03F612E6671800899322 /* SDL_x11clipboard.h in Headers */,
				04BD03F812E6671800899322 /* SDL_x11dyn.h in Headers */,
				04BD03FA12E6671800899322 /* SDL_x11events.h in Headers */,
				04BD03FE12E6671800899322 /* SDL_x11keyboard.h in Headers */,
				04BD040012E6671800899322 /* SDL_x11modes.h in Headers */,
				04BD040212E6671800899322 /* SDL_x11mouse.h in Headers */,
				04BD040412E6671800899322 /* SDL_x11opengl.h in Headers */,
				04BD040612E6671800899322 /* SDL_x11opengles.h in Headers */,
				04BD040A12E6671800899322 /* SDL_x11shape.h in Headers */,
				04BD040B12E6671800899322 /* SDL_x11sym.h in Headers */,
				04BD040D12E6671800899322 /* SDL_x11touch.h in Headers */,
				04BD040F12E6671800899322 /* SDL_x11video.h in Headers */,
				AAC07100195606770073DCDF /* SDL_opengles2_gl2ext.h in Headers */,
				04BD041112E6671800899322 /* SDL_x11window.h in Headers */,
				AADC5A4B1FDA05CF00960936 /* SDL_yuv_c.h in Headers */,
				041B2CAC12FA0D680087D585 /* SDL_sysrender.h in Headers */,
				04409B9712FA97ED00FB9AA8 /* SDL_yuv_sw_c.h in Headers */,
				04F7803B12FB748500FC43C0 /* SDL_nullframebuffer_c.h in Headers */,
				5C2EF6AA1FC98D2D003F5197 /* SDL_shaders_gles2.h in Headers */,
				04F7805612FB74A200FC43C0 /* SDL_blendfillrect.h in Headers */,
				04F7805812FB74A200FC43C0 /* SDL_blendline.h in Headers */,
				04F7805A12FB74A200FC43C0 /* SDL_blendpoint.h in Headers */,
				AADC5A461FDA047B00960936 /* SDL_shaders_metal_osx.h in Headers */,
				04F7805B12FB74A200FC43C0 /* SDL_draw.h in Headers */,
				04F7805D12FB74A200FC43C0 /* SDL_drawline.h in Headers */,
				04F7805F12FB74A200FC43C0 /* SDL_drawpoint.h in Headers */,
				0442EC1E12FE1BCB004C9285 /* SDL_render_sw_c.h in Headers */,
				0442EC5D12FE1C60004C9285 /* SDL_x11framebuffer.h in Headers */,
				04043BBC12FEB1BE0076DB1F /* SDL_glfuncs.h in Headers */,
				043567411303160F00BA5428 /* SDL_shaders_gl.h in Headers */,
				AA628ACD159367B7005138DD /* SDL_rotate.h in Headers */,
				AA628AD4159367F2005138DD /* SDL_x11xinput2.h in Headers */,
				AABCC38E164063D200AB8930 /* SDL_cocoamessagebox.h in Headers */,
				D55A1B85179F278E00625D7C /* SDL_cocoamousetap.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB313F7317554B71006C0E22 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				DB0F490C17CA57ED008798C5 /* SDL_filesystem.h in Headers */,
				DB313FC817554B71006C0E22 /* begin_code.h in Headers */,
				DB313FC917554B71006C0E22 /* close_code.h in Headers */,
				DB313FF917554B71006C0E22 /* SDL.h in Headers */,
				DB313FCA17554B71006C0E22 /* SDL_assert.h in Headers */,
				DB313FCB17554B71006C0E22 /* SDL_atomic.h in Headers */,
				DB313FCC17554B71006C0E22 /* SDL_audio.h in Headers */,
				DB313FFC17554B71006C0E22 /* SDL_bits.h in Headers */,
				DB313FCD17554B71006C0E22 /* SDL_blendmode.h in Headers */,
				DB313FCE17554B71006C0E22 /* SDL_clipboard.h in Headers */,
				DB313FD017554B71006C0E22 /* SDL_config.h in Headers */,
				F30D9C86212BC94F0047DF2E /* SDL_sensor_c.h in Headers */,
				DB313FCF17554B71006C0E22 /* SDL_config_macosx.h in Headers */,
				DB313FD117554B71006C0E22 /* SDL_copying.h in Headers */,
				DB313FD217554B71006C0E22 /* SDL_cpuinfo.h in Headers */,
				DB313FD317554B71006C0E22 /* SDL_endian.h in Headers */,
				DB313FD417554B71006C0E22 /* SDL_error.h in Headers */,
				DB313FD517554B71006C0E22 /* SDL_events.h in Headers */,
				DB313FFB17554B71006C0E22 /* SDL_gamecontroller.h in Headers */,
				DB313FD617554B71006C0E22 /* SDL_gesture.h in Headers */,
				DB313FD717554B71006C0E22 /* SDL_haptic.h in Headers */,
				DB313FD817554B71006C0E22 /* SDL_hints.h in Headers */,
				DB313FD917554B71006C0E22 /* SDL_joystick.h in Headers */,
				DB313FDA17554B71006C0E22 /* SDL_keyboard.h in Headers */,
				DB313FDB17554B71006C0E22 /* SDL_keycode.h in Headers */,
				A704171F20F09AC900A82227 /* controller_type.h in Headers */,
				DB313FDC17554B71006C0E22 /* SDL_loadso.h in Headers */,
				DB313FDD17554B71006C0E22 /* SDL_log.h in Headers */,
				DB313FDE17554B71006C0E22 /* SDL_main.h in Headers */,
				AAC07107195606770073DCDF /* SDL_opengles2_khrplatform.h in Headers */,
				DB0F489317C400E6008798C5 /* SDL_messagebox.h in Headers */,
				DB313FDF17554B71006C0E22 /* SDL_mouse.h in Headers */,
				DB313FE017554B71006C0E22 /* SDL_mutex.h in Headers */,
				DB313FE117554B71006C0E22 /* SDL_name.h in Headers */,
				DB313FE217554B71006C0E22 /* SDL_opengl.h in Headers */,
				DB313FE317554B71006C0E22 /* SDL_opengles.h in Headers */,
				56A67029185654B40007D20F /* SDL_dynapi.h in Headers */,
				DB313FE417554B71006C0E22 /* SDL_opengles2.h in Headers */,
				DB313FE517554B71006C0E22 /* SDL_pixels.h in Headers */,
				DB313FE617554B71006C0E22 /* SDL_platform.h in Headers */,
				DB313FE717554B71006C0E22 /* SDL_power.h in Headers */,
				DB313FE817554B71006C0E22 /* SDL_quit.h in Headers */,
				DB313FE917554B71006C0E22 /* SDL_rect.h in Headers */,
				DB313FEA17554B71006C0E22 /* SDL_render.h in Headers */,
				DB313FEB17554B71006C0E22 /* SDL_revision.h in Headers */,
				DB313FEC17554B71006C0E22 /* SDL_rwops.h in Headers */,
				DB313FED17554B71006C0E22 /* SDL_scancode.h in Headers */,
				DB313FEE17554B71006C0E22 /* SDL_shape.h in Headers */,
				56F9D55F1DF73B7D00C15B5D /* SDL_dataqueue.h in Headers */,
				56A6702C185654B40007D20F /* SDL_dynapi_overrides.h in Headers */,
				F30D9CCF212EB4810047DF2E /* SDL_displayevents_c.h in Headers */,
				DB313FEF17554B71006C0E22 /* SDL_stdinc.h in Headers */,
				DB313FF017554B71006C0E22 /* SDL_surface.h in Headers */,
				DB313FF117554B71006C0E22 /* SDL_system.h in Headers */,
				DB313FF217554B71006C0E22 /* SDL_syswm.h in Headers */,
				AAC070FB195606770073DCDF /* SDL_opengl_glext.h in Headers */,
				DB313FF317554B71006C0E22 /* SDL_thread.h in Headers */,
				F30D9C89212BC94F0047DF2E /* SDL_syssensor.h in Headers */,
				DB313FF417554B71006C0E22 /* SDL_timer.h in Headers */,
				DB313FF517554B71006C0E22 /* SDL_touch.h in Headers */,
				DB313FF617554B71006C0E22 /* SDL_types.h in Headers */,
				DB313FF717554B71006C0E22 /* SDL_version.h in Headers */,
				DB313FF817554B71006C0E22 /* SDL_video.h in Headers */,
				DB313F7417554B71006C0E22 /* SDL_diskaudio.h in Headers */,
				56A6700B1856545C0007D20F /* SDL_internal.h in Headers */,
				DB313F7517554B71006C0E22 /* SDL_dummyaudio.h in Headers */,
				DB313F7617554B71006C0E22 /* SDL_coreaudio.h in Headers */,
				DB313F7717554B71006C0E22 /* SDL_audio_c.h in Headers */,
				DB313F7817554B71006C0E22 /* SDL_audiodev_c.h in Headers */,
				AAC070FE195606770073DCDF /* SDL_opengles2_gl2.h in Headers */,
				DB313F7A17554B71006C0E22 /* SDL_sysaudio.h in Headers */,
				DB313F7B17554B71006C0E22 /* SDL_wave.h in Headers */,
				DB313F7C17554B71006C0E22 /* blank_cursor.h in Headers */,
				DB313F7D17554B71006C0E22 /* default_cursor.h in Headers */,
				DB313F7E17554B71006C0E22 /* scancodes_darwin.h in Headers */,
				AADC5A501FDA05E600960936 /* SDL_cocoavulkan.h in Headers */,
				5C2EF6FD1FC9EE65003F5197 /* SDL_egl_c.h in Headers */,
				DB313F7F17554B71006C0E22 /* scancodes_linux.h in Headers */,
				DB313F8017554B71006C0E22 /* scancodes_xfree86.h in Headers */,
				DB313F8117554B71006C0E22 /* SDL_clipboardevents_c.h in Headers */,
				DB313F8217554B71006C0E22 /* SDL_events_c.h in Headers */,
				56A67023185654B40007D20F /* SDL_dynapi_procs.h in Headers */,
				DB313F8317554B71006C0E22 /* SDL_gesture_c.h in Headers */,
				AADC5A4E1FDA05D400960936 /* SDL_vulkan_internal.h in Headers */,
				DB313F8417554B71006C0E22 /* SDL_keyboard_c.h in Headers */,
				AADC5A4A1FDA05C200960936 /* yuv_rgb.h in Headers */,
				DB313F8517554B71006C0E22 /* SDL_mouse_c.h in Headers */,
				DB313F8617554B71006C0E22 /* SDL_sysevents.h in Headers */,
				DB313F8717554B71006C0E22 /* SDL_touch_c.h in Headers */,
				DB313F8817554B71006C0E22 /* SDL_windowevents_c.h in Headers */,
				DB313F8917554B71006C0E22 /* SDL_rwopsbundlesupport.h in Headers */,
				5C2EF6FF1FC9EE65003F5197 /* SDL_rect_c.h in Headers */,
				F30D9C92212CABDC0047DF2E /* SDL_dummysensor.h in Headers */,
				DB313F8A17554B71006C0E22 /* SDL_haptic_c.h in Headers */,
				DB313F8B17554B71006C0E22 /* SDL_syshaptic.h in Headers */,
				DB313F8C17554B71006C0E22 /* SDL_sysjoystick_c.h in Headers */,
				DB313F8D17554B71006C0E22 /* SDL_joystick_c.h in Headers */,
				DB313F8E17554B71006C0E22 /* SDL_sysjoystick.h in Headers */,
				F3950CDA212BC88D00F51292 /* SDL_sensor.h in Headers */,
				DB313F8F17554B71006C0E22 /* SDL_assert_c.h in Headers */,
				DB313F9017554B71006C0E22 /* SDL_error_c.h in Headers */,
				DB313F9217554B71006C0E22 /* SDL_sysmutex_c.h in Headers */,
				DB313F9317554B71006C0E22 /* SDL_systhread_c.h in Headers */,
				DB313F9417554B71006C0E22 /* SDL_systhread.h in Headers */,
				DB313F9517554B71006C0E22 /* SDL_thread_c.h in Headers */,
				A704171920F09AC900A82227 /* SDL_hidapijoystick_c.h in Headers */,
				DB313F9617554B71006C0E22 /* SDL_timer_c.h in Headers */,
				DB313F9717554B71006C0E22 /* SDL_cocoaclipboard.h in Headers */,
				DB313F9817554B71006C0E22 /* SDL_cocoaevents.h in Headers */,
				DB313F9917554B71006C0E22 /* SDL_cocoakeyboard.h in Headers */,
				DB313F9A17554B71006C0E22 /* SDL_cocoamodes.h in Headers */,
				DB313F9B17554B71006C0E22 /* SDL_cocoamouse.h in Headers */,
				DB313F9C17554B71006C0E22 /* SDL_cocoaopengl.h in Headers */,
				DB313F9D17554B71006C0E22 /* SDL_cocoashape.h in Headers */,
				AAC07104195606770073DCDF /* SDL_opengles2_gl2platform.h in Headers */,
				5C2EF6F21FC9D182003F5197 /* SDL_cocoaopengles.h in Headers */,
				DB313F9E17554B71006C0E22 /* SDL_cocoavideo.h in Headers */,
				DB313F9F17554B71006C0E22 /* SDL_cocoawindow.h in Headers */,
				DB313FA017554B71006C0E22 /* SDL_nullevents_c.h in Headers */,
				DB313FA117554B71006C0E22 /* SDL_nullvideo.h in Headers */,
				DB313FA217554B71006C0E22 /* SDL_blit.h in Headers */,
				DB313FA317554B71006C0E22 /* SDL_blit_auto.h in Headers */,
				DB313FA417554B71006C0E22 /* SDL_blit_copy.h in Headers */,
				DB313FA517554B71006C0E22 /* SDL_blit_slow.h in Headers */,
				DB313FA617554B71006C0E22 /* SDL_pixels_c.h in Headers */,
				5C2EF6AB1FC98D2E003F5197 /* SDL_gles2funcs.h in Headers */,
				DB313FA717554B71006C0E22 /* SDL_RLEaccel_c.h in Headers */,
				DB313FA817554B71006C0E22 /* SDL_shape_internals.h in Headers */,
				DB313FA917554B71006C0E22 /* SDL_sysvideo.h in Headers */,
				DB313FAA17554B71006C0E22 /* imKStoUCS.h in Headers */,
				DB313FAB17554B71006C0E22 /* SDL_x11clipboard.h in Headers */,
				DB313FAC17554B71006C0E22 /* SDL_x11dyn.h in Headers */,
				DB313FAD17554B71006C0E22 /* SDL_x11events.h in Headers */,
				DB313FAE17554B71006C0E22 /* SDL_x11keyboard.h in Headers */,
				DB313FAF17554B71006C0E22 /* SDL_x11modes.h in Headers */,
				DB313FB017554B71006C0E22 /* SDL_x11mouse.h in Headers */,
				DB313FB117554B71006C0E22 /* SDL_x11opengl.h in Headers */,
				DB313FB217554B71006C0E22 /* SDL_x11opengles.h in Headers */,
				DB313FB317554B71006C0E22 /* SDL_x11shape.h in Headers */,
				DB313FB417554B71006C0E22 /* SDL_x11sym.h in Headers */,
				DB313FB517554B71006C0E22 /* SDL_x11touch.h in Headers */,
				DB313FB617554B71006C0E22 /* SDL_x11video.h in Headers */,
				AAC07101195606770073DCDF /* SDL_opengles2_gl2ext.h in Headers */,
				DB313FB717554B71006C0E22 /* SDL_x11window.h in Headers */,
				AADC5A4C1FDA05CF00960936 /* SDL_yuv_c.h in Headers */,
				DB313FB817554B71006C0E22 /* SDL_sysrender.h in Headers */,
				DB313FBA17554B71006C0E22 /* SDL_yuv_sw_c.h in Headers */,
				DB313FBB17554B71006C0E22 /* SDL_nullframebuffer_c.h in Headers */,
				5C2EF6AE1FC98D2E003F5197 /* SDL_shaders_gles2.h in Headers */,
				DB313FBC17554B71006C0E22 /* SDL_blendfillrect.h in Headers */,
				DB313FBD17554B71006C0E22 /* SDL_blendline.h in Headers */,
				DB313FBE17554B71006C0E22 /* SDL_blendpoint.h in Headers */,
				AADC5A471FDA047E00960936 /* SDL_shaders_metal_osx.h in Headers */,
				DB313FBF17554B71006C0E22 /* SDL_draw.h in Headers */,
				DB313FC017554B71006C0E22 /* SDL_drawline.h in Headers */,
				DB313FC117554B71006C0E22 /* SDL_drawpoint.h in Headers */,
				DB313FC217554B71006C0E22 /* SDL_render_sw_c.h in Headers */,
				DB313FC317554B71006C0E22 /* SDL_x11framebuffer.h in Headers */,
				DB313FC417554B71006C0E22 /* SDL_glfuncs.h in Headers */,
				DB313FC517554B71006C0E22 /* SDL_shaders_gl.h in Headers */,
				DB313FC617554B71006C0E22 /* SDL_rotate.h in Headers */,
				DB313FC717554B71006C0E22 /* SDL_x11xinput2.h in Headers */,
				DB313FFA17554B71006C0E22 /* SDL_cocoamessagebox.h in Headers */,
				D55A1B86179F278F00625D7C /* SDL_cocoamousetap.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		BECDF5FE0761BA81005FE872 /* Framework */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0073177A0858DB0500B2BC32 /* Build configuration list for PBXNativeTarget "Framework" */;
			buildPhases = (
				BECDF5FF0761BA81005FE872 /* Headers */,
				BECDF62A0761BA81005FE872 /* Resources */,
				BECDF62C0761BA81005FE872 /* Sources */,
				BECDF6680761BA81005FE872 /* Frameworks */,
			);
			buildRules = (
			);
			comments = "We recommend installing to /Library/Frameworks\nAn alternative is $(HOME)/Library/Frameworks for per-user if permissions are an issue.\n\nAdd the framework to the Groups & Files panel (under Linked Frameworks is a good place) and enable the check box for the targets that need to link to it. You can also manually add \"-framework SDL\" to your linker flags if you don't like the check box system.\n\nAdd /Library/Frameworks/SDL.framework/Headers to your header search path\nAdd /Library/Frameworks to your library search path\n(Adjust the two above if installed in $(HOME)/Library/Frameworks. You can also list both paths if you want robustness.)\n\nWe used to use an exports file. It was becoming a maintenance issue we kept neglecting, so we have removed it. If you need it back, set the \"Exported Symbols File\" option to:\n../../src/main/macosx/exports/SDL.x\n(You may need to regenerate the exports list. There is a Makefile in that directory that you can run from the command line to rebuild it.)\nLong term, we want to utilize gcc 4.0's new visibility feature (analogous to declspec on Windows). Other platforms would benefit from this change too. The downside is that we still use gcc 3.3 for the PowerPC build here so only our x86 builds will cull the symbols if we go down this route (and don't use the exports file).\n\n";
			dependencies = (
			);
			name = Framework;
			productInstallPath = "@executable_path/../Frameworks";
			productName = SDL;
			productReference = BECDF66C0761BA81005FE872 /* SDL2.framework */;
			productType = "com.apple.product-type.framework";
		};
		BECDF66D0761BA81005FE872 /* Static Library */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0073177E0858DB0500B2BC32 /* Build configuration list for PBXNativeTarget "Static Library" */;
			buildPhases = (
				BECDF66E0761BA81005FE872 /* Headers */,
				BECDF6790761BA81005FE872 /* Sources */,
				BECDF6B10761BA81005FE872 /* Frameworks */,
				BECDF6B20761BA81005FE872 /* Rez */,
			);
			buildRules = (
			);
			comments = "This produces libsdl.a, which is the static build of SDL. You will have to link to the Cocoa and OpenGL frameworks in your application.";
			dependencies = (
			);
			name = "Static Library";
			productInstallPath = /usr/local/lib;
			productName = "Static Library";
			productReference = BECDF6B30761BA81005FE872 /* libSDL2.a */;
			productType = "com.apple.product-type.library.static";
		};
		BECDF6BB0761BA81005FE872 /* Standard DMG */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 007317860858DB0500B2BC32 /* Build configuration list for PBXNativeTarget "Standard DMG" */;
			buildPhases = (
				BECDF6BD0761BA81005FE872 /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
				BECDF6C60761BA81005FE872 /* PBXTargetDependency */,
			);
			name = "Standard DMG";
			productInstallPath = /usr/local/bin;
			productName = "Standard Package";
			productReference = BECDF6BE0761BA81005FE872 /* Standard DMG */;
			productType = "com.apple.product-type.tool";
		};
		DB313F7217554B71006C0E22 /* Shared Library */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB31407417554B71006C0E22 /* Build configuration list for PBXNativeTarget "Shared Library" */;
			buildPhases = (
				DB313F7317554B71006C0E22 /* Headers */,
				DB313FFD17554B71006C0E22 /* Sources */,
				DB31406B17554B71006C0E22 /* Frameworks */,
				DB31407317554B71006C0E22 /* Rez */,
			);
			buildRules = (
			);
			comments = "This produces libSDL2.dylib, which is the shared build of SDL.";
			dependencies = (
			);
			name = "Shared Library";
			productInstallPath = /usr/local/lib;
			productName = "Shared Library";
			productReference = DB31407717554B71006C0E22 /* libSDL2.dylib */;
			productType = "com.apple.product-type.library.dynamic";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		0867D690FE84028FC02AAC07 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0900;
			};
			buildConfigurationList = 0073178E0858DB0500B2BC32 /* Build configuration list for PBXProject "SDL" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 1;
			knownRegions = (
				English,
				Japanese,
				French,
				German,
			);
			mainGroup = 0867D691FE84028FC02AAC07 /* SDLFramework */;
			productRefGroup = 034768DDFF38A45A11DB9C8B /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				BECDF5FE0761BA81005FE872 /* Framework */,
				BECDF66D0761BA81005FE872 /* Static Library */,
				DB313F7217554B71006C0E22 /* Shared Library */,
				BECDF6BB0761BA81005FE872 /* Standard DMG */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		BECDF62A0761BA81005FE872 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXRezBuildPhase section */
		BECDF6B20761BA81005FE872 /* Rez */ = {
			isa = PBXRezBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB31407317554B71006C0E22 /* Rez */ = {
			isa = PBXRezBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXRezBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		BECDF6BD0761BA81005FE872 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 12;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Sign framework\nif [ \"$SDL_CODESIGN_IDENTITY\" != \"\" ]; then\n    codesign --force --deep --sign \"$SDL_CODESIGN_IDENTITY\" $TARGET_BUILD_DIR/SDL2.framework/Versions/A\nfi\n\n# clean up the framework, remove headers, extra files\nmkdir -p build/dmg-tmp\nxcrun CpMac -r $TARGET_BUILD_DIR/SDL2.framework build/dmg-tmp/\n\ncp pkg-support/resources/License.txt build/dmg-tmp\ncp pkg-support/resources/ReadMe.txt build/dmg-tmp\n\n# remove the .DS_Store files if any (we may want to provide one in the future for fancy .dmgs)\nfind build/dmg-tmp -name .DS_Store -exec rm -f \"{}\" \\;\n\n# for fancy .dmg\nmkdir -p build/dmg-tmp/.logo\ncp pkg-support/resources/SDL_DS_Store build/dmg-tmp/.DS_Store\ncp pkg-support/sdl_logo.pdf build/dmg-tmp/.logo\n\n# create the dmg\nhdiutil create -ov -fs HFS+ -volname SDL2 -srcfolder build/dmg-tmp build/SDL2.dmg\n\n# clean up\nrm -rf build/dmg-tmp";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		BECDF62C0761BA81005FE872 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				04BDFFFB12E6671800899322 /* SDL_atomic.c in Sources */,
				04BDFFFC12E6671800899322 /* SDL_spinlock.c in Sources */,
				5C2EF6A21FC987C6003F5197 /* SDL_shaders_gles2.c in Sources */,
				56115BBB1DF72C6D00F47E1E /* SDL_dataqueue.c in Sources */,
				04BD000812E6671800899322 /* SDL_diskaudio.c in Sources */,
				04BD001012E6671800899322 /* SDL_dummyaudio.c in Sources */,
				04BD002612E6671800899322 /* SDL_audio.c in Sources */,
				04BD002812E6671800899322 /* SDL_audiocvt.c in Sources */,
				04BD002912E6671800899322 /* SDL_audiodev.c in Sources */,
				04BD002C12E6671800899322 /* SDL_audiotypecvt.c in Sources */,
				04BD002D12E6671800899322 /* SDL_mixer.c in Sources */,
				04BD003512E6671800899322 /* SDL_wave.c in Sources */,
				04BD004112E6671800899322 /* SDL_cpuinfo.c in Sources */,
				04BD004812E6671800899322 /* SDL_clipboardevents.c in Sources */,
				04BD004A12E6671800899322 /* SDL_events.c in Sources */,
				A704172620F09AC900A82227 /* SDL_hidapi_xbox360.c in Sources */,
				04BD004C12E6671800899322 /* SDL_gesture.c in Sources */,
				04BD004E12E6671800899322 /* SDL_keyboard.c in Sources */,
				04BD005012E6671800899322 /* SDL_mouse.c in Sources */,
				04BD005212E6671800899322 /* SDL_quit.c in Sources */,
				04BD005412E6671800899322 /* SDL_touch.c in Sources */,
				04BD005612E6671800899322 /* SDL_windowevents.c in Sources */,
				04BD005912E6671800899322 /* SDL_rwopsbundlesupport.m in Sources */,
				04BD005A12E6671800899322 /* SDL_rwops.c in Sources */,
				04BD005B12E6671800899322 /* SDL_syshaptic.c in Sources */,
				04BD005F12E6671800899322 /* SDL_haptic.c in Sources */,
				4D1664551EDD60AD003DE88E /* SDL_cocoavulkan.m in Sources */,
				04BD006612E6671800899322 /* SDL_sysjoystick.c in Sources */,
				04BD007012E6671800899322 /* SDL_joystick.c in Sources */,
				04BD008812E6671800899322 /* SDL_sysloadso.c in Sources */,
				04BD009412E6671800899322 /* SDL_syspower.c in Sources */,
				04BD009612E6671800899322 /* SDL_power.c in Sources */,
				04BD009C12E6671800899322 /* SDL_assert.c in Sources */,
				4D1664531EDD60AD003DE88E /* SDL_cocoametalview.m in Sources */,
				04BD009F12E6671800899322 /* SDL_error.c in Sources */,
				04BD00A212E6671800899322 /* SDL.c in Sources */,
				04BD00A312E6671800899322 /* SDL_getenv.c in Sources */,
				04BD00A412E6671800899322 /* SDL_iconv.c in Sources */,
				04BD00A512E6671800899322 /* SDL_malloc.c in Sources */,
				04BD00A612E6671800899322 /* SDL_qsort.c in Sources */,
				04BD00A712E6671800899322 /* SDL_stdlib.c in Sources */,
				04BD00A812E6671800899322 /* SDL_string.c in Sources */,
				04BD00BD12E6671800899322 /* SDL_syscond.c in Sources */,
				04BD00BE12E6671800899322 /* SDL_sysmutex.c in Sources */,
				FABA34C71D8B5DB100915323 /* SDL_coreaudio.m in Sources */,
				04BD00C012E6671800899322 /* SDL_syssem.c in Sources */,
				04BD00C112E6671800899322 /* SDL_systhread.c in Sources */,
				04BD00CA12E6671800899322 /* SDL_thread.c in Sources */,
				04BD00D712E6671800899322 /* SDL_timer.c in Sources */,
				04BD00D912E6671800899322 /* SDL_systimer.c in Sources */,
				04BD00F412E6671800899322 /* SDL_cocoaclipboard.m in Sources */,
				04BD00F612E6671800899322 /* SDL_cocoaevents.m in Sources */,
				04BD00F812E6671800899322 /* SDL_cocoakeyboard.m in Sources */,
				AA9A7F151FB0209D00FED37F /* SDL_yuv.c in Sources */,
				A704171A20F09AC900A82227 /* SDL_hidapi_switch.c in Sources */,
				04BD00FA12E6671800899322 /* SDL_cocoamodes.m in Sources */,
				4D16644F1EDD6023003DE88E /* SDL_vulkan_utils.c in Sources */,
				04BD00FC12E6671800899322 /* SDL_cocoamouse.m in Sources */,
				04BD00FE12E6671800899322 /* SDL_cocoaopengl.m in Sources */,
				04BD010012E6671800899322 /* SDL_cocoashape.m in Sources */,
				04BD010212E6671800899322 /* SDL_cocoavideo.m in Sources */,
				04BD010412E6671800899322 /* SDL_cocoawindow.m in Sources */,
				04BD011712E6671800899322 /* SDL_nullevents.c in Sources */,
				A704172320F09AC900A82227 /* SDL_hidapi_xboxone.c in Sources */,
				04BD011B12E6671800899322 /* SDL_nullvideo.c in Sources */,
				04BD017512E6671800899322 /* SDL_blit.c in Sources */,
				04BD017712E6671800899322 /* SDL_blit_0.c in Sources */,
				04BD017812E6671800899322 /* SDL_blit_1.c in Sources */,
				AA9A7F121FB0206400FED37F /* yuv_rgb.c in Sources */,
				04BD017912E6671800899322 /* SDL_blit_A.c in Sources */,
				04BD017A12E6671800899322 /* SDL_blit_auto.c in Sources */,
				04BD017C12E6671800899322 /* SDL_blit_copy.c in Sources */,
				A704172020F09AC900A82227 /* SDL_hidapi_ps4.c in Sources */,
				A704170920F09A9800A82227 /* hid.c in Sources */,
				04BD017E12E6671800899322 /* SDL_blit_N.c in Sources */,
				04BD017F12E6671800899322 /* SDL_blit_slow.c in Sources */,
				04BD018112E6671800899322 /* SDL_bmp.c in Sources */,
				04BD018212E6671800899322 /* SDL_clipboard.c in Sources */,
				04BD018712E6671800899322 /* SDL_fillrect.c in Sources */,
				04BD018C12E6671800899322 /* SDL_pixels.c in Sources */,
				04BD018E12E6671800899322 /* SDL_rect.c in Sources */,
				04BD019612E6671800899322 /* SDL_RLEaccel.c in Sources */,
				A704171420F09AC900A82227 /* SDL_hidapijoystick.c in Sources */,
				04BD019812E6671800899322 /* SDL_shape.c in Sources */,
				04BD019A12E6671800899322 /* SDL_stretch.c in Sources */,
				04BD019B12E6671800899322 /* SDL_surface.c in Sources */,
				04BD019D12E6671800899322 /* SDL_video.c in Sources */,
				04BD01DB12E6671800899322 /* imKStoUCS.c in Sources */,
				04BD01DD12E6671800899322 /* SDL_x11clipboard.c in Sources */,
				04BD01DF12E6671800899322 /* SDL_x11dyn.c in Sources */,
				5C2EF6A01FC987C6003F5197 /* SDL_render_gles2.c in Sources */,
				04BD01E112E6671800899322 /* SDL_x11events.c in Sources */,
				04BD01E512E6671800899322 /* SDL_x11keyboard.c in Sources */,
				04BD01E712E6671800899322 /* SDL_x11modes.c in Sources */,
				04BD01E912E6671800899322 /* SDL_x11mouse.c in Sources */,
				04BD01EB12E6671800899322 /* SDL_x11opengl.c in Sources */,
				04BD01ED12E6671800899322 /* SDL_x11opengles.c in Sources */,
				04BD01F112E6671800899322 /* SDL_x11shape.c in Sources */,
				04BD01F412E6671800899322 /* SDL_x11touch.c in Sources */,
				04BD01F612E6671800899322 /* SDL_x11video.c in Sources */,
				04BD01F812E6671800899322 /* SDL_x11window.c in Sources */,
				041B2CA512FA0D680087D585 /* SDL_render.c in Sources */,
				04409B9412FA97ED00FB9AA8 /* SDL_yuv_sw.c in Sources */,
				04F7803A12FB748500FC43C0 /* SDL_nullframebuffer.c in Sources */,
				04F7804912FB74A200FC43C0 /* SDL_blendfillrect.c in Sources */,
				AADC5A441FDA035D00960936 /* SDL_render_metal.m in Sources */,
				04F7804B12FB74A200FC43C0 /* SDL_blendline.c in Sources */,
				04F7804D12FB74A200FC43C0 /* SDL_blendpoint.c in Sources */,
				F30D9C93212CABDC0047DF2E /* SDL_dummysensor.c in Sources */,
				5C2EF6F81FC9EE35003F5197 /* SDL_egl.c in Sources */,
				04F7805012FB74A200FC43C0 /* SDL_drawline.c in Sources */,
				04F7805212FB74A200FC43C0 /* SDL_drawpoint.c in Sources */,
				0442EC1812FE1BBA004C9285 /* SDL_render_gl.c in Sources */,
				F30D9CD0212EB4810047DF2E /* SDL_displayevents.c in Sources */,
				0442EC1D12FE1BCB004C9285 /* SDL_render_sw.c in Sources */,
				0442EC5A12FE1C60004C9285 /* SDL_x11framebuffer.c in Sources */,
				0442EC5F12FE1C75004C9285 /* SDL_hints.c in Sources */,
				56A67024185654B40007D20F /* SDL_dynapi.c in Sources */,
				F30D9C8A212BC94F0047DF2E /* SDL_sensor.c in Sources */,
				04BAC0C81300C2160055DE28 /* SDL_log.c in Sources */,
				5C2EF6EE1FC9D0ED003F5197 /* SDL_cocoaopengles.m in Sources */,
				0435673E1303160F00BA5428 /* SDL_shaders_gl.c in Sources */,
				566CDE90148F0AC200C5A9BB /* SDL_dropevents.c in Sources */,
				AA628ACA159367B7005138DD /* SDL_rotate.c in Sources */,
				AA628AD1159367F2005138DD /* SDL_x11xinput2.c in Sources */,
				AA9E4093163BE51E007A2AD0 /* SDL_x11messagebox.c in Sources */,
				AABCC38F164063D200AB8930 /* SDL_cocoamessagebox.m in Sources */,
				AA0AD09D16648D1700CE5896 /* SDL_gamecontroller.c in Sources */,
				AA0F8491178D5ECC00823F9D /* SDL_systls.c in Sources */,
				D55A1B82179F262300625D7C /* SDL_cocoamousetap.m in Sources */,
				567E2F1C17C44BB2005F1892 /* SDL_sysfilesystem.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BECDF6790761BA81005FE872 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				4D1664571EDD61F0003DE88E /* SDL_cocoametalview.m in Sources */,
				4D1664581EDD61F0003DE88E /* SDL_cocoavulkan.m in Sources */,
				5C2EF6A91FC98D2D003F5197 /* SDL_shaders_gles2.c in Sources */,
				4D1664561EDD61DA003DE88E /* SDL_vulkan_utils.c in Sources */,
				04BD021712E6671800899322 /* SDL_atomic.c in Sources */,
				04BD021812E6671800899322 /* SDL_spinlock.c in Sources */,
				56F9D55C1DF73B6B00C15B5D /* SDL_dataqueue.c in Sources */,
				04BD022412E6671800899322 /* SDL_diskaudio.c in Sources */,
				04BD022C12E6671800899322 /* SDL_dummyaudio.c in Sources */,
				04BD024212E6671800899322 /* SDL_audio.c in Sources */,
				04BD024412E6671800899322 /* SDL_audiocvt.c in Sources */,
				04BD024512E6671800899322 /* SDL_audiodev.c in Sources */,
				04BD024812E6671800899322 /* SDL_audiotypecvt.c in Sources */,
				04BD024912E6671800899322 /* SDL_mixer.c in Sources */,
				04BD025112E6671800899322 /* SDL_wave.c in Sources */,
				A704172720F09AC900A82227 /* SDL_hidapi_xbox360.c in Sources */,
				04BD025C12E6671800899322 /* SDL_cpuinfo.c in Sources */,
				04BD026312E6671800899322 /* SDL_clipboardevents.c in Sources */,
				04BD026512E6671800899322 /* SDL_events.c in Sources */,
				5C2EF6FB1FC9EE64003F5197 /* SDL_egl.c in Sources */,
				AA41F88014B8F1F500993C4F /* SDL_dropevents.c in Sources */,
				04BD026712E6671800899322 /* SDL_gesture.c in Sources */,
				04BD026912E6671800899322 /* SDL_keyboard.c in Sources */,
				04BD026B12E6671800899322 /* SDL_mouse.c in Sources */,
				04BD026D12E6671800899322 /* SDL_quit.c in Sources */,
				04BD026F12E6671800899322 /* SDL_touch.c in Sources */,
				04BD027112E6671800899322 /* SDL_windowevents.c in Sources */,
				04BD027412E6671800899322 /* SDL_rwopsbundlesupport.m in Sources */,
				04BD027512E6671800899322 /* SDL_rwops.c in Sources */,
				04BD027612E6671800899322 /* SDL_syshaptic.c in Sources */,
				04BD027A12E6671800899322 /* SDL_haptic.c in Sources */,
				04BD028112E6671800899322 /* SDL_sysjoystick.c in Sources */,
				BBFC088D164C6647003E6A99 /* SDL_gamecontroller.c in Sources */,
				04BD028B12E6671800899322 /* SDL_joystick.c in Sources */,
				04BD02A312E6671800899322 /* SDL_sysloadso.c in Sources */,
				04BD02AE12E6671800899322 /* SDL_syspower.c in Sources */,
				04BD02B012E6671800899322 /* SDL_power.c in Sources */,
				04BD02B612E6671800899322 /* SDL_assert.c in Sources */,
				04BD02B912E6671800899322 /* SDL_error.c in Sources */,
				04BD02BC12E6671800899322 /* SDL.c in Sources */,
				04BD02BD12E6671800899322 /* SDL_getenv.c in Sources */,
				04BD02BE12E6671800899322 /* SDL_iconv.c in Sources */,
				04BD02BF12E6671800899322 /* SDL_malloc.c in Sources */,
				04BD02C012E6671800899322 /* SDL_qsort.c in Sources */,
				04BD02C112E6671800899322 /* SDL_stdlib.c in Sources */,
				04BD02C212E6671800899322 /* SDL_string.c in Sources */,
				562D3C7C1D8F4933003FEEE6 /* SDL_coreaudio.m in Sources */,
				04BD02D712E6671800899322 /* SDL_syscond.c in Sources */,
				04BD02D812E6671800899322 /* SDL_sysmutex.c in Sources */,
				04BD02DA12E6671800899322 /* SDL_syssem.c in Sources */,
				04BD02DB12E6671800899322 /* SDL_systhread.c in Sources */,
				04BD02E412E6671800899322 /* SDL_thread.c in Sources */,
				04BD02F112E6671800899322 /* SDL_timer.c in Sources */,
				04BD02F312E6671800899322 /* SDL_systimer.c in Sources */,
				A704171B20F09AC900A82227 /* SDL_hidapi_switch.c in Sources */,
				04BD030E12E6671800899322 /* SDL_cocoaclipboard.m in Sources */,
				04BD031012E6671800899322 /* SDL_cocoaevents.m in Sources */,
				04BD031212E6671800899322 /* SDL_cocoakeyboard.m in Sources */,
				04BD031412E6671800899322 /* SDL_cocoamodes.m in Sources */,
				04BD031612E6671800899322 /* SDL_cocoamouse.m in Sources */,
				5C2EF6A31FC98B38003F5197 /* SDL_yuv.c in Sources */,
				5C2EF6F11FC9D181003F5197 /* SDL_cocoaopengles.m in Sources */,
				04BD031812E6671800899322 /* SDL_cocoaopengl.m in Sources */,
				A704172420F09AC900A82227 /* SDL_hidapi_xboxone.c in Sources */,
				04BD031A12E6671800899322 /* SDL_cocoashape.m in Sources */,
				04BD031C12E6671800899322 /* SDL_cocoavideo.m in Sources */,
				04BD031E12E6671800899322 /* SDL_cocoawindow.m in Sources */,
				04BD033112E6671800899322 /* SDL_nullevents.c in Sources */,
				04BD033512E6671800899322 /* SDL_nullvideo.c in Sources */,
				5C2EF6A51FC98B6B003F5197 /* yuv_rgb.c in Sources */,
				04BD038F12E6671800899322 /* SDL_blit.c in Sources */,
				04BD039112E6671800899322 /* SDL_blit_0.c in Sources */,
				A704172120F09AC900A82227 /* SDL_hidapi_ps4.c in Sources */,
				A704170A20F09A9800A82227 /* hid.c in Sources */,
				04BD039212E6671800899322 /* SDL_blit_1.c in Sources */,
				04BD039312E6671800899322 /* SDL_blit_A.c in Sources */,
				04BD039412E6671800899322 /* SDL_blit_auto.c in Sources */,
				04BD039612E6671800899322 /* SDL_blit_copy.c in Sources */,
				04BD039812E6671800899322 /* SDL_blit_N.c in Sources */,
				04BD039912E6671800899322 /* SDL_blit_slow.c in Sources */,
				04BD039B12E6671800899322 /* SDL_bmp.c in Sources */,
				04BD039C12E6671800899322 /* SDL_clipboard.c in Sources */,
				A704171520F09AC900A82227 /* SDL_hidapijoystick.c in Sources */,
				04BD03A112E6671800899322 /* SDL_fillrect.c in Sources */,
				04BD03A612E6671800899322 /* SDL_pixels.c in Sources */,
				04BD03A812E6671800899322 /* SDL_rect.c in Sources */,
				04BD03B012E6671800899322 /* SDL_RLEaccel.c in Sources */,
				04BD03B212E6671800899322 /* SDL_shape.c in Sources */,
				04BD03B412E6671800899322 /* SDL_stretch.c in Sources */,
				04BD03B512E6671800899322 /* SDL_surface.c in Sources */,
				04BD03B712E6671800899322 /* SDL_video.c in Sources */,
				04BD03F312E6671800899322 /* imKStoUCS.c in Sources */,
				5C2EF6A81FC98D2D003F5197 /* SDL_render_gles2.c in Sources */,
				04BD03F512E6671800899322 /* SDL_x11clipboard.c in Sources */,
				04BD03F712E6671800899322 /* SDL_x11dyn.c in Sources */,
				04BD03F912E6671800899322 /* SDL_x11events.c in Sources */,
				04BD03FD12E6671800899322 /* SDL_x11keyboard.c in Sources */,
				04BD03FF12E6671800899322 /* SDL_x11modes.c in Sources */,
				04BD040112E6671800899322 /* SDL_x11mouse.c in Sources */,
				04BD040312E6671800899322 /* SDL_x11opengl.c in Sources */,
				04BD040512E6671800899322 /* SDL_x11opengles.c in Sources */,
				04BD040912E6671800899322 /* SDL_x11shape.c in Sources */,
				04BD040C12E6671800899322 /* SDL_x11touch.c in Sources */,
				04BD040E12E6671800899322 /* SDL_x11video.c in Sources */,
				04BD041012E6671800899322 /* SDL_x11window.c in Sources */,
				AADC5A451FDA047900960936 /* SDL_render_metal.m in Sources */,
				041B2CAB12FA0D680087D585 /* SDL_render.c in Sources */,
				04409B9812FA97ED00FB9AA8 /* SDL_yuv_sw.c in Sources */,
				F30D9C94212CABDC0047DF2E /* SDL_dummysensor.c in Sources */,
				04F7803C12FB748500FC43C0 /* SDL_nullframebuffer.c in Sources */,
				04F7805512FB74A200FC43C0 /* SDL_blendfillrect.c in Sources */,
				04F7805712FB74A200FC43C0 /* SDL_blendline.c in Sources */,
				04F7805912FB74A200FC43C0 /* SDL_blendpoint.c in Sources */,
				F30D9CD1212EB4810047DF2E /* SDL_displayevents.c in Sources */,
				04F7805C12FB74A200FC43C0 /* SDL_drawline.c in Sources */,
				04F7805E12FB74A200FC43C0 /* SDL_drawpoint.c in Sources */,
				0442EC1912FE1BBA004C9285 /* SDL_render_gl.c in Sources */,
				0442EC1F12FE1BCB004C9285 /* SDL_render_sw.c in Sources */,
				F30D9C8B212BC94F0047DF2E /* SDL_sensor.c in Sources */,
				56A67025185654B40007D20F /* SDL_dynapi.c in Sources */,
				0442EC5C12FE1C60004C9285 /* SDL_x11framebuffer.c in Sources */,
				0442EC6012FE1C75004C9285 /* SDL_hints.c in Sources */,
				04BAC0C91300C2160055DE28 /* SDL_log.c in Sources */,
				043567401303160F00BA5428 /* SDL_shaders_gl.c in Sources */,
				AA628ACB159367B7005138DD /* SDL_rotate.c in Sources */,
				AA628AD2159367F2005138DD /* SDL_x11xinput2.c in Sources */,
				AA9E4094163BE51E007A2AD0 /* SDL_x11messagebox.c in Sources */,
				AABCC390164063D200AB8930 /* SDL_cocoamessagebox.m in Sources */,
				AA0F8492178D5ECC00823F9D /* SDL_systls.c in Sources */,
				D55A1B84179F263600625D7C /* SDL_cocoamousetap.m in Sources */,
				DB0F490817CA5292008798C5 /* SDL_sysfilesystem.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB313FFD17554B71006C0E22 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				4D16645A1EDD6235003DE88E /* SDL_cocoametalview.m in Sources */,
				4D16645B1EDD6235003DE88E /* SDL_cocoavulkan.m in Sources */,
				5C2EF6AD1FC98D2E003F5197 /* SDL_shaders_gles2.c in Sources */,
				4D1664591EDD621B003DE88E /* SDL_vulkan_utils.c in Sources */,
				DB313FFE17554B71006C0E22 /* SDL_atomic.c in Sources */,
				DB313FFF17554B71006C0E22 /* SDL_spinlock.c in Sources */,
				56F9D55D1DF73B6C00C15B5D /* SDL_dataqueue.c in Sources */,
				DB31400017554B71006C0E22 /* SDL_diskaudio.c in Sources */,
				DB31400117554B71006C0E22 /* SDL_dummyaudio.c in Sources */,
				DB31400317554B71006C0E22 /* SDL_audio.c in Sources */,
				DB31400417554B71006C0E22 /* SDL_audiocvt.c in Sources */,
				DB31400517554B71006C0E22 /* SDL_audiodev.c in Sources */,
				DB31400617554B71006C0E22 /* SDL_audiotypecvt.c in Sources */,
				DB31400717554B71006C0E22 /* SDL_mixer.c in Sources */,
				DB31400817554B71006C0E22 /* SDL_wave.c in Sources */,
				A704172820F09AC900A82227 /* SDL_hidapi_xbox360.c in Sources */,
				DB31400917554B71006C0E22 /* SDL_cpuinfo.c in Sources */,
				DB31400A17554B71006C0E22 /* SDL_clipboardevents.c in Sources */,
				DB31400B17554B71006C0E22 /* SDL_events.c in Sources */,
				5C2EF6FE1FC9EE65003F5197 /* SDL_egl.c in Sources */,
				DB31400C17554B71006C0E22 /* SDL_dropevents.c in Sources */,
				DB31400D17554B71006C0E22 /* SDL_gesture.c in Sources */,
				DB31400E17554B71006C0E22 /* SDL_keyboard.c in Sources */,
				DB31400F17554B71006C0E22 /* SDL_mouse.c in Sources */,
				DB31401017554B71006C0E22 /* SDL_quit.c in Sources */,
				DB31401117554B71006C0E22 /* SDL_touch.c in Sources */,
				DB31401217554B71006C0E22 /* SDL_windowevents.c in Sources */,
				DB31401317554B71006C0E22 /* SDL_rwopsbundlesupport.m in Sources */,
				DB31401417554B71006C0E22 /* SDL_rwops.c in Sources */,
				DB31401517554B71006C0E22 /* SDL_syshaptic.c in Sources */,
				DB31401617554B71006C0E22 /* SDL_haptic.c in Sources */,
				DB31401717554B71006C0E22 /* SDL_sysjoystick.c in Sources */,
				DB31401817554B71006C0E22 /* SDL_gamecontroller.c in Sources */,
				DB31401917554B71006C0E22 /* SDL_joystick.c in Sources */,
				DB31401A17554B71006C0E22 /* SDL_sysloadso.c in Sources */,
				DB31401B17554B71006C0E22 /* SDL_syspower.c in Sources */,
				DB31401C17554B71006C0E22 /* SDL_power.c in Sources */,
				DB31401D17554B71006C0E22 /* SDL_assert.c in Sources */,
				DB31401E17554B71006C0E22 /* SDL_error.c in Sources */,
				DB31402017554B71006C0E22 /* SDL.c in Sources */,
				DB31402117554B71006C0E22 /* SDL_getenv.c in Sources */,
				DB31402217554B71006C0E22 /* SDL_iconv.c in Sources */,
				DB31402317554B71006C0E22 /* SDL_malloc.c in Sources */,
				DB31402417554B71006C0E22 /* SDL_qsort.c in Sources */,
				DB31402517554B71006C0E22 /* SDL_stdlib.c in Sources */,
				DB31402617554B71006C0E22 /* SDL_string.c in Sources */,
				562D3C7D1D8F4933003FEEE6 /* SDL_coreaudio.m in Sources */,
				DB31402717554B71006C0E22 /* SDL_syscond.c in Sources */,
				DB31402817554B71006C0E22 /* SDL_sysmutex.c in Sources */,
				DB31402917554B71006C0E22 /* SDL_syssem.c in Sources */,
				DB31402A17554B71006C0E22 /* SDL_systhread.c in Sources */,
				DB31402B17554B71006C0E22 /* SDL_thread.c in Sources */,
				DB31402C17554B71006C0E22 /* SDL_timer.c in Sources */,
				DB31402D17554B71006C0E22 /* SDL_systimer.c in Sources */,
				A704171C20F09AC900A82227 /* SDL_hidapi_switch.c in Sources */,
				DB31402E17554B71006C0E22 /* SDL_cocoaclipboard.m in Sources */,
				DB31402F17554B71006C0E22 /* SDL_cocoaevents.m in Sources */,
				DB31403017554B71006C0E22 /* SDL_cocoakeyboard.m in Sources */,
				DB31403117554B71006C0E22 /* SDL_cocoamodes.m in Sources */,
				DB31403217554B71006C0E22 /* SDL_cocoamouse.m in Sources */,
				5C2EF6A41FC98B39003F5197 /* SDL_yuv.c in Sources */,
				5C2EF6F31FC9D182003F5197 /* SDL_cocoaopengles.m in Sources */,
				DB31403317554B71006C0E22 /* SDL_cocoaopengl.m in Sources */,
				A704172520F09AC900A82227 /* SDL_hidapi_xboxone.c in Sources */,
				DB31403417554B71006C0E22 /* SDL_cocoashape.m in Sources */,
				DB31403517554B71006C0E22 /* SDL_cocoavideo.m in Sources */,
				DB31403617554B71006C0E22 /* SDL_cocoawindow.m in Sources */,
				DB31403717554B71006C0E22 /* SDL_nullevents.c in Sources */,
				DB31403817554B71006C0E22 /* SDL_nullvideo.c in Sources */,
				5C2EF6A61FC98B6C003F5197 /* yuv_rgb.c in Sources */,
				DB31403917554B71006C0E22 /* SDL_blit.c in Sources */,
				DB31403A17554B71006C0E22 /* SDL_blit_0.c in Sources */,
				A704172220F09AC900A82227 /* SDL_hidapi_ps4.c in Sources */,
				A704170B20F09A9800A82227 /* hid.c in Sources */,
				DB31403B17554B71006C0E22 /* SDL_blit_1.c in Sources */,
				DB31403C17554B71006C0E22 /* SDL_blit_A.c in Sources */,
				DB31403D17554B71006C0E22 /* SDL_blit_auto.c in Sources */,
				DB31403E17554B71006C0E22 /* SDL_blit_copy.c in Sources */,
				DB31403F17554B71006C0E22 /* SDL_blit_N.c in Sources */,
				DB31404017554B71006C0E22 /* SDL_blit_slow.c in Sources */,
				DB31404117554B71006C0E22 /* SDL_bmp.c in Sources */,
				DB31404217554B71006C0E22 /* SDL_clipboard.c in Sources */,
				A704171620F09AC900A82227 /* SDL_hidapijoystick.c in Sources */,
				DB31404317554B71006C0E22 /* SDL_fillrect.c in Sources */,
				DB31404417554B71006C0E22 /* SDL_pixels.c in Sources */,
				DB31404517554B71006C0E22 /* SDL_rect.c in Sources */,
				DB31404617554B71006C0E22 /* SDL_RLEaccel.c in Sources */,
				DB31404717554B71006C0E22 /* SDL_shape.c in Sources */,
				DB31404817554B71006C0E22 /* SDL_stretch.c in Sources */,
				DB31404917554B71006C0E22 /* SDL_surface.c in Sources */,
				DB31404A17554B71006C0E22 /* SDL_video.c in Sources */,
				DB31404B17554B71006C0E22 /* imKStoUCS.c in Sources */,
				5C2EF6AC1FC98D2E003F5197 /* SDL_render_gles2.c in Sources */,
				DB31404C17554B71006C0E22 /* SDL_x11clipboard.c in Sources */,
				DB31404D17554B71006C0E22 /* SDL_x11dyn.c in Sources */,
				DB31404E17554B71006C0E22 /* SDL_x11events.c in Sources */,
				DB31404F17554B71006C0E22 /* SDL_x11keyboard.c in Sources */,
				DB31405017554B71006C0E22 /* SDL_x11modes.c in Sources */,
				DB31405117554B71006C0E22 /* SDL_x11mouse.c in Sources */,
				DB31405217554B71006C0E22 /* SDL_x11opengl.c in Sources */,
				DB31405317554B71006C0E22 /* SDL_x11opengles.c in Sources */,
				DB31405417554B71006C0E22 /* SDL_x11shape.c in Sources */,
				DB31405517554B71006C0E22 /* SDL_x11touch.c in Sources */,
				DB31405617554B71006C0E22 /* SDL_x11video.c in Sources */,
				DB31405717554B71006C0E22 /* SDL_x11window.c in Sources */,
				AADC5A481FDA048100960936 /* SDL_render_metal.m in Sources */,
				DB31405817554B71006C0E22 /* SDL_render.c in Sources */,
				DB31405A17554B71006C0E22 /* SDL_yuv_sw.c in Sources */,
				F30D9C95212CABDC0047DF2E /* SDL_dummysensor.c in Sources */,
				DB31405B17554B71006C0E22 /* SDL_nullframebuffer.c in Sources */,
				DB31405C17554B71006C0E22 /* SDL_blendfillrect.c in Sources */,
				DB31405D17554B71006C0E22 /* SDL_blendline.c in Sources */,
				DB31405E17554B71006C0E22 /* SDL_blendpoint.c in Sources */,
				F30D9CD2212EB4810047DF2E /* SDL_displayevents.c in Sources */,
				DB31405F17554B71006C0E22 /* SDL_drawline.c in Sources */,
				DB31406017554B71006C0E22 /* SDL_drawpoint.c in Sources */,
				DB31406117554B71006C0E22 /* SDL_render_gl.c in Sources */,
				DB31406217554B71006C0E22 /* SDL_render_sw.c in Sources */,
				F30D9C8C212BC94F0047DF2E /* SDL_sensor.c in Sources */,
				56A67026185654B40007D20F /* SDL_dynapi.c in Sources */,
				DB31406317554B71006C0E22 /* SDL_x11framebuffer.c in Sources */,
				DB31406417554B71006C0E22 /* SDL_hints.c in Sources */,
				DB31406517554B71006C0E22 /* SDL_log.c in Sources */,
				DB31406617554B71006C0E22 /* SDL_shaders_gl.c in Sources */,
				DB31406717554B71006C0E22 /* SDL_rotate.c in Sources */,
				DB31406817554B71006C0E22 /* SDL_x11xinput2.c in Sources */,
				DB31406917554B71006C0E22 /* SDL_x11messagebox.c in Sources */,
				DB31406A17554B71006C0E22 /* SDL_cocoamessagebox.m in Sources */,
				AA0F8493178D5ECC00823F9D /* SDL_systls.c in Sources */,
				D55A1B83179F263500625D7C /* SDL_cocoamousetap.m in Sources */,
				DB0F490A17CA5293008798C5 /* SDL_sysfilesystem.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		BECDF6C60761BA81005FE872 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BECDF5FE0761BA81005FE872 /* Framework */;
			targetProxy = BECDF6C50761BA81005FE872 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		00CFA621106A567900758660 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				DEPLOYMENT_POSTPROCESSING = YES;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_ALTIVEC_EXTENSIONS = YES;
				GCC_AUTO_VECTORIZATION = YES;
				GCC_ENABLE_SSE3_EXTENSIONS = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 3;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					/usr/X11R6/include,
					"$(VULKAN_SDK)/include",
					../../src/video/khronos,
					../../src/hidapi/hidapi,
				);
				MACOSX_DEPLOYMENT_TARGET = 10.6;
				SDKROOT = macosx;
				STRIP_STYLE = "non-global";
			};
			name = Release;
		};
		00CFA622106A567900758660 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_32_64_BIT)";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				DYLIB_COMPATIBILITY_VERSION = 1.0.0;
				DYLIB_CURRENT_VERSION = 10.0.0;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				FRAMEWORK_VERSION = A;
				INFOPLIST_FILE = "Info-Framework.plist";
				INSTALL_PATH = "@rpath";
				OTHER_LDFLAGS = "-liconv";
				PRODUCT_BUNDLE_IDENTIFIER = org.libsdl.SDL2;
				PRODUCT_NAME = SDL2;
				PROVISIONING_PROFILE = "";
				WRAPPER_EXTENSION = framework;
			};
			name = Release;
		};
		00CFA623106A567900758660 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(GCC_PREPROCESSOR_DEFINITIONS)",
					"$(GCC_PREPROCESSOR_DEFINITIONS_QUOTED_1)",
					"$(GCC_PREPROCESSOR_DEFINITIONS_QUOTED_2)",
					"$(GCC_PREPROCESSOR_DEFINITIONS_QUOTED_3)",
					"$(GCC_PREPROCESSOR_DEFINITIONS_QUOTED_4)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				PRODUCT_NAME = SDL2;
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
		00CFA625106A567900758660 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = "Standard DMG";
				PROVISIONING_PROFILE = "";
			};
			name = Release;
		};
		00CFA627106A568900758660 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_ALTIVEC_EXTENSIONS = YES;
				GCC_AUTO_VECTORIZATION = YES;
				GCC_ENABLE_SSE3_EXTENSIONS = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					/usr/X11R6/include,
					"$(VULKAN_SDK)/include",
					../../src/video/khronos,
					../../src/hidapi/hidapi,
				);
				MACOSX_DEPLOYMENT_TARGET = 10.6;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				STRIP_INSTALLED_PRODUCT = NO;
			};
			name = Debug;
		};
		00CFA628106A568900758660 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_32_64_BIT)";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				DYLIB_COMPATIBILITY_VERSION = 1.0.0;
				DYLIB_CURRENT_VERSION = 10.0.0;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				FRAMEWORK_VERSION = A;
				INFOPLIST_FILE = "Info-Framework.plist";
				INSTALL_PATH = "@rpath";
				OTHER_LDFLAGS = "-liconv";
				PRODUCT_BUNDLE_IDENTIFIER = org.libsdl.SDL2;
				PRODUCT_NAME = SDL2;
				PROVISIONING_PROFILE = "";
				WRAPPER_EXTENSION = framework;
			};
			name = Debug;
		};
		00CFA629106A568900758660 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(GCC_PREPROCESSOR_DEFINITIONS)",
					"$(GCC_PREPROCESSOR_DEFINITIONS_QUOTED_1)",
					"$(GCC_PREPROCESSOR_DEFINITIONS_QUOTED_2)",
					"$(GCC_PREPROCESSOR_DEFINITIONS_QUOTED_3)",
					"$(GCC_PREPROCESSOR_DEFINITIONS_QUOTED_4)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				PRODUCT_NAME = SDL2;
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		00CFA62B106A568900758660 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = "Standard DMG";
				PROVISIONING_PROFILE = "";
			};
			name = Debug;
		};
		DB31407517554B71006C0E22 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				EXECUTABLE_PREFIX = lib;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(GCC_PREPROCESSOR_DEFINITIONS)",
					"$(GCC_PREPROCESSOR_DEFINITIONS_QUOTED_1)",
					"$(GCC_PREPROCESSOR_DEFINITIONS_QUOTED_2)",
					"$(GCC_PREPROCESSOR_DEFINITIONS_QUOTED_3)",
					"$(GCC_PREPROCESSOR_DEFINITIONS_QUOTED_4)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				INSTALL_PATH = "@rpath";
				PRODUCT_NAME = SDL2;
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		DB31407617554B71006C0E22 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				EXECUTABLE_PREFIX = lib;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(GCC_PREPROCESSOR_DEFINITIONS)",
					"$(GCC_PREPROCESSOR_DEFINITIONS_QUOTED_1)",
					"$(GCC_PREPROCESSOR_DEFINITIONS_QUOTED_2)",
					"$(GCC_PREPROCESSOR_DEFINITIONS_QUOTED_3)",
					"$(GCC_PREPROCESSOR_DEFINITIONS_QUOTED_4)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				INSTALL_PATH = "@rpath";
				PRODUCT_NAME = SDL2;
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		0073177A0858DB0500B2BC32 /* Build configuration list for PBXNativeTarget "Framework" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00CFA628106A568900758660 /* Debug */,
				00CFA622106A567900758660 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		0073177E0858DB0500B2BC32 /* Build configuration list for PBXNativeTarget "Static Library" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00CFA629106A568900758660 /* Debug */,
				00CFA623106A567900758660 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		007317860858DB0500B2BC32 /* Build configuration list for PBXNativeTarget "Standard DMG" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00CFA62B106A568900758660 /* Debug */,
				00CFA625106A567900758660 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		0073178E0858DB0500B2BC32 /* Build configuration list for PBXProject "SDL" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00CFA627106A568900758660 /* Debug */,
				00CFA621106A567900758660 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB31407417554B71006C0E22 /* Build configuration list for PBXNativeTarget "Shared Library" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB31407517554B71006C0E22 /* Debug */,
				DB31407617554B71006C0E22 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = 0867D690FE84028FC02AAC07 /* Project object */;
}
