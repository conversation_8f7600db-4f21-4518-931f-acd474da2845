// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXAggregateTarget section */
		BEC566920761D90300A33029 /* All */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 001B599808BDB826006539E9 /* Build configuration list for PBXAggregateTarget "All" */;
			buildPhases = (
			);
			dependencies = (
				DB0F490517CA5249008798C5 /* PBXTargetDependency */,
				DB0F490717CA5249008798C5 /* PBXTargetDependency */,
				DB166E9816A1D7CF00A1396C /* PBXTargetDependency */,
				DB166E9616A1D7CD00A1396C /* PBXTargetDependency */,
				DB166E6C16A1D72000A1396C /* PBXTargetDependency */,
				DB166E5616A1D6B800A1396C /* PBXTargetDependency */,
				DB166E3B16A1D65A00A1396C /* PBXTargetDependency */,
				DB166E2016A1D5D000A1396C /* PBXTargetDependency */,
				DB166E0916A1D5A400A1396C /* PBXTargetDependency */,
				DB166DF216A1D53700A1396C /* PBXTargetDependency */,
				DB166DD916A1D38900A1396C /* PBXTargetDependency */,
				001799481074403E00F5D044 /* PBXTargetDependency */,
				0017994C1074403E00F5D044 /* PBXTargetDependency */,
				001799501074403E00F5D044 /* PBXTargetDependency */,
				001799521074403E00F5D044 /* PBXTargetDependency */,
				0017995A1074403E00F5D044 /* PBXTargetDependency */,
				0017995E1074403E00F5D044 /* PBXTargetDependency */,
				001799601074403E00F5D044 /* PBXTargetDependency */,
				001799661074403E00F5D044 /* PBXTargetDependency */,
				001799681074403E00F5D044 /* PBXTargetDependency */,
				0017996A1074403E00F5D044 /* PBXTargetDependency */,
				0017996C1074403E00F5D044 /* PBXTargetDependency */,
				0017996E1074403E00F5D044 /* PBXTargetDependency */,
				001799701074403E00F5D044 /* PBXTargetDependency */,
				001799721074403E00F5D044 /* PBXTargetDependency */,
				001799741074403E00F5D044 /* PBXTargetDependency */,
				001799761074403E00F5D044 /* PBXTargetDependency */,
				001799781074403E00F5D044 /* PBXTargetDependency */,
				0017997C1074403E00F5D044 /* PBXTargetDependency */,
				001799801074403E00F5D044 /* PBXTargetDependency */,
				001799841074403E00F5D044 /* PBXTargetDependency */,
				001799881074403E00F5D044 /* PBXTargetDependency */,
				0017998A1074403E00F5D044 /* PBXTargetDependency */,
				0017998C1074403E00F5D044 /* PBXTargetDependency */,
				0017998E1074403E00F5D044 /* PBXTargetDependency */,
				001799921074403E00F5D044 /* PBXTargetDependency */,
				001799941074403E00F5D044 /* PBXTargetDependency */,
				001799961074403E00F5D044 /* PBXTargetDependency */,
				0017999E1074403E00F5D044 /* PBXTargetDependency */,
				001799A21074403E00F5D044 /* PBXTargetDependency */,
				DB166D7016A1CEAF00A1396C /* PBXTargetDependency */,
				DB166D6E16A1CEAA00A1396C /* PBXTargetDependency */,
				DB166DC316A1D32C00A1396C /* PBXTargetDependency */,
			);
			name = All;
			productName = "Build All";
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		001794D01073667700F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		001794D11073667B00F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		001794D41073668800F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		001794D51073668D00F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		001794D61073669200F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		001794D71073669700F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		001794D91073669E00F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		001794DB107366A700F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		001794DC107366AC00F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		001794DE107366B900F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		001794DF107366BD00F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		001794E0107366C100F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		001794E5107366D900F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		0017957C10741F7900F5D044 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		0017957D10741F7900F5D044 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		0017957E10741F7900F5D044 /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		0017957F10741F7900F5D044 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		0017958010741F7900F5D044 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		0017958110741F7900F5D044 /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		0017958310741F7900F5D044 /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		0017958410741F7900F5D044 /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		0017958510741F7900F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		001795901074216E00F5D044 /* testatomic.c in Sources */ = {isa = PBXBuildFile; fileRef = 0017958F1074216E00F5D044 /* testatomic.c */; };
		0017959D107421BF00F5D044 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		0017959E107421BF00F5D044 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		0017959F107421BF00F5D044 /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		001795A0107421BF00F5D044 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		001795A1107421BF00F5D044 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		001795A2107421BF00F5D044 /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		001795A4107421BF00F5D044 /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		001795A5107421BF00F5D044 /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		001795A6107421BF00F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		001795B11074222D00F5D044 /* testaudioinfo.c in Sources */ = {isa = PBXBuildFile; fileRef = 001795B01074222D00F5D044 /* testaudioinfo.c */; };
		0017971110742F3200F5D044 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		0017971210742F3200F5D044 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		0017971310742F3200F5D044 /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		0017971410742F3200F5D044 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		0017971510742F3200F5D044 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		0017971610742F3200F5D044 /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		0017971810742F3200F5D044 /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		0017971910742F3200F5D044 /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		0017971A10742F3200F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		0017972810742FB900F5D044 /* testgl2.c in Sources */ = {isa = PBXBuildFile; fileRef = 0017972710742FB900F5D044 /* testgl2.c */; };
		00179738107430D600F5D044 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		00179739107430D600F5D044 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		0017973A107430D600F5D044 /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		0017973B107430D600F5D044 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		0017973C107430D600F5D044 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		0017973D107430D600F5D044 /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		0017973F107430D600F5D044 /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		00179740107430D600F5D044 /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		00179741107430D600F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		0017974F1074315700F5D044 /* testhaptic.c in Sources */ = {isa = PBXBuildFile; fileRef = 0017974E1074315700F5D044 /* testhaptic.c */; };
		0017975E107431B300F5D044 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		0017975F107431B300F5D044 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		00179760107431B300F5D044 /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		00179761107431B300F5D044 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		00179762107431B300F5D044 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		00179763107431B300F5D044 /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		00179765107431B300F5D044 /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		00179766107431B300F5D044 /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		00179767107431B300F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		001797721074320D00F5D044 /* testdraw2.c in Sources */ = {isa = PBXBuildFile; fileRef = 001797711074320D00F5D044 /* testdraw2.c */; };
		0017977E107432AE00F5D044 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		0017977F107432AE00F5D044 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		00179780107432AE00F5D044 /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		00179781107432AE00F5D044 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		00179782107432AE00F5D044 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		00179783107432AE00F5D044 /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		00179785107432AE00F5D044 /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		00179786107432AE00F5D044 /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		00179787107432AE00F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		00179792107432FA00F5D044 /* testime.c in Sources */ = {isa = PBXBuildFile; fileRef = 00179791107432FA00F5D044 /* testime.c */; };
		0017979E1074334C00F5D044 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		0017979F1074334C00F5D044 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		001797A01074334C00F5D044 /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		001797A11074334C00F5D044 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		001797A21074334C00F5D044 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		001797A31074334C00F5D044 /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		001797A51074334C00F5D044 /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		001797A61074334C00F5D044 /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		001797A71074334C00F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		001797B41074339C00F5D044 /* testintersections.c in Sources */ = {isa = PBXBuildFile; fileRef = 001797B31074339C00F5D044 /* testintersections.c */; };
		001797C0107433C600F5D044 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		001797C1107433C600F5D044 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		001797C2107433C600F5D044 /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		001797C3107433C600F5D044 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		001797C4107433C600F5D044 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		001797C5107433C600F5D044 /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		001797C7107433C600F5D044 /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		001797C8107433C600F5D044 /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		001797C9107433C600F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		001797D41074343E00F5D044 /* testloadso.c in Sources */ = {isa = PBXBuildFile; fileRef = 001797D31074343E00F5D044 /* testloadso.c */; };
		001798021074355200F5D044 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		001798031074355200F5D044 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		001798041074355200F5D044 /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		001798051074355200F5D044 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		001798061074355200F5D044 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		001798071074355200F5D044 /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		001798091074355200F5D044 /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		0017980A1074355200F5D044 /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		0017980B1074355200F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		001798161074359B00F5D044 /* testmultiaudio.c in Sources */ = {isa = PBXBuildFile; fileRef = 001798151074359B00F5D044 /* testmultiaudio.c */; };
		0017987F1074392D00F5D044 /* testnative.c in Sources */ = {isa = PBXBuildFile; fileRef = 0017985A107436ED00F5D044 /* testnative.c */; };
		001798801074392D00F5D044 /* testnativecocoa.m in Sources */ = {isa = PBXBuildFile; fileRef = 0017985C107436ED00F5D044 /* testnativecocoa.m */; };
		001798811074392D00F5D044 /* testnativex11.c in Sources */ = {isa = PBXBuildFile; fileRef = 00179872107438D000F5D044 /* testnativex11.c */; };
		001798841074392D00F5D044 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		001798851074392D00F5D044 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		001798861074392D00F5D044 /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		001798871074392D00F5D044 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		001798881074392D00F5D044 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		001798891074392D00F5D044 /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		0017988B1074392D00F5D044 /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		0017988C1074392D00F5D044 /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		0017988D1074392D00F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		001798A5107439DF00F5D044 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		001798A6107439DF00F5D044 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		001798A7107439DF00F5D044 /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		001798A8107439DF00F5D044 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		001798A9107439DF00F5D044 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		001798AA107439DF00F5D044 /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		001798AC107439DF00F5D044 /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		001798AD107439DF00F5D044 /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		001798AE107439DF00F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		001798BA10743A4900F5D044 /* testpower.c in Sources */ = {isa = PBXBuildFile; fileRef = 001798B910743A4900F5D044 /* testpower.c */; };
		001798E210743BEC00F5D044 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		001798E310743BEC00F5D044 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		001798E410743BEC00F5D044 /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		001798E510743BEC00F5D044 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		001798E610743BEC00F5D044 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		001798E710743BEC00F5D044 /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		001798E910743BEC00F5D044 /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		001798EA10743BEC00F5D044 /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		001798EB10743BEC00F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		001798FA10743E9200F5D044 /* testresample.c in Sources */ = {isa = PBXBuildFile; fileRef = 001798F910743E9200F5D044 /* testresample.c */; };
		0017990610743F1000F5D044 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		0017990710743F1000F5D044 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		0017990810743F1000F5D044 /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		0017990910743F1000F5D044 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		0017990A10743F1000F5D044 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		0017990B10743F1000F5D044 /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		0017990D10743F1000F5D044 /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		0017990E10743F1000F5D044 /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		0017990F10743F1000F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		0017991A10743F5300F5D044 /* testsprite2.c in Sources */ = {isa = PBXBuildFile; fileRef = 0017991910743F5300F5D044 /* testsprite2.c */; };
		0017992810743FB700F5D044 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		0017992910743FB700F5D044 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		0017992A10743FB700F5D044 /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		0017992B10743FB700F5D044 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		0017992C10743FB700F5D044 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		0017992D10743FB700F5D044 /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		0017992F10743FB700F5D044 /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		0017993010743FB700F5D044 /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		0017993110743FB700F5D044 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		0017993C10743FEF00F5D044 /* testwm2.c in Sources */ = {isa = PBXBuildFile; fileRef = 0017993B10743FEF00F5D044 /* testwm2.c */; };
		002A863010730405007319AE /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		002A864110730546007319AE /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		002A864210730546007319AE /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		002A864310730546007319AE /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		002A864D10730546007319AE /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		002A864E10730546007319AE /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		002A864F10730546007319AE /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		002A865310730547007319AE /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		002A865410730547007319AE /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		002A865510730547007319AE /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		002A866210730547007319AE /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		002A866310730547007319AE /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		002A866410730547007319AE /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		002A866B10730548007319AE /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		002A866C10730548007319AE /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		002A866D10730548007319AE /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		002A866E10730548007319AE /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		002A866F10730548007319AE /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		002A867010730548007319AE /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		002A867410730548007319AE /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		002A867510730548007319AE /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		002A867610730548007319AE /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		002A867710730548007319AE /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		002A867810730548007319AE /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		002A867910730549007319AE /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		002A867A10730549007319AE /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		002A867B10730549007319AE /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		002A867C10730549007319AE /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		002A868010730549007319AE /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		002A868110730549007319AE /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		002A868210730549007319AE /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		002A868610730549007319AE /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		002A868710730549007319AE /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		002A868810730549007319AE /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		002A868910730549007319AE /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		002A868A10730549007319AE /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		002A868B1073054A007319AE /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		002A868F1073054A007319AE /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		002A86901073054A007319AE /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		002A86911073054A007319AE /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		002A86951073054A007319AE /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		002A86961073054A007319AE /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		002A86971073054A007319AE /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		002A86981073054A007319AE /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		002A86991073054A007319AE /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		002A869A1073054A007319AE /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		002A86A310730593007319AE /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		002A86A410730593007319AE /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		002A86AB10730594007319AE /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		002A86AC10730594007319AE /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		002A86AF10730594007319AE /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		002A86B010730594007319AE /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		002A86B910730594007319AE /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		002A86BA10730594007319AE /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		002A86BF10730595007319AE /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		002A86C010730595007319AE /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		002A86C110730595007319AE /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		002A86C210730595007319AE /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		002A86C510730595007319AE /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		002A86C610730595007319AE /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		002A86C710730595007319AE /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		002A86C810730595007319AE /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		002A86C910730595007319AE /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		002A86CA10730595007319AE /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		002A86CD10730595007319AE /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		002A86CE10730596007319AE /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		002A86D110730596007319AE /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		002A86D210730596007319AE /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		002A86D310730596007319AE /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		002A86D410730596007319AE /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		002A86D710730596007319AE /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		002A86D810730596007319AE /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		002A86DB10730596007319AE /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		002A86DC10730596007319AE /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		002A86DD10730596007319AE /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		002A86DE10730596007319AE /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		002A871610730623007319AE /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		002A871A10730623007319AE /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		002A871C10730623007319AE /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		002A872110730624007319AE /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		002A872410730624007319AE /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		002A872510730624007319AE /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		002A872710730624007319AE /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		002A872810730624007319AE /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		002A872910730624007319AE /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		002A872B10730624007319AE /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		002A872D10730624007319AE /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		002A872E10730624007319AE /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		002A873010730625007319AE /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		002A873210730625007319AE /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		002A873310730625007319AE /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		002A873B10730675007319AE /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		002A873F10730675007319AE /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		002A874110730676007319AE /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		002A874610730676007319AE /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		002A874910730676007319AE /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		002A874A10730676007319AE /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		002A874C10730676007319AE /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		002A874D10730677007319AE /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		002A874E10730677007319AE /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		002A875010730677007319AE /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		002A875210730677007319AE /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		002A875310730677007319AE /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		002A875510730677007319AE /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		002A875710730678007319AE /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		002A875810730678007319AE /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		002A875E10730745007319AE /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		002F33AA09CA188600EBEB88 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		002F33AF09CA188600EBEB88 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		002F33B009CA188600EBEB88 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		002F33B209CA188600EBEB88 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		002F33B509CA188600EBEB88 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		002F33B609CA188600EBEB88 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		002F33B709CA188600EBEB88 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		002F33B809CA188600EBEB88 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		002F33BC09CA188600EBEB88 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		002F33BF09CA188600EBEB88 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		002F33C109CA188600EBEB88 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		002F340B09CA1BFF00EBEB88 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		002F341809CA1C5B00EBEB88 /* testfile.c in Sources */ = {isa = PBXBuildFile; fileRef = 002F341709CA1C5B00EBEB88 /* testfile.c */; };
		002F342A09CA1F0300EBEB88 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		002F343709CA1F6F00EBEB88 /* testiconv.c in Sources */ = {isa = PBXBuildFile; fileRef = 002F343609CA1F6F00EBEB88 /* testiconv.c */; };
		002F344609CA1FB300EBEB88 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		002F345409CA202000EBEB88 /* testoverlay2.c in Sources */ = {isa = PBXBuildFile; fileRef = 002F345209CA201C00EBEB88 /* testoverlay2.c */; };
		002F346309CA204F00EBEB88 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		002F347009CA20A600EBEB88 /* testplatform.c in Sources */ = {isa = PBXBuildFile; fileRef = 002F346F09CA20A600EBEB88 /* testplatform.c */; };
		00794E6609D20865003FC8A1 /* sample.wav in CopyFiles */ = {isa = PBXBuildFile; fileRef = 00794E6209D20839003FC8A1 /* sample.wav */; };
		00794EF009D23739003FC8A1 /* utf8.txt in CopyFiles */ = {isa = PBXBuildFile; fileRef = 00794E6309D20839003FC8A1 /* utf8.txt */; };
		00794EF709D237DE003FC8A1 /* moose.dat in CopyFiles */ = {isa = PBXBuildFile; fileRef = 00794E5E09D20839003FC8A1 /* moose.dat */; };
		453774A5120915E3002F0F45 /* testshape.c in Sources */ = {isa = PBXBuildFile; fileRef = 453774A4120915E3002F0F45 /* testshape.c */; };
		66E88E5C203B733D0004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E5D203B73530004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E5E203B74490004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E5F203B74860004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E60203B74C20004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E61203B74CC0004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E62203B74D50004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E63203B74DC0004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E64203B74E50004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E65203B74EC0004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E66203B75140004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E67203B751D0004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E68203B75250004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E69203B75390004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E6A203B75450004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E6B203B754C0004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E6C203B75540004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E6D203B755B0004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E6E203B75620004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E6F203B756A0004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E70203B75710004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E71203B75780004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E72203B757F0004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E73203B758C0004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E74203B75AF0004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E75203B75B90004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E76203B75BF0004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E77203B75C70004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E78203B75CE0004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E79203B75D50004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E7A203B75DE0004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E7B203B75E40004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E7C203B75EB0004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E7D203B75F30004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E7E203B75F90004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E7F203B76000004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E80203B76060004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E81203B760D0004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E82203B76140004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E83203B761D0004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E84203B76230004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E85203B762D0004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E86203B76340004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E87203B763B0004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E88203B76420004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E89203B764A0004D44E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 66E88E5B203B733C0004D44E /* Metal.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		66E88E8B203B778F0004D44E /* testyuv_cvt.c in Sources */ = {isa = PBXBuildFile; fileRef = 66E88E8A203B778F0004D44E /* testyuv_cvt.c */; };
		AAF02FFA1F90092700B9A9FB /* SDL_test_memory.c in Sources */ = {isa = PBXBuildFile; fileRef = AAF02FF41F90089800B9A9FB /* SDL_test_memory.c */; };
		BBFC08C0164C6862003E6A99 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		BBFC08C1164C6862003E6A99 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		BBFC08C2164C6862003E6A99 /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		BBFC08C3164C6862003E6A99 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		BBFC08C4164C6862003E6A99 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		BBFC08C5164C6862003E6A99 /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		BBFC08C7164C6862003E6A99 /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		BBFC08C8164C6862003E6A99 /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		BBFC08C9164C6862003E6A99 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		BBFC08D0164C6876003E6A99 /* testgamecontroller.c in Sources */ = {isa = PBXBuildFile; fileRef = BBFC088E164C6820003E6A99 /* testgamecontroller.c */; };
		BEC566B10761D90300A33029 /* checkkeys.c in Sources */ = {isa = PBXBuildFile; fileRef = 092D6D10FFB30A2C7F000001 /* checkkeys.c */; };
		BEC566CB0761D90300A33029 /* loopwave.c in Sources */ = {isa = PBXBuildFile; fileRef = 083E4872006D84C97F000001 /* loopwave.c */; };
		BEC567010761D90300A33029 /* testerror.c in Sources */ = {isa = PBXBuildFile; fileRef = 083E4878006D85357F000001 /* testerror.c */; };
		BEC567290761D90400A33029 /* testthread.c in Sources */ = {isa = PBXBuildFile; fileRef = 092D6D58FFB311A97F000001 /* testthread.c */; };
		BEC567360761D90400A33029 /* testjoystick.c in Sources */ = {isa = PBXBuildFile; fileRef = 092D6D62FFB312AA7F000001 /* testjoystick.c */; };
		BEC567430761D90400A33029 /* testkeys.c in Sources */ = {isa = PBXBuildFile; fileRef = 092D6D6CFFB313437F000001 /* testkeys.c */; };
		BEC567500761D90400A33029 /* testlock.c in Sources */ = {isa = PBXBuildFile; fileRef = 092D6D75FFB313BB7F000001 /* testlock.c */; };
		BEC567780761D90500A33029 /* testsem.c in Sources */ = {isa = PBXBuildFile; fileRef = 083E487E006D86A17F000001 /* testsem.c */; };
		BEC567930761D90500A33029 /* testtimer.c in Sources */ = {isa = PBXBuildFile; fileRef = 083E4880006D86A17F000001 /* testtimer.c */; };
		BEC567AD0761D90500A33029 /* testver.c in Sources */ = {isa = PBXBuildFile; fileRef = 083E4882006D86A17F000001 /* testver.c */; };
		BEC567F00761D90600A33029 /* torturethread.c in Sources */ = {isa = PBXBuildFile; fileRef = 083E4887006D86A17F000001 /* torturethread.c */; };
		DB0F48DD17CA51E5008798C5 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		DB0F48DE17CA51E5008798C5 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		DB0F48DF17CA51E5008798C5 /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		DB0F48E017CA51E5008798C5 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		DB0F48E117CA51E5008798C5 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		DB0F48E217CA51E5008798C5 /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		DB0F48E417CA51E5008798C5 /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		DB0F48E517CA51E5008798C5 /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		DB0F48E617CA51E5008798C5 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		DB0F48EE17CA51F8008798C5 /* testdrawchessboard.c in Sources */ = {isa = PBXBuildFile; fileRef = DB0F48D717CA51D2008798C5 /* testdrawchessboard.c */; };
		DB0F48F317CA5212008798C5 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		DB0F48F417CA5212008798C5 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		DB0F48F517CA5212008798C5 /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		DB0F48F617CA5212008798C5 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		DB0F48F717CA5212008798C5 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		DB0F48F817CA5212008798C5 /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		DB0F48FA17CA5212008798C5 /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		DB0F48FB17CA5212008798C5 /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		DB0F48FC17CA5212008798C5 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		DB0F490317CA5225008798C5 /* testfilesystem.c in Sources */ = {isa = PBXBuildFile; fileRef = DB0F48D817CA51D2008798C5 /* testfilesystem.c */; };
		DB166D7116A1CFB200A1396C /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		DB166D7216A1CFB200A1396C /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		DB166D7316A1CFB200A1396C /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		DB166D7416A1CFB200A1396C /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		DB166D7516A1CFB200A1396C /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		DB166D7616A1CFB200A1396C /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		DB166D7716A1CFB200A1396C /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		DB166D7816A1CFB200A1396C /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		DB166D7A16A1CFD500A1396C /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		DB166D9316A1D1A500A1396C /* SDL_test_assert.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166D8416A1D1A500A1396C /* SDL_test_assert.c */; };
		DB166D9416A1D1A500A1396C /* SDL_test_common.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166D8516A1D1A500A1396C /* SDL_test_common.c */; };
		DB166D9516A1D1A500A1396C /* SDL_test_compare.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166D8616A1D1A500A1396C /* SDL_test_compare.c */; };
		DB166D9616A1D1A500A1396C /* SDL_test_crc32.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166D8716A1D1A500A1396C /* SDL_test_crc32.c */; };
		DB166D9716A1D1A500A1396C /* SDL_test_font.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166D8816A1D1A500A1396C /* SDL_test_font.c */; };
		DB166D9816A1D1A500A1396C /* SDL_test_fuzzer.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166D8916A1D1A500A1396C /* SDL_test_fuzzer.c */; };
		DB166D9916A1D1A500A1396C /* SDL_test_harness.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166D8A16A1D1A500A1396C /* SDL_test_harness.c */; };
		DB166D9A16A1D1A500A1396C /* SDL_test_imageBlit.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166D8B16A1D1A500A1396C /* SDL_test_imageBlit.c */; };
		DB166D9B16A1D1A500A1396C /* SDL_test_imageBlitBlend.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166D8C16A1D1A500A1396C /* SDL_test_imageBlitBlend.c */; };
		DB166D9C16A1D1A500A1396C /* SDL_test_imageFace.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166D8D16A1D1A500A1396C /* SDL_test_imageFace.c */; };
		DB166D9D16A1D1A500A1396C /* SDL_test_imagePrimitives.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166D8E16A1D1A500A1396C /* SDL_test_imagePrimitives.c */; };
		DB166D9E16A1D1A500A1396C /* SDL_test_imagePrimitivesBlend.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166D8F16A1D1A500A1396C /* SDL_test_imagePrimitivesBlend.c */; };
		DB166D9F16A1D1A500A1396C /* SDL_test_log.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166D9016A1D1A500A1396C /* SDL_test_log.c */; };
		DB166DA016A1D1A500A1396C /* SDL_test_md5.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166D9116A1D1A500A1396C /* SDL_test_md5.c */; };
		DB166DA116A1D1A500A1396C /* SDL_test_random.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166D9216A1D1A500A1396C /* SDL_test_random.c */; };
		DB166DA216A1D1E900A1396C /* libSDL_test.a in Frameworks */ = {isa = PBXBuildFile; fileRef = DB166D7F16A1D12400A1396C /* libSDL_test.a */; };
		DB166DA316A1D1FA00A1396C /* libSDL_test.a in Frameworks */ = {isa = PBXBuildFile; fileRef = DB166D7F16A1D12400A1396C /* libSDL_test.a */; };
		DB166DA416A1D21700A1396C /* libSDL_test.a in Frameworks */ = {isa = PBXBuildFile; fileRef = DB166D7F16A1D12400A1396C /* libSDL_test.a */; };
		DB166DA716A1D24D00A1396C /* libSDL_test.a in Frameworks */ = {isa = PBXBuildFile; fileRef = DB166D7F16A1D12400A1396C /* libSDL_test.a */; };
		DB166DAA16A1D27700A1396C /* libSDL_test.a in Frameworks */ = {isa = PBXBuildFile; fileRef = DB166D7F16A1D12400A1396C /* libSDL_test.a */; };
		DB166DAB16A1D27C00A1396C /* libSDL_test.a in Frameworks */ = {isa = PBXBuildFile; fileRef = DB166D7F16A1D12400A1396C /* libSDL_test.a */; };
		DB166DAC16A1D29000A1396C /* libSDL_test.a in Frameworks */ = {isa = PBXBuildFile; fileRef = DB166D7F16A1D12400A1396C /* libSDL_test.a */; };
		DB166DB116A1D2F600A1396C /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		DB166DB216A1D2F600A1396C /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		DB166DB316A1D2F600A1396C /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		DB166DB416A1D2F600A1396C /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		DB166DB516A1D2F600A1396C /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		DB166DB616A1D2F600A1396C /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		DB166DB816A1D2F600A1396C /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		DB166DB916A1D2F600A1396C /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		DB166DBA16A1D2F600A1396C /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		DB166DC116A1D31E00A1396C /* testgesture.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166CBB16A1C74100A1396C /* testgesture.c */; };
		DB166DC816A1D36A00A1396C /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		DB166DC916A1D36A00A1396C /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		DB166DCA16A1D36A00A1396C /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		DB166DCB16A1D36A00A1396C /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		DB166DCC16A1D36A00A1396C /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		DB166DCD16A1D36A00A1396C /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		DB166DCF16A1D36A00A1396C /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		DB166DD016A1D36A00A1396C /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		DB166DD116A1D36A00A1396C /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		DB166DD716A1D37800A1396C /* testmessage.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166CBD16A1C74100A1396C /* testmessage.c */; };
		DB166DDB16A1D42F00A1396C /* icon.bmp in CopyFiles */ = {isa = PBXBuildFile; fileRef = 00794E5D09D20839003FC8A1 /* icon.bmp */; };
		DB166DE016A1D50C00A1396C /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		DB166DE116A1D50C00A1396C /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		DB166DE216A1D50C00A1396C /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		DB166DE316A1D50C00A1396C /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		DB166DE416A1D50C00A1396C /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		DB166DE516A1D50C00A1396C /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		DB166DE716A1D50C00A1396C /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		DB166DE816A1D50C00A1396C /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		DB166DE916A1D50C00A1396C /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		DB166DEA16A1D50C00A1396C /* libSDL_test.a in Frameworks */ = {isa = PBXBuildFile; fileRef = DB166D7F16A1D12400A1396C /* libSDL_test.a */; };
		DB166DF016A1D52500A1396C /* testrelative.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166CBF16A1C74100A1396C /* testrelative.c */; };
		DB166DF716A1D57C00A1396C /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		DB166DF816A1D57C00A1396C /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		DB166DF916A1D57C00A1396C /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		DB166DFA16A1D57C00A1396C /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		DB166DFB16A1D57C00A1396C /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		DB166DFC16A1D57C00A1396C /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		DB166DFE16A1D57C00A1396C /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		DB166DFF16A1D57C00A1396C /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		DB166E0016A1D57C00A1396C /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		DB166E0116A1D57C00A1396C /* libSDL_test.a in Frameworks */ = {isa = PBXBuildFile; fileRef = DB166D7F16A1D12400A1396C /* libSDL_test.a */; };
		DB166E0716A1D59400A1396C /* testrendercopyex.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166CC016A1C74100A1396C /* testrendercopyex.c */; };
		DB166E0E16A1D5AD00A1396C /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		DB166E0F16A1D5AD00A1396C /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		DB166E1016A1D5AD00A1396C /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		DB166E1116A1D5AD00A1396C /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		DB166E1216A1D5AD00A1396C /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		DB166E1316A1D5AD00A1396C /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		DB166E1516A1D5AD00A1396C /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		DB166E1616A1D5AD00A1396C /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		DB166E1716A1D5AD00A1396C /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		DB166E1816A1D5AD00A1396C /* libSDL_test.a in Frameworks */ = {isa = PBXBuildFile; fileRef = DB166D7F16A1D12400A1396C /* libSDL_test.a */; };
		DB166E1E16A1D5C300A1396C /* testrendertarget.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166CC116A1C74100A1396C /* testrendertarget.c */; };
		DB166E2216A1D5EC00A1396C /* sample.bmp in CopyFiles */ = {isa = PBXBuildFile; fileRef = 00794E6109D20839003FC8A1 /* sample.bmp */; };
		DB166E2316A1D60B00A1396C /* icon.bmp in CopyFiles */ = {isa = PBXBuildFile; fileRef = 00794E5D09D20839003FC8A1 /* icon.bmp */; };
		DB166E2516A1D61900A1396C /* icon.bmp in CopyFiles */ = {isa = PBXBuildFile; fileRef = 00794E5D09D20839003FC8A1 /* icon.bmp */; };
		DB166E2616A1D61900A1396C /* sample.bmp in CopyFiles */ = {isa = PBXBuildFile; fileRef = 00794E6109D20839003FC8A1 /* sample.bmp */; };
		DB166E2B16A1D64D00A1396C /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		DB166E2C16A1D64D00A1396C /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		DB166E2D16A1D64D00A1396C /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		DB166E2E16A1D64D00A1396C /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		DB166E2F16A1D64D00A1396C /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		DB166E3016A1D64D00A1396C /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		DB166E3216A1D64D00A1396C /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		DB166E3316A1D64D00A1396C /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		DB166E3416A1D64D00A1396C /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		DB166E3C16A1D66500A1396C /* testrumble.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166CC216A1C74100A1396C /* testrumble.c */; };
		DB166E4116A1D69000A1396C /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		DB166E4216A1D69000A1396C /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		DB166E4316A1D69000A1396C /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		DB166E4416A1D69000A1396C /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		DB166E4516A1D69000A1396C /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		DB166E4616A1D69000A1396C /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		DB166E4816A1D69000A1396C /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		DB166E4916A1D69000A1396C /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		DB166E4A16A1D69000A1396C /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		DB166E4B16A1D69000A1396C /* libSDL_test.a in Frameworks */ = {isa = PBXBuildFile; fileRef = DB166D7F16A1D12400A1396C /* libSDL_test.a */; };
		DB166E4D16A1D69000A1396C /* icon.bmp in CopyFiles */ = {isa = PBXBuildFile; fileRef = 00794E5D09D20839003FC8A1 /* icon.bmp */; };
		DB166E4E16A1D69000A1396C /* sample.bmp in CopyFiles */ = {isa = PBXBuildFile; fileRef = 00794E6109D20839003FC8A1 /* sample.bmp */; };
		DB166E5416A1D6A300A1396C /* testscale.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166CC316A1C74100A1396C /* testscale.c */; };
		DB166E5B16A1D6F300A1396C /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		DB166E5C16A1D6F300A1396C /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		DB166E5D16A1D6F300A1396C /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		DB166E5E16A1D6F300A1396C /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		DB166E5F16A1D6F300A1396C /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		DB166E6016A1D6F300A1396C /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		DB166E6216A1D6F300A1396C /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		DB166E6316A1D6F300A1396C /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		DB166E6416A1D6F300A1396C /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		DB166E6A16A1D70C00A1396C /* testshader.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166CC416A1C74100A1396C /* testshader.c */; };
		DB166E7116A1D78400A1396C /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		DB166E7216A1D78400A1396C /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		DB166E7316A1D78400A1396C /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		DB166E7416A1D78400A1396C /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		DB166E7516A1D78400A1396C /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		DB166E7616A1D78400A1396C /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		DB166E7816A1D78400A1396C /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		DB166E7916A1D78400A1396C /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		DB166E7A16A1D78400A1396C /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		DB166E8416A1D78C00A1396C /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		DB166E8516A1D78C00A1396C /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		DB166E8616A1D78C00A1396C /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		DB166E8716A1D78C00A1396C /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		DB166E8816A1D78C00A1396C /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		DB166E8916A1D78C00A1396C /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		DB166E8B16A1D78C00A1396C /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		DB166E8C16A1D78C00A1396C /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		DB166E8D16A1D78C00A1396C /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		DB166E9316A1D7BC00A1396C /* testspriteminimal.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166CC516A1C74100A1396C /* testspriteminimal.c */; };
		DB166E9416A1D7C700A1396C /* teststreaming.c in Sources */ = {isa = PBXBuildFile; fileRef = DB166CC616A1C74100A1396C /* teststreaming.c */; };
		DB166E9A16A1D7F700A1396C /* moose.dat in CopyFiles */ = {isa = PBXBuildFile; fileRef = 00794E5E09D20839003FC8A1 /* moose.dat */; };
		DB166E9C16A1D80900A1396C /* icon.bmp in CopyFiles */ = {isa = PBXBuildFile; fileRef = 00794E5D09D20839003FC8A1 /* icon.bmp */; };
		DB166ED016A1D88100A1396C /* shapes in CopyFiles */ = {isa = PBXBuildFile; fileRef = DB166ECF16A1D87000A1396C /* shapes */; };
		DB445EEA18184B7000B306B0 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		DB445EEB18184B7000B306B0 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		DB445EEC18184B7000B306B0 /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		DB445EED18184B7000B306B0 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		DB445EEE18184B7000B306B0 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		DB445EEF18184B7000B306B0 /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		DB445EF118184B7000B306B0 /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		DB445EF218184B7000B306B0 /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		DB445EF318184B7000B306B0 /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		DB445EF418184B7000B306B0 /* libSDL_test.a in Frameworks */ = {isa = PBXBuildFile; fileRef = DB166D7F16A1D12400A1396C /* libSDL_test.a */; };
		DB445EFB18184BB600B306B0 /* testdropfile.c in Sources */ = {isa = PBXBuildFile; fileRef = DB445EFA18184BB600B306B0 /* testdropfile.c */; };
		DB89957118A19ABA0092407C /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		DB89957218A19ABA0092407C /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		DB89957318A19ABA0092407C /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		DB89957418A19ABA0092407C /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		DB89957518A19ABA0092407C /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		DB89957618A19ABA0092407C /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		DB89957818A19ABA0092407C /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		DB89957918A19ABA0092407C /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		DB89957A18A19ABA0092407C /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		DB89958418A19B130092407C /* testhotplug.c in Sources */ = {isa = PBXBuildFile; fileRef = DB89958318A19B130092407C /* testhotplug.c */; };
		DBEC54DD1A1A81C3005B1EAB /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		DBEC54DE1A1A81C3005B1EAB /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002F33A709CA188600EBEB88 /* Cocoa.framework */; };
		DBEC54DF1A1A81C3005B1EAB /* libSDL2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 003FA645093FFD41000C53B3 /* libSDL2.a */; };
		DBEC54E01A1A81C3005B1EAB /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863B10730545007319AE /* CoreAudio.framework */; };
		DBEC54E11A1A81C3005B1EAB /* ForceFeedback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863C10730545007319AE /* ForceFeedback.framework */; };
		DBEC54E21A1A81C3005B1EAB /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A863D10730545007319AE /* IOKit.framework */; };
		DBEC54E31A1A81C3005B1EAB /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A869F10730593007319AE /* AudioToolbox.framework */; };
		DBEC54E41A1A81C3005B1EAB /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A86A010730593007319AE /* CoreFoundation.framework */; };
		DBEC54E51A1A81C3005B1EAB /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A871410730623007319AE /* AudioUnit.framework */; };
		DBEC54E61A1A81C3005B1EAB /* Carbon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 002A873910730675007319AE /* Carbon.framework */; };
		DBEC54EB1A1A8205005B1EAB /* controllermap.c in Sources */ = {isa = PBXBuildFile; fileRef = DBEC54D11A1A811D005B1EAB /* controllermap.c */; };
		DBEC54ED1A1A828A005B1EAB /* axis.bmp in CopyFiles */ = {isa = PBXBuildFile; fileRef = DBEC54D61A1A8145005B1EAB /* axis.bmp */; };
		DBEC54EE1A1A828D005B1EAB /* button.bmp in CopyFiles */ = {isa = PBXBuildFile; fileRef = DBEC54D71A1A8145005B1EAB /* button.bmp */; };
		DBEC54EF1A1A828F005B1EAB /* controllermap.bmp in CopyFiles */ = {isa = PBXBuildFile; fileRef = DBEC54D81A1A8145005B1EAB /* controllermap.bmp */; };
		FA73672319A54A90004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73672819A54AB6004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73672919A54AB9004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73672A19A54AC0004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73672B19A54AC2004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73672C19A54AC5004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73672D19A54AC7004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73672E19A54ACA004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73672F19A54ACC004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73673019A54AD0004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73673119A54AD3004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73673219A54AD5004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73673319A54AD8004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73673419A54ADB004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73673519A54ADE004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73673619A54AE1004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73673719A54AE3004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73673819A54AE6004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73673919A54AE8004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73673A19A54AEB004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73673B19A54AED004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73673C19A54AF0004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73673D19A54AF3004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73673E19A54AF6004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73673F19A54AF8004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73674019A54AFB004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73674119A54AFE004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73674219A54B01004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73674319A54B04004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73674419A54B06004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73674519A54B09004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73674619A54B0B004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73674719A54B0F004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73674819A54B13004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73674919A54B16004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73674A19A54B19004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73674B19A54B1B004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73674C19A54B1F004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73674D19A54B22004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73674E19A54B25004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73674F19A54B28004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73675019A54B2B004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73675119A54B2F004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73675219A54B32004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
		FA73675319A54B35004122E4 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FA73672219A54A90004122E4 /* CoreVideo.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		001799471074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BEC566AB0761D90300A33029;
			remoteInfo = checkkeys;
		};
		0017994B1074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BEC566C50761D90300A33029;
			remoteInfo = loopwave;
		};
		0017994F1074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0017957410741F7900F5D044;
			remoteInfo = testatomic;
		};
		001799511074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 00179595107421BF00F5D044;
			remoteInfo = testaudioinfo;
		};
		001799591074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 00179756107431B300F5D044;
			remoteInfo = testdraw2;
		};
		0017995D1074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BEC566FB0761D90300A33029;
			remoteInfo = testerror;
		};
		0017995F1074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 002F340109CA1BFF00EBEB88;
			remoteInfo = testfile;
		};
		001799651074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0017970910742F3200F5D044;
			remoteInfo = testgl2;
		};
		001799671074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 00179730107430D600F5D044;
			remoteInfo = testhaptic;
		};
		001799691074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BEC567230761D90400A33029;
			remoteInfo = testthread;
		};
		0017996B1074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 002F342009CA1F0300EBEB88;
			remoteInfo = testiconv;
		};
		0017996D1074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 00179776107432AE00F5D044;
			remoteInfo = testime;
		};
		0017996F1074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 001797961074334C00F5D044;
			remoteInfo = testintersections;
		};
		001799711074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BEC567300761D90400A33029;
			remoteInfo = testjoystick;
		};
		001799731074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BEC5673D0761D90400A33029;
			remoteInfo = testkeys;
		};
		001799751074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 001797B8107433C600F5D044;
			remoteInfo = testloadso;
		};
		001799771074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BEC5674A0761D90400A33029;
			remoteInfo = testlock;
		};
		0017997B1074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 001797FA1074355200F5D044;
			remoteInfo = testmultiaudio;
		};
		0017997F1074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 001798781074392D00F5D044;
			remoteInfo = testnativex11;
		};
		001799831074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 002F343C09CA1FB300EBEB88;
			remoteInfo = testoverlay2;
		};
		001799871074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 002F345909CA204F00EBEB88;
			remoteInfo = testplatform;
		};
		001799891074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0017989D107439DF00F5D044;
			remoteInfo = testpower;
		};
		0017998B1074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 001798DA10743BEC00F5D044;
			remoteInfo = testresample;
		};
		0017998D1074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BEC567720761D90500A33029;
			remoteInfo = testsem;
		};
		001799911074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 001798FE10743F1000F5D044;
			remoteInfo = testsprite2;
		};
		001799931074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BEC5678D0761D90500A33029;
			remoteInfo = testtimer;
		};
		001799951074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BEC567A70761D90500A33029;
			remoteInfo = testversion;
		};
		0017999D1074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0017992010743FB700F5D044;
			remoteInfo = testwm2;
		};
		001799A11074403E00F5D044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BEC567EA0761D90600A33029;
			remoteInfo = torturethread;
		};
		003FA642093FFD41000C53B3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 003FA63A093FFD41000C53B3 /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = BECDF66C0761BA81005FE872;
			remoteInfo = Framework;
		};
		003FA644093FFD41000C53B3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 003FA63A093FFD41000C53B3 /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = BECDF6B30761BA81005FE872;
			remoteInfo = "Static Library";
		};
		003FA648093FFD41000C53B3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 003FA63A093FFD41000C53B3 /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = BECDF6BE0761BA81005FE872;
			remoteInfo = "Standard DMG";
		};
		DB0F490417CA5249008798C5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DB0F48D917CA51E5008798C5;
			remoteInfo = testdrawchessboard;
		};
		DB0F490617CA5249008798C5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DB0F48EF17CA5212008798C5;
			remoteInfo = testfilesystem;
		};
		DB166D6D16A1CEAA00A1396C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BBFC08B7164C6862003E6A99;
			remoteInfo = testgamecontroller;
		};
		DB166D6F16A1CEAF00A1396C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 4537749112091504002F0F45;
			remoteInfo = testshape;
		};
		DB166DC216A1D32C00A1396C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DB166DAD16A1D2F600A1396C;
			remoteInfo = testgesture;
		};
		DB166DD816A1D38900A1396C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DB166DC416A1D36A00A1396C;
			remoteInfo = testmessage;
		};
		DB166DF116A1D53700A1396C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DB166DDC16A1D50C00A1396C;
			remoteInfo = testrelative;
		};
		DB166E0816A1D5A400A1396C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DB166DF316A1D57C00A1396C;
			remoteInfo = testrendercopyex;
		};
		DB166E1F16A1D5D000A1396C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DB166E0A16A1D5AD00A1396C;
			remoteInfo = testrendertarget;
		};
		DB166E3A16A1D65A00A1396C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DB166E2716A1D64D00A1396C;
			remoteInfo = testrumble;
		};
		DB166E5516A1D6B800A1396C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DB166E3D16A1D69000A1396C;
			remoteInfo = testscale;
		};
		DB166E6B16A1D72000A1396C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DB166E5716A1D6F300A1396C;
			remoteInfo = testshader;
		};
		DB166E9516A1D7CD00A1396C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DB166E6D16A1D78400A1396C;
			remoteInfo = testspriteminimal;
		};
		DB166E9716A1D7CF00A1396C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 08FB7793FE84155DC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DB166E8016A1D78C00A1396C;
			remoteInfo = teststreaming;
		};
		DB1D40D617B3F30D00D74CFC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 003FA63A093FFD41000C53B3 /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = DB31407717554B71006C0E22;
			remoteInfo = "Shared Library";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		00794E6409D2084F003FC8A1 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 16;
			files = (
				00794E6609D20865003FC8A1 /* sample.wav in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		00794EEC09D2371F003FC8A1 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 16;
			files = (
				00794EF009D23739003FC8A1 /* utf8.txt in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		00794EF409D237C7003FC8A1 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 16;
			files = (
				00794EF709D237DE003FC8A1 /* moose.dat in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB0F48E717CA51E5008798C5 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB0F48FD17CA5212008798C5 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166DDA16A1D40F00A1396C /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 16;
			files = (
				DB166DDB16A1D42F00A1396C /* icon.bmp in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E2116A1D5DF00A1396C /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 16;
			files = (
				DB166E2316A1D60B00A1396C /* icon.bmp in CopyFiles */,
				DB166E2216A1D5EC00A1396C /* sample.bmp in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E2416A1D61000A1396C /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 16;
			files = (
				DB166E2516A1D61900A1396C /* icon.bmp in CopyFiles */,
				DB166E2616A1D61900A1396C /* sample.bmp in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E4C16A1D69000A1396C /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 16;
			files = (
				DB166E4D16A1D69000A1396C /* icon.bmp in CopyFiles */,
				DB166E4E16A1D69000A1396C /* sample.bmp in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E9916A1D7EE00A1396C /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 16;
			files = (
				DB166E9A16A1D7F700A1396C /* moose.dat in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E9B16A1D7FC00A1396C /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 16;
			files = (
				DB166E9C16A1D80900A1396C /* icon.bmp in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166ECE16A1D85400A1396C /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 16;
			files = (
				DB166ED016A1D88100A1396C /* shapes in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DBEC54EC1A1A827C005B1EAB /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 16;
			files = (
				DBEC54ED1A1A828A005B1EAB /* axis.bmp in CopyFiles */,
				DBEC54EE1A1A828D005B1EAB /* button.bmp in CopyFiles */,
				DBEC54EF1A1A828F005B1EAB /* controllermap.bmp in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		0017958C10741F7900F5D044 /* testatomic */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testatomic; sourceTree = BUILT_PRODUCTS_DIR; };
		0017958F1074216E00F5D044 /* testatomic.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testatomic.c; sourceTree = "<group>"; };
		001795AD107421BF00F5D044 /* testaudioinfo */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testaudioinfo; sourceTree = BUILT_PRODUCTS_DIR; };
		001795B01074222D00F5D044 /* testaudioinfo.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testaudioinfo.c; sourceTree = "<group>"; };
		0017972110742F3200F5D044 /* testgl2 */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testgl2; sourceTree = BUILT_PRODUCTS_DIR; };
		0017972710742FB900F5D044 /* testgl2.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testgl2.c; sourceTree = "<group>"; };
		00179748107430D600F5D044 /* testhaptic */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testhaptic; sourceTree = BUILT_PRODUCTS_DIR; };
		0017974E1074315700F5D044 /* testhaptic.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testhaptic.c; sourceTree = "<group>"; };
		0017976E107431B300F5D044 /* testdraw2 */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testdraw2; sourceTree = BUILT_PRODUCTS_DIR; };
		001797711074320D00F5D044 /* testdraw2.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testdraw2.c; sourceTree = "<group>"; };
		0017978E107432AE00F5D044 /* testime */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testime; sourceTree = BUILT_PRODUCTS_DIR; };
		00179791107432FA00F5D044 /* testime.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testime.c; sourceTree = "<group>"; };
		001797AE1074334C00F5D044 /* testintersections */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testintersections; sourceTree = BUILT_PRODUCTS_DIR; };
		001797B31074339C00F5D044 /* testintersections.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testintersections.c; sourceTree = "<group>"; };
		001797D0107433C600F5D044 /* testloadso */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testloadso; sourceTree = BUILT_PRODUCTS_DIR; };
		001797D31074343E00F5D044 /* testloadso.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testloadso.c; sourceTree = "<group>"; };
		001798121074355200F5D044 /* testmultiaudio */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testmultiaudio; sourceTree = BUILT_PRODUCTS_DIR; };
		001798151074359B00F5D044 /* testmultiaudio.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testmultiaudio.c; sourceTree = "<group>"; };
		0017985A107436ED00F5D044 /* testnative.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testnative.c; sourceTree = "<group>"; };
		0017985B107436ED00F5D044 /* testnative.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = testnative.h; sourceTree = "<group>"; };
		0017985C107436ED00F5D044 /* testnativecocoa.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = testnativecocoa.m; sourceTree = "<group>"; };
		00179872107438D000F5D044 /* testnativex11.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testnativex11.c; sourceTree = "<group>"; };
		001798941074392D00F5D044 /* testnative */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testnative; sourceTree = BUILT_PRODUCTS_DIR; };
		001798B5107439DF00F5D044 /* testpower */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testpower; sourceTree = BUILT_PRODUCTS_DIR; };
		001798B910743A4900F5D044 /* testpower.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testpower.c; sourceTree = "<group>"; };
		001798F210743BEC00F5D044 /* testresample */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testresample; sourceTree = BUILT_PRODUCTS_DIR; };
		001798F910743E9200F5D044 /* testresample.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testresample.c; sourceTree = "<group>"; };
		0017991610743F1000F5D044 /* testsprite2 */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testsprite2; sourceTree = BUILT_PRODUCTS_DIR; };
		0017991910743F5300F5D044 /* testsprite2.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testsprite2.c; sourceTree = "<group>"; };
		0017993810743FB700F5D044 /* testwm2 */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testwm2; sourceTree = BUILT_PRODUCTS_DIR; };
		0017993B10743FEF00F5D044 /* testwm2.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testwm2.c; sourceTree = "<group>"; };
		002A863B10730545007319AE /* CoreAudio.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreAudio.framework; path = /System/Library/Frameworks/CoreAudio.framework; sourceTree = "<absolute>"; };
		002A863C10730545007319AE /* ForceFeedback.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ForceFeedback.framework; path = /System/Library/Frameworks/ForceFeedback.framework; sourceTree = "<absolute>"; };
		002A863D10730545007319AE /* IOKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = IOKit.framework; path = /System/Library/Frameworks/IOKit.framework; sourceTree = "<absolute>"; };
		002A869F10730593007319AE /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = /System/Library/Frameworks/AudioToolbox.framework; sourceTree = "<absolute>"; };
		002A86A010730593007319AE /* CoreFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreFoundation.framework; path = /System/Library/Frameworks/CoreFoundation.framework; sourceTree = "<absolute>"; };
		002A871410730623007319AE /* AudioUnit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioUnit.framework; path = /System/Library/Frameworks/AudioUnit.framework; sourceTree = "<absolute>"; };
		002A873910730675007319AE /* Carbon.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Carbon.framework; path = /System/Library/Frameworks/Carbon.framework; sourceTree = "<absolute>"; };
		002F33A709CA188600EBEB88 /* Cocoa.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Cocoa.framework; path = /System/Library/Frameworks/Cocoa.framework; sourceTree = "<absolute>"; };
		002F341209CA1BFF00EBEB88 /* testfile */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testfile; sourceTree = BUILT_PRODUCTS_DIR; };
		002F341709CA1C5B00EBEB88 /* testfile.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = testfile.c; sourceTree = "<group>"; };
		002F343109CA1F0300EBEB88 /* testiconv */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testiconv; sourceTree = BUILT_PRODUCTS_DIR; };
		002F343609CA1F6F00EBEB88 /* testiconv.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = testiconv.c; sourceTree = "<group>"; };
		002F344D09CA1FB300EBEB88 /* testoverlay2 */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testoverlay2; sourceTree = BUILT_PRODUCTS_DIR; };
		002F345209CA201C00EBEB88 /* testoverlay2.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = testoverlay2.c; sourceTree = "<group>"; };
		002F346A09CA204F00EBEB88 /* testplatform */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testplatform; sourceTree = BUILT_PRODUCTS_DIR; };
		002F346F09CA20A600EBEB88 /* testplatform.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = testplatform.c; sourceTree = "<group>"; };
		003FA63A093FFD41000C53B3 /* SDL.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = SDL.xcodeproj; path = ../SDL/SDL.xcodeproj; sourceTree = SOURCE_ROOT; };
		00794E5D09D20839003FC8A1 /* icon.bmp */ = {isa = PBXFileReference; lastKnownFileType = image.bmp; path = icon.bmp; sourceTree = "<group>"; };
		00794E5E09D20839003FC8A1 /* moose.dat */ = {isa = PBXFileReference; lastKnownFileType = file; path = moose.dat; sourceTree = "<group>"; };
		00794E5F09D20839003FC8A1 /* picture.xbm */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = text; path = picture.xbm; sourceTree = "<group>"; };
		00794E6109D20839003FC8A1 /* sample.bmp */ = {isa = PBXFileReference; lastKnownFileType = image.bmp; path = sample.bmp; sourceTree = "<group>"; };
		00794E6209D20839003FC8A1 /* sample.wav */ = {isa = PBXFileReference; lastKnownFileType = audio.wav; path = sample.wav; sourceTree = "<group>"; };
		00794E6309D20839003FC8A1 /* utf8.txt */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = text; path = utf8.txt; sourceTree = "<group>"; };
		083E4872006D84C97F000001 /* loopwave.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = loopwave.c; sourceTree = "<group>"; };
		083E4878006D85357F000001 /* testerror.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = testerror.c; sourceTree = "<group>"; };
		083E487E006D86A17F000001 /* testsem.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = testsem.c; sourceTree = "<group>"; };
		083E4880006D86A17F000001 /* testtimer.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = testtimer.c; sourceTree = "<group>"; };
		083E4882006D86A17F000001 /* testver.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = testver.c; sourceTree = "<group>"; };
		083E4887006D86A17F000001 /* torturethread.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = torturethread.c; sourceTree = "<group>"; };
		092D6D10FFB30A2C7F000001 /* checkkeys.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = checkkeys.c; sourceTree = "<group>"; };
		092D6D58FFB311A97F000001 /* testthread.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = testthread.c; sourceTree = "<group>"; };
		092D6D62FFB312AA7F000001 /* testjoystick.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = testjoystick.c; sourceTree = "<group>"; };
		092D6D6CFFB313437F000001 /* testkeys.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = testkeys.c; sourceTree = "<group>"; };
		092D6D75FFB313BB7F000001 /* testlock.c */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = sourcecode.c.c; path = testlock.c; sourceTree = "<group>"; };
		4537749212091504002F0F45 /* testshape */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testshape; sourceTree = BUILT_PRODUCTS_DIR; };
		453774A4120915E3002F0F45 /* testshape.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testshape.c; sourceTree = "<group>"; };
		66E88E5B203B733C0004D44E /* Metal.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Metal.framework; path = System/Library/Frameworks/Metal.framework; sourceTree = SDKROOT; };
		66E88E8A203B778F0004D44E /* testyuv_cvt.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testyuv_cvt.c; sourceTree = "<group>"; };
		AAF02FF41F90089800B9A9FB /* SDL_test_memory.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_memory.c; sourceTree = "<group>"; };
		BBFC088E164C6820003E6A99 /* testgamecontroller.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testgamecontroller.c; sourceTree = "<group>"; };
		BBFC08CD164C6862003E6A99 /* testgamecontroller */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testgamecontroller; sourceTree = BUILT_PRODUCTS_DIR; };
		BEC566B60761D90300A33029 /* checkkeys */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = checkkeys; sourceTree = BUILT_PRODUCTS_DIR; };
		BEC566D10761D90300A33029 /* loopwave */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = loopwave; sourceTree = BUILT_PRODUCTS_DIR; };
		BEC567060761D90400A33029 /* testerror */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testerror; sourceTree = BUILT_PRODUCTS_DIR; };
		BEC5672E0761D90400A33029 /* testthread */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testthread; sourceTree = BUILT_PRODUCTS_DIR; };
		BEC5673B0761D90400A33029 /* testjoystick */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testjoystick; sourceTree = BUILT_PRODUCTS_DIR; };
		BEC567480761D90400A33029 /* testkeys */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testkeys; sourceTree = BUILT_PRODUCTS_DIR; };
		BEC567550761D90400A33029 /* testlock */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testlock; sourceTree = BUILT_PRODUCTS_DIR; };
		BEC5677D0761D90500A33029 /* testsem */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testsem; sourceTree = BUILT_PRODUCTS_DIR; };
		BEC567980761D90500A33029 /* testtimer */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testtimer; sourceTree = BUILT_PRODUCTS_DIR; };
		BEC567B20761D90500A33029 /* testversion */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testversion; sourceTree = BUILT_PRODUCTS_DIR; };
		BEC567F50761D90600A33029 /* torturethread */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = torturethread; sourceTree = BUILT_PRODUCTS_DIR; };
		DB0F48D717CA51D2008798C5 /* testdrawchessboard.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testdrawchessboard.c; sourceTree = "<group>"; };
		DB0F48D817CA51D2008798C5 /* testfilesystem.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testfilesystem.c; sourceTree = "<group>"; };
		DB0F48EC17CA51E5008798C5 /* testdrawchessboard */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testdrawchessboard; sourceTree = BUILT_PRODUCTS_DIR; };
		DB0F490117CA5212008798C5 /* testfilesystem */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testfilesystem; sourceTree = BUILT_PRODUCTS_DIR; };
		DB166CBB16A1C74100A1396C /* testgesture.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testgesture.c; sourceTree = "<group>"; };
		DB166CBC16A1C74100A1396C /* testgles.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testgles.c; sourceTree = "<group>"; };
		DB166CBD16A1C74100A1396C /* testmessage.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testmessage.c; sourceTree = "<group>"; };
		DB166CBF16A1C74100A1396C /* testrelative.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testrelative.c; sourceTree = "<group>"; };
		DB166CC016A1C74100A1396C /* testrendercopyex.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testrendercopyex.c; sourceTree = "<group>"; };
		DB166CC116A1C74100A1396C /* testrendertarget.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testrendertarget.c; sourceTree = "<group>"; };
		DB166CC216A1C74100A1396C /* testrumble.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testrumble.c; sourceTree = "<group>"; };
		DB166CC316A1C74100A1396C /* testscale.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testscale.c; sourceTree = "<group>"; };
		DB166CC416A1C74100A1396C /* testshader.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testshader.c; sourceTree = "<group>"; };
		DB166CC516A1C74100A1396C /* testspriteminimal.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = testspriteminimal.c; sourceTree = "<group>"; };
		DB166CC616A1C74100A1396C /* teststreaming.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = teststreaming.c; sourceTree = "<group>"; };
		DB166D7F16A1D12400A1396C /* libSDL_test.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libSDL_test.a; sourceTree = BUILT_PRODUCTS_DIR; };
		DB166D8416A1D1A500A1396C /* SDL_test_assert.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_assert.c; sourceTree = "<group>"; };
		DB166D8516A1D1A500A1396C /* SDL_test_common.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_common.c; sourceTree = "<group>"; };
		DB166D8616A1D1A500A1396C /* SDL_test_compare.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_compare.c; sourceTree = "<group>"; };
		DB166D8716A1D1A500A1396C /* SDL_test_crc32.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_crc32.c; sourceTree = "<group>"; };
		DB166D8816A1D1A500A1396C /* SDL_test_font.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_font.c; sourceTree = "<group>"; };
		DB166D8916A1D1A500A1396C /* SDL_test_fuzzer.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_fuzzer.c; sourceTree = "<group>"; };
		DB166D8A16A1D1A500A1396C /* SDL_test_harness.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_harness.c; sourceTree = "<group>"; };
		DB166D8B16A1D1A500A1396C /* SDL_test_imageBlit.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_imageBlit.c; sourceTree = "<group>"; };
		DB166D8C16A1D1A500A1396C /* SDL_test_imageBlitBlend.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_imageBlitBlend.c; sourceTree = "<group>"; };
		DB166D8D16A1D1A500A1396C /* SDL_test_imageFace.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_imageFace.c; sourceTree = "<group>"; };
		DB166D8E16A1D1A500A1396C /* SDL_test_imagePrimitives.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_imagePrimitives.c; sourceTree = "<group>"; };
		DB166D8F16A1D1A500A1396C /* SDL_test_imagePrimitivesBlend.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_imagePrimitivesBlend.c; sourceTree = "<group>"; };
		DB166D9016A1D1A500A1396C /* SDL_test_log.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_log.c; sourceTree = "<group>"; };
		DB166D9116A1D1A500A1396C /* SDL_test_md5.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_md5.c; sourceTree = "<group>"; };
		DB166D9216A1D1A500A1396C /* SDL_test_random.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_test_random.c; sourceTree = "<group>"; };
		DB166DBF16A1D2F600A1396C /* testgesture */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testgesture; sourceTree = BUILT_PRODUCTS_DIR; };
		DB166DD516A1D36A00A1396C /* testmessage */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testmessage; sourceTree = BUILT_PRODUCTS_DIR; };
		DB166DEE16A1D50C00A1396C /* testrelative */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testrelative; sourceTree = BUILT_PRODUCTS_DIR; };
		DB166E0516A1D57C00A1396C /* testrendercopyex */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testrendercopyex; sourceTree = BUILT_PRODUCTS_DIR; };
		DB166E1C16A1D5AD00A1396C /* testrendertarget */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testrendertarget; sourceTree = BUILT_PRODUCTS_DIR; };
		DB166E3816A1D64D00A1396C /* testrumble */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testrumble; sourceTree = BUILT_PRODUCTS_DIR; };
		DB166E5216A1D69000A1396C /* testscale */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testscale; sourceTree = BUILT_PRODUCTS_DIR; };
		DB166E6816A1D6F300A1396C /* testshader */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testshader; sourceTree = BUILT_PRODUCTS_DIR; };
		DB166E7E16A1D78400A1396C /* testspriteminimal */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testspriteminimal; sourceTree = BUILT_PRODUCTS_DIR; };
		DB166E9116A1D78C00A1396C /* teststreaming */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = teststreaming; sourceTree = BUILT_PRODUCTS_DIR; };
		DB166ECF16A1D87000A1396C /* shapes */ = {isa = PBXFileReference; lastKnownFileType = folder; path = shapes; sourceTree = "<group>"; };
		DB445EF818184B7000B306B0 /* testdropfile.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testdropfile.app; sourceTree = BUILT_PRODUCTS_DIR; };
		DB445EFA18184BB600B306B0 /* testdropfile.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testdropfile.c; sourceTree = "<group>"; };
		DB89957E18A19ABA0092407C /* testhotplug */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testhotplug; sourceTree = BUILT_PRODUCTS_DIR; };
		DB89958318A19B130092407C /* testhotplug.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testhotplug.c; sourceTree = "<group>"; };
		DBBC552C182831D700F3CA8D /* TestDropFile-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "TestDropFile-Info.plist"; sourceTree = SOURCE_ROOT; };
		DBEC54D11A1A811D005B1EAB /* controllermap.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = controllermap.c; sourceTree = "<group>"; };
		DBEC54D61A1A8145005B1EAB /* axis.bmp */ = {isa = PBXFileReference; lastKnownFileType = image.bmp; path = axis.bmp; sourceTree = "<group>"; };
		DBEC54D71A1A8145005B1EAB /* button.bmp */ = {isa = PBXFileReference; lastKnownFileType = image.bmp; path = button.bmp; sourceTree = "<group>"; };
		DBEC54D81A1A8145005B1EAB /* controllermap.bmp */ = {isa = PBXFileReference; lastKnownFileType = image.bmp; path = controllermap.bmp; sourceTree = "<group>"; };
		DBEC54EA1A1A81C3005B1EAB /* controllermap */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = controllermap; sourceTree = BUILT_PRODUCTS_DIR; };
		FA73672219A54A90004122E4 /* CoreVideo.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreVideo.framework; path = /System/Library/Frameworks/CoreVideo.framework; sourceTree = "<absolute>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		0017957A10741F7900F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E5F203B74860004D44E /* Metal.framework in Frameworks */,
				FA73672919A54AB9004122E4 /* CoreVideo.framework in Frameworks */,
				0017957C10741F7900F5D044 /* Cocoa.framework in Frameworks */,
				0017957D10741F7900F5D044 /* CoreAudio.framework in Frameworks */,
				0017957E10741F7900F5D044 /* ForceFeedback.framework in Frameworks */,
				0017957F10741F7900F5D044 /* IOKit.framework in Frameworks */,
				0017958010741F7900F5D044 /* AudioToolbox.framework in Frameworks */,
				0017958110741F7900F5D044 /* CoreFoundation.framework in Frameworks */,
				0017958310741F7900F5D044 /* AudioUnit.framework in Frameworks */,
				0017958410741F7900F5D044 /* Carbon.framework in Frameworks */,
				0017958510741F7900F5D044 /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017959B107421BF00F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E89203B764A0004D44E /* Metal.framework in Frameworks */,
				FA73672A19A54AC0004122E4 /* CoreVideo.framework in Frameworks */,
				0017959D107421BF00F5D044 /* Cocoa.framework in Frameworks */,
				0017959E107421BF00F5D044 /* CoreAudio.framework in Frameworks */,
				0017959F107421BF00F5D044 /* ForceFeedback.framework in Frameworks */,
				001795A0107421BF00F5D044 /* IOKit.framework in Frameworks */,
				001795A1107421BF00F5D044 /* AudioToolbox.framework in Frameworks */,
				001795A2107421BF00F5D044 /* CoreFoundation.framework in Frameworks */,
				001795A4107421BF00F5D044 /* AudioUnit.framework in Frameworks */,
				001795A5107421BF00F5D044 /* Carbon.framework in Frameworks */,
				001795A6107421BF00F5D044 /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017970F10742F3200F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E80203B76060004D44E /* Metal.framework in Frameworks */,
				FA73673319A54AD8004122E4 /* CoreVideo.framework in Frameworks */,
				0017971110742F3200F5D044 /* Cocoa.framework in Frameworks */,
				0017971210742F3200F5D044 /* CoreAudio.framework in Frameworks */,
				0017971310742F3200F5D044 /* ForceFeedback.framework in Frameworks */,
				0017971410742F3200F5D044 /* IOKit.framework in Frameworks */,
				0017971510742F3200F5D044 /* AudioToolbox.framework in Frameworks */,
				0017971610742F3200F5D044 /* CoreFoundation.framework in Frameworks */,
				0017971810742F3200F5D044 /* AudioUnit.framework in Frameworks */,
				0017971910742F3200F5D044 /* Carbon.framework in Frameworks */,
				0017971A10742F3200F5D044 /* libSDL2.a in Frameworks */,
				DB166DA316A1D1FA00A1396C /* libSDL_test.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		00179736107430D600F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E7F203B76000004D44E /* Metal.framework in Frameworks */,
				FA73673419A54ADB004122E4 /* CoreVideo.framework in Frameworks */,
				00179738107430D600F5D044 /* Cocoa.framework in Frameworks */,
				00179739107430D600F5D044 /* CoreAudio.framework in Frameworks */,
				0017973A107430D600F5D044 /* ForceFeedback.framework in Frameworks */,
				0017973B107430D600F5D044 /* IOKit.framework in Frameworks */,
				0017973C107430D600F5D044 /* AudioToolbox.framework in Frameworks */,
				0017973D107430D600F5D044 /* CoreFoundation.framework in Frameworks */,
				0017973F107430D600F5D044 /* AudioUnit.framework in Frameworks */,
				00179740107430D600F5D044 /* Carbon.framework in Frameworks */,
				00179741107430D600F5D044 /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017975C107431B300F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E88203B76420004D44E /* Metal.framework in Frameworks */,
				FA73672B19A54AC2004122E4 /* CoreVideo.framework in Frameworks */,
				0017975E107431B300F5D044 /* Cocoa.framework in Frameworks */,
				0017975F107431B300F5D044 /* CoreAudio.framework in Frameworks */,
				00179760107431B300F5D044 /* ForceFeedback.framework in Frameworks */,
				00179761107431B300F5D044 /* IOKit.framework in Frameworks */,
				00179762107431B300F5D044 /* AudioToolbox.framework in Frameworks */,
				00179763107431B300F5D044 /* CoreFoundation.framework in Frameworks */,
				00179765107431B300F5D044 /* AudioUnit.framework in Frameworks */,
				00179766107431B300F5D044 /* Carbon.framework in Frameworks */,
				00179767107431B300F5D044 /* libSDL2.a in Frameworks */,
				DB166DA216A1D1E900A1396C /* libSDL_test.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017977C107432AE00F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E7C203B75EB0004D44E /* Metal.framework in Frameworks */,
				FA73673719A54AE3004122E4 /* CoreVideo.framework in Frameworks */,
				0017977E107432AE00F5D044 /* Cocoa.framework in Frameworks */,
				0017977F107432AE00F5D044 /* CoreAudio.framework in Frameworks */,
				00179780107432AE00F5D044 /* ForceFeedback.framework in Frameworks */,
				00179781107432AE00F5D044 /* IOKit.framework in Frameworks */,
				00179782107432AE00F5D044 /* AudioToolbox.framework in Frameworks */,
				00179783107432AE00F5D044 /* CoreFoundation.framework in Frameworks */,
				00179785107432AE00F5D044 /* AudioUnit.framework in Frameworks */,
				00179786107432AE00F5D044 /* Carbon.framework in Frameworks */,
				00179787107432AE00F5D044 /* libSDL2.a in Frameworks */,
				DB166DA716A1D24D00A1396C /* libSDL_test.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017979C1074334C00F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E7B203B75E40004D44E /* Metal.framework in Frameworks */,
				FA73673819A54AE6004122E4 /* CoreVideo.framework in Frameworks */,
				0017979E1074334C00F5D044 /* Cocoa.framework in Frameworks */,
				0017979F1074334C00F5D044 /* CoreAudio.framework in Frameworks */,
				001797A01074334C00F5D044 /* ForceFeedback.framework in Frameworks */,
				001797A11074334C00F5D044 /* IOKit.framework in Frameworks */,
				001797A21074334C00F5D044 /* AudioToolbox.framework in Frameworks */,
				001797A31074334C00F5D044 /* CoreFoundation.framework in Frameworks */,
				001797A51074334C00F5D044 /* AudioUnit.framework in Frameworks */,
				001797A61074334C00F5D044 /* Carbon.framework in Frameworks */,
				001797A71074334C00F5D044 /* libSDL2.a in Frameworks */,
				DB166DAA16A1D27700A1396C /* libSDL_test.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		001797BE107433C600F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E78203B75CE0004D44E /* Metal.framework in Frameworks */,
				FA73673B19A54AED004122E4 /* CoreVideo.framework in Frameworks */,
				001797C0107433C600F5D044 /* Cocoa.framework in Frameworks */,
				001797C1107433C600F5D044 /* CoreAudio.framework in Frameworks */,
				001797C2107433C600F5D044 /* ForceFeedback.framework in Frameworks */,
				001797C3107433C600F5D044 /* IOKit.framework in Frameworks */,
				001797C4107433C600F5D044 /* AudioToolbox.framework in Frameworks */,
				001797C5107433C600F5D044 /* CoreFoundation.framework in Frameworks */,
				001797C7107433C600F5D044 /* AudioUnit.framework in Frameworks */,
				001797C8107433C600F5D044 /* Carbon.framework in Frameworks */,
				001797C9107433C600F5D044 /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		001798001074355200F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E75203B75B90004D44E /* Metal.framework in Frameworks */,
				FA73673E19A54AF6004122E4 /* CoreVideo.framework in Frameworks */,
				001798021074355200F5D044 /* Cocoa.framework in Frameworks */,
				001798031074355200F5D044 /* CoreAudio.framework in Frameworks */,
				001798041074355200F5D044 /* ForceFeedback.framework in Frameworks */,
				001798051074355200F5D044 /* IOKit.framework in Frameworks */,
				001798061074355200F5D044 /* AudioToolbox.framework in Frameworks */,
				001798071074355200F5D044 /* CoreFoundation.framework in Frameworks */,
				001798091074355200F5D044 /* AudioUnit.framework in Frameworks */,
				0017980A1074355200F5D044 /* Carbon.framework in Frameworks */,
				0017980B1074355200F5D044 /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		001798821074392D00F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E74203B75AF0004D44E /* Metal.framework in Frameworks */,
				FA73673F19A54AF8004122E4 /* CoreVideo.framework in Frameworks */,
				001798841074392D00F5D044 /* Cocoa.framework in Frameworks */,
				001798851074392D00F5D044 /* CoreAudio.framework in Frameworks */,
				001798861074392D00F5D044 /* ForceFeedback.framework in Frameworks */,
				001798871074392D00F5D044 /* IOKit.framework in Frameworks */,
				001798881074392D00F5D044 /* AudioToolbox.framework in Frameworks */,
				001798891074392D00F5D044 /* CoreFoundation.framework in Frameworks */,
				0017988B1074392D00F5D044 /* AudioUnit.framework in Frameworks */,
				0017988C1074392D00F5D044 /* Carbon.framework in Frameworks */,
				0017988D1074392D00F5D044 /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		001798A3107439DF00F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E71203B75780004D44E /* Metal.framework in Frameworks */,
				FA73674219A54B01004122E4 /* CoreVideo.framework in Frameworks */,
				001798A5107439DF00F5D044 /* Cocoa.framework in Frameworks */,
				001798A6107439DF00F5D044 /* CoreAudio.framework in Frameworks */,
				001798A7107439DF00F5D044 /* ForceFeedback.framework in Frameworks */,
				001798A8107439DF00F5D044 /* IOKit.framework in Frameworks */,
				001798A9107439DF00F5D044 /* AudioToolbox.framework in Frameworks */,
				001798AA107439DF00F5D044 /* CoreFoundation.framework in Frameworks */,
				001798AC107439DF00F5D044 /* AudioUnit.framework in Frameworks */,
				001798AD107439DF00F5D044 /* Carbon.framework in Frameworks */,
				001798AE107439DF00F5D044 /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		001798E010743BEC00F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E6D203B755B0004D44E /* Metal.framework in Frameworks */,
				FA73674619A54B0B004122E4 /* CoreVideo.framework in Frameworks */,
				001798E210743BEC00F5D044 /* Cocoa.framework in Frameworks */,
				001798E310743BEC00F5D044 /* CoreAudio.framework in Frameworks */,
				001798E410743BEC00F5D044 /* ForceFeedback.framework in Frameworks */,
				001798E510743BEC00F5D044 /* IOKit.framework in Frameworks */,
				001798E610743BEC00F5D044 /* AudioToolbox.framework in Frameworks */,
				001798E710743BEC00F5D044 /* CoreFoundation.framework in Frameworks */,
				001798E910743BEC00F5D044 /* AudioUnit.framework in Frameworks */,
				001798EA10743BEC00F5D044 /* Carbon.framework in Frameworks */,
				001798EB10743BEC00F5D044 /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017990410743F1000F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E67203B751D0004D44E /* Metal.framework in Frameworks */,
				FA73674C19A54B1F004122E4 /* CoreVideo.framework in Frameworks */,
				0017990610743F1000F5D044 /* Cocoa.framework in Frameworks */,
				0017990710743F1000F5D044 /* CoreAudio.framework in Frameworks */,
				0017990810743F1000F5D044 /* ForceFeedback.framework in Frameworks */,
				0017990910743F1000F5D044 /* IOKit.framework in Frameworks */,
				0017990A10743F1000F5D044 /* AudioToolbox.framework in Frameworks */,
				0017990B10743F1000F5D044 /* CoreFoundation.framework in Frameworks */,
				0017990D10743F1000F5D044 /* AudioUnit.framework in Frameworks */,
				0017990E10743F1000F5D044 /* Carbon.framework in Frameworks */,
				0017990F10743F1000F5D044 /* libSDL2.a in Frameworks */,
				DB166DAB16A1D27C00A1396C /* libSDL_test.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017992610743FB700F5D044 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E61203B74CC0004D44E /* Metal.framework in Frameworks */,
				FA73675219A54B32004122E4 /* CoreVideo.framework in Frameworks */,
				0017992810743FB700F5D044 /* Cocoa.framework in Frameworks */,
				0017992910743FB700F5D044 /* CoreAudio.framework in Frameworks */,
				0017992A10743FB700F5D044 /* ForceFeedback.framework in Frameworks */,
				0017992B10743FB700F5D044 /* IOKit.framework in Frameworks */,
				0017992C10743FB700F5D044 /* AudioToolbox.framework in Frameworks */,
				0017992D10743FB700F5D044 /* CoreFoundation.framework in Frameworks */,
				0017992F10743FB700F5D044 /* AudioUnit.framework in Frameworks */,
				0017993010743FB700F5D044 /* Carbon.framework in Frameworks */,
				0017993110743FB700F5D044 /* libSDL2.a in Frameworks */,
				DB166DAC16A1D29000A1396C /* libSDL_test.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		002F340809CA1BFF00EBEB88 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E84203B76230004D44E /* Metal.framework in Frameworks */,
				FA73672F19A54ACC004122E4 /* CoreVideo.framework in Frameworks */,
				002F340B09CA1BFF00EBEB88 /* Cocoa.framework in Frameworks */,
				002A866B10730548007319AE /* CoreAudio.framework in Frameworks */,
				002A866C10730548007319AE /* ForceFeedback.framework in Frameworks */,
				002A866D10730548007319AE /* IOKit.framework in Frameworks */,
				002A86BF10730595007319AE /* AudioToolbox.framework in Frameworks */,
				002A86C010730595007319AE /* CoreFoundation.framework in Frameworks */,
				002A872410730624007319AE /* AudioUnit.framework in Frameworks */,
				002A874910730676007319AE /* Carbon.framework in Frameworks */,
				001794D11073667B00F5D044 /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		002F342709CA1F0300EBEB88 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E7D203B75F30004D44E /* Metal.framework in Frameworks */,
				FA73673619A54AE1004122E4 /* CoreVideo.framework in Frameworks */,
				002F342A09CA1F0300EBEB88 /* Cocoa.framework in Frameworks */,
				002A866210730547007319AE /* CoreAudio.framework in Frameworks */,
				002A866310730547007319AE /* ForceFeedback.framework in Frameworks */,
				002A866410730547007319AE /* IOKit.framework in Frameworks */,
				002A86B910730594007319AE /* AudioToolbox.framework in Frameworks */,
				002A86BA10730594007319AE /* CoreFoundation.framework in Frameworks */,
				002A872110730624007319AE /* AudioUnit.framework in Frameworks */,
				002A874610730676007319AE /* Carbon.framework in Frameworks */,
				001794D41073668800F5D044 /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		002F344309CA1FB300EBEB88 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E73203B758C0004D44E /* Metal.framework in Frameworks */,
				FA73674019A54AFB004122E4 /* CoreVideo.framework in Frameworks */,
				002F344609CA1FB300EBEB88 /* Cocoa.framework in Frameworks */,
				002A868010730549007319AE /* CoreAudio.framework in Frameworks */,
				002A868110730549007319AE /* ForceFeedback.framework in Frameworks */,
				002A868210730549007319AE /* IOKit.framework in Frameworks */,
				002A86CD10730595007319AE /* AudioToolbox.framework in Frameworks */,
				002A86CE10730596007319AE /* CoreFoundation.framework in Frameworks */,
				002A872B10730624007319AE /* AudioUnit.framework in Frameworks */,
				002A875010730677007319AE /* Carbon.framework in Frameworks */,
				001794D91073669E00F5D044 /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		002F346009CA204F00EBEB88 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E72203B757F0004D44E /* Metal.framework in Frameworks */,
				FA73674119A54AFE004122E4 /* CoreVideo.framework in Frameworks */,
				002F346309CA204F00EBEB88 /* Cocoa.framework in Frameworks */,
				002A868610730549007319AE /* CoreAudio.framework in Frameworks */,
				002A868710730549007319AE /* ForceFeedback.framework in Frameworks */,
				002A868810730549007319AE /* IOKit.framework in Frameworks */,
				002A86D110730596007319AE /* AudioToolbox.framework in Frameworks */,
				002A86D210730596007319AE /* CoreFoundation.framework in Frameworks */,
				002A872D10730624007319AE /* AudioUnit.framework in Frameworks */,
				002A875210730677007319AE /* Carbon.framework in Frameworks */,
				001794DB107366A700F5D044 /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4537749012091504002F0F45 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E68203B75250004D44E /* Metal.framework in Frameworks */,
				FA73674B19A54B1B004122E4 /* CoreVideo.framework in Frameworks */,
				DB166D7116A1CFB200A1396C /* AudioToolbox.framework in Frameworks */,
				DB166D7216A1CFB200A1396C /* AudioUnit.framework in Frameworks */,
				DB166D7316A1CFB200A1396C /* Carbon.framework in Frameworks */,
				DB166D7416A1CFB200A1396C /* Cocoa.framework in Frameworks */,
				DB166D7516A1CFB200A1396C /* CoreAudio.framework in Frameworks */,
				DB166D7616A1CFB200A1396C /* CoreFoundation.framework in Frameworks */,
				DB166D7716A1CFB200A1396C /* ForceFeedback.framework in Frameworks */,
				DB166D7816A1CFB200A1396C /* IOKit.framework in Frameworks */,
				DB166D7A16A1CFD500A1396C /* libSDL2.a in Frameworks */,
				DB166DA416A1D21700A1396C /* libSDL_test.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BBFC08BE164C6862003E6A99 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E82203B76140004D44E /* Metal.framework in Frameworks */,
				FA73673119A54AD3004122E4 /* CoreVideo.framework in Frameworks */,
				BBFC08C0164C6862003E6A99 /* Cocoa.framework in Frameworks */,
				BBFC08C1164C6862003E6A99 /* CoreAudio.framework in Frameworks */,
				BBFC08C2164C6862003E6A99 /* ForceFeedback.framework in Frameworks */,
				BBFC08C3164C6862003E6A99 /* IOKit.framework in Frameworks */,
				BBFC08C4164C6862003E6A99 /* AudioToolbox.framework in Frameworks */,
				BBFC08C5164C6862003E6A99 /* CoreFoundation.framework in Frameworks */,
				BBFC08C7164C6862003E6A99 /* AudioUnit.framework in Frameworks */,
				BBFC08C8164C6862003E6A99 /* Carbon.framework in Frameworks */,
				BBFC08C9164C6862003E6A99 /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC566B20761D90300A33029 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E5C203B733D0004D44E /* Metal.framework in Frameworks */,
				FA73672319A54A90004122E4 /* CoreVideo.framework in Frameworks */,
				002F33C109CA188600EBEB88 /* Cocoa.framework in Frameworks */,
				002A863010730405007319AE /* libSDL2.a in Frameworks */,
				002A864D10730546007319AE /* CoreAudio.framework in Frameworks */,
				002A864E10730546007319AE /* ForceFeedback.framework in Frameworks */,
				002A864F10730546007319AE /* IOKit.framework in Frameworks */,
				002A86AB10730594007319AE /* AudioToolbox.framework in Frameworks */,
				002A86AC10730594007319AE /* CoreFoundation.framework in Frameworks */,
				002A871A10730623007319AE /* AudioUnit.framework in Frameworks */,
				002A873F10730675007319AE /* Carbon.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC566CC0761D90300A33029 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E5E203B74490004D44E /* Metal.framework in Frameworks */,
				FA73672819A54AB6004122E4 /* CoreVideo.framework in Frameworks */,
				002F33BF09CA188600EBEB88 /* Cocoa.framework in Frameworks */,
				002A865310730547007319AE /* CoreAudio.framework in Frameworks */,
				002A865410730547007319AE /* ForceFeedback.framework in Frameworks */,
				002A865510730547007319AE /* IOKit.framework in Frameworks */,
				002A86AF10730594007319AE /* AudioToolbox.framework in Frameworks */,
				002A86B010730594007319AE /* CoreFoundation.framework in Frameworks */,
				002A871C10730623007319AE /* AudioUnit.framework in Frameworks */,
				002A874110730676007319AE /* Carbon.framework in Frameworks */,
				002A875E10730745007319AE /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567020761D90300A33029 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E85203B762D0004D44E /* Metal.framework in Frameworks */,
				FA73672E19A54ACA004122E4 /* CoreVideo.framework in Frameworks */,
				002F33BC09CA188600EBEB88 /* Cocoa.framework in Frameworks */,
				002A866E10730548007319AE /* CoreAudio.framework in Frameworks */,
				002A866F10730548007319AE /* ForceFeedback.framework in Frameworks */,
				002A867010730548007319AE /* IOKit.framework in Frameworks */,
				002A86C110730595007319AE /* AudioToolbox.framework in Frameworks */,
				002A86C210730595007319AE /* CoreFoundation.framework in Frameworks */,
				002A872510730624007319AE /* AudioUnit.framework in Frameworks */,
				002A874A10730676007319AE /* Carbon.framework in Frameworks */,
				001794D01073667700F5D044 /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC5672A0761D90400A33029 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E64203B74E50004D44E /* Metal.framework in Frameworks */,
				FA73674F19A54B28004122E4 /* CoreVideo.framework in Frameworks */,
				002F33B809CA188600EBEB88 /* Cocoa.framework in Frameworks */,
				002A868F1073054A007319AE /* CoreAudio.framework in Frameworks */,
				002A86901073054A007319AE /* ForceFeedback.framework in Frameworks */,
				002A86911073054A007319AE /* IOKit.framework in Frameworks */,
				002A86D710730596007319AE /* AudioToolbox.framework in Frameworks */,
				002A86D810730596007319AE /* CoreFoundation.framework in Frameworks */,
				002A873010730625007319AE /* AudioUnit.framework in Frameworks */,
				002A875510730677007319AE /* Carbon.framework in Frameworks */,
				001794DE107366B900F5D044 /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567370761D90400A33029 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E7A203B75DE0004D44E /* Metal.framework in Frameworks */,
				FA73673919A54AE8004122E4 /* CoreVideo.framework in Frameworks */,
				002F33B709CA188600EBEB88 /* Cocoa.framework in Frameworks */,
				002A867410730548007319AE /* CoreAudio.framework in Frameworks */,
				002A867510730548007319AE /* ForceFeedback.framework in Frameworks */,
				002A867610730548007319AE /* IOKit.framework in Frameworks */,
				002A86C510730595007319AE /* AudioToolbox.framework in Frameworks */,
				002A86C610730595007319AE /* CoreFoundation.framework in Frameworks */,
				002A872710730624007319AE /* AudioUnit.framework in Frameworks */,
				002A874C10730676007319AE /* Carbon.framework in Frameworks */,
				001794D51073668D00F5D044 /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567440761D90400A33029 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E79203B75D50004D44E /* Metal.framework in Frameworks */,
				FA73673A19A54AEB004122E4 /* CoreVideo.framework in Frameworks */,
				002F33B509CA188600EBEB88 /* Cocoa.framework in Frameworks */,
				002A867710730548007319AE /* CoreAudio.framework in Frameworks */,
				002A867810730548007319AE /* ForceFeedback.framework in Frameworks */,
				002A867910730549007319AE /* IOKit.framework in Frameworks */,
				002A86C710730595007319AE /* AudioToolbox.framework in Frameworks */,
				002A86C810730595007319AE /* CoreFoundation.framework in Frameworks */,
				002A872810730624007319AE /* AudioUnit.framework in Frameworks */,
				002A874D10730677007319AE /* Carbon.framework in Frameworks */,
				001794D61073669200F5D044 /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567510761D90400A33029 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E77203B75C70004D44E /* Metal.framework in Frameworks */,
				FA73673C19A54AF0004122E4 /* CoreVideo.framework in Frameworks */,
				002F33B609CA188600EBEB88 /* Cocoa.framework in Frameworks */,
				002A867A10730549007319AE /* CoreAudio.framework in Frameworks */,
				002A867B10730549007319AE /* ForceFeedback.framework in Frameworks */,
				002A867C10730549007319AE /* IOKit.framework in Frameworks */,
				002A86C910730595007319AE /* AudioToolbox.framework in Frameworks */,
				002A86CA10730595007319AE /* CoreFoundation.framework in Frameworks */,
				002A872910730624007319AE /* AudioUnit.framework in Frameworks */,
				002A874E10730677007319AE /* Carbon.framework in Frameworks */,
				001794D71073669700F5D044 /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567790761D90500A33029 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E6A203B75450004D44E /* Metal.framework in Frameworks */,
				FA73674919A54B16004122E4 /* CoreVideo.framework in Frameworks */,
				002F33B209CA188600EBEB88 /* Cocoa.framework in Frameworks */,
				002A868910730549007319AE /* CoreAudio.framework in Frameworks */,
				002A868A10730549007319AE /* ForceFeedback.framework in Frameworks */,
				002A868B1073054A007319AE /* IOKit.framework in Frameworks */,
				002A86D310730596007319AE /* AudioToolbox.framework in Frameworks */,
				002A86D410730596007319AE /* CoreFoundation.framework in Frameworks */,
				002A872E10730624007319AE /* AudioUnit.framework in Frameworks */,
				002A875310730677007319AE /* Carbon.framework in Frameworks */,
				001794DC107366AC00F5D044 /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567940761D90500A33029 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E63203B74DC0004D44E /* Metal.framework in Frameworks */,
				FA73675019A54B2B004122E4 /* CoreVideo.framework in Frameworks */,
				002F33B009CA188600EBEB88 /* Cocoa.framework in Frameworks */,
				002A86981073054A007319AE /* CoreAudio.framework in Frameworks */,
				002A86991073054A007319AE /* ForceFeedback.framework in Frameworks */,
				002A869A1073054A007319AE /* IOKit.framework in Frameworks */,
				002A86DD10730596007319AE /* AudioToolbox.framework in Frameworks */,
				002A86DE10730596007319AE /* CoreFoundation.framework in Frameworks */,
				002A873310730625007319AE /* AudioUnit.framework in Frameworks */,
				002A875810730678007319AE /* Carbon.framework in Frameworks */,
				001794DF107366BD00F5D044 /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567AE0761D90500A33029 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E62203B74D50004D44E /* Metal.framework in Frameworks */,
				FA73675119A54B2F004122E4 /* CoreVideo.framework in Frameworks */,
				002F33AF09CA188600EBEB88 /* Cocoa.framework in Frameworks */,
				002A86951073054A007319AE /* CoreAudio.framework in Frameworks */,
				002A86961073054A007319AE /* ForceFeedback.framework in Frameworks */,
				002A86971073054A007319AE /* IOKit.framework in Frameworks */,
				002A86DB10730596007319AE /* AudioToolbox.framework in Frameworks */,
				002A86DC10730596007319AE /* CoreFoundation.framework in Frameworks */,
				002A873210730625007319AE /* AudioUnit.framework in Frameworks */,
				002A875710730678007319AE /* Carbon.framework in Frameworks */,
				001794E0107366C100F5D044 /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567F10761D90600A33029 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E60203B74C20004D44E /* Metal.framework in Frameworks */,
				FA73675319A54B35004122E4 /* CoreVideo.framework in Frameworks */,
				002F33AA09CA188600EBEB88 /* Cocoa.framework in Frameworks */,
				002A864110730546007319AE /* CoreAudio.framework in Frameworks */,
				002A864210730546007319AE /* ForceFeedback.framework in Frameworks */,
				002A864310730546007319AE /* IOKit.framework in Frameworks */,
				002A86A310730593007319AE /* AudioToolbox.framework in Frameworks */,
				002A86A410730593007319AE /* CoreFoundation.framework in Frameworks */,
				002A871610730623007319AE /* AudioUnit.framework in Frameworks */,
				002A873B10730675007319AE /* Carbon.framework in Frameworks */,
				001794E5107366D900F5D044 /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB0F48DC17CA51E5008798C5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E87203B763B0004D44E /* Metal.framework in Frameworks */,
				FA73672C19A54AC5004122E4 /* CoreVideo.framework in Frameworks */,
				DB0F48DD17CA51E5008798C5 /* Cocoa.framework in Frameworks */,
				DB0F48DE17CA51E5008798C5 /* CoreAudio.framework in Frameworks */,
				DB0F48DF17CA51E5008798C5 /* ForceFeedback.framework in Frameworks */,
				DB0F48E017CA51E5008798C5 /* IOKit.framework in Frameworks */,
				DB0F48E117CA51E5008798C5 /* AudioToolbox.framework in Frameworks */,
				DB0F48E217CA51E5008798C5 /* CoreFoundation.framework in Frameworks */,
				DB0F48E417CA51E5008798C5 /* AudioUnit.framework in Frameworks */,
				DB0F48E517CA51E5008798C5 /* Carbon.framework in Frameworks */,
				DB0F48E617CA51E5008798C5 /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB0F48F217CA5212008798C5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E83203B761D0004D44E /* Metal.framework in Frameworks */,
				FA73673019A54AD0004122E4 /* CoreVideo.framework in Frameworks */,
				DB0F48F317CA5212008798C5 /* Cocoa.framework in Frameworks */,
				DB0F48F417CA5212008798C5 /* CoreAudio.framework in Frameworks */,
				DB0F48F517CA5212008798C5 /* ForceFeedback.framework in Frameworks */,
				DB0F48F617CA5212008798C5 /* IOKit.framework in Frameworks */,
				DB0F48F717CA5212008798C5 /* AudioToolbox.framework in Frameworks */,
				DB0F48F817CA5212008798C5 /* CoreFoundation.framework in Frameworks */,
				DB0F48FA17CA5212008798C5 /* AudioUnit.framework in Frameworks */,
				DB0F48FB17CA5212008798C5 /* Carbon.framework in Frameworks */,
				DB0F48FC17CA5212008798C5 /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166D7C16A1D12400A1396C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166DB016A1D2F600A1396C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E81203B760D0004D44E /* Metal.framework in Frameworks */,
				FA73673219A54AD5004122E4 /* CoreVideo.framework in Frameworks */,
				DB166DB116A1D2F600A1396C /* Cocoa.framework in Frameworks */,
				DB166DB216A1D2F600A1396C /* CoreAudio.framework in Frameworks */,
				DB166DB316A1D2F600A1396C /* ForceFeedback.framework in Frameworks */,
				DB166DB416A1D2F600A1396C /* IOKit.framework in Frameworks */,
				DB166DB516A1D2F600A1396C /* AudioToolbox.framework in Frameworks */,
				DB166DB616A1D2F600A1396C /* CoreFoundation.framework in Frameworks */,
				DB166DB816A1D2F600A1396C /* AudioUnit.framework in Frameworks */,
				DB166DB916A1D2F600A1396C /* Carbon.framework in Frameworks */,
				DB166DBA16A1D2F600A1396C /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166DC716A1D36A00A1396C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E76203B75BF0004D44E /* Metal.framework in Frameworks */,
				FA73673D19A54AF3004122E4 /* CoreVideo.framework in Frameworks */,
				DB166DC816A1D36A00A1396C /* Cocoa.framework in Frameworks */,
				DB166DC916A1D36A00A1396C /* CoreAudio.framework in Frameworks */,
				DB166DCA16A1D36A00A1396C /* ForceFeedback.framework in Frameworks */,
				DB166DCB16A1D36A00A1396C /* IOKit.framework in Frameworks */,
				DB166DCC16A1D36A00A1396C /* AudioToolbox.framework in Frameworks */,
				DB166DCD16A1D36A00A1396C /* CoreFoundation.framework in Frameworks */,
				DB166DCF16A1D36A00A1396C /* AudioUnit.framework in Frameworks */,
				DB166DD016A1D36A00A1396C /* Carbon.framework in Frameworks */,
				DB166DD116A1D36A00A1396C /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166DDF16A1D50C00A1396C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E70203B75710004D44E /* Metal.framework in Frameworks */,
				FA73674319A54B04004122E4 /* CoreVideo.framework in Frameworks */,
				DB166DE016A1D50C00A1396C /* Cocoa.framework in Frameworks */,
				DB166DE116A1D50C00A1396C /* CoreAudio.framework in Frameworks */,
				DB166DE216A1D50C00A1396C /* ForceFeedback.framework in Frameworks */,
				DB166DE316A1D50C00A1396C /* IOKit.framework in Frameworks */,
				DB166DE416A1D50C00A1396C /* AudioToolbox.framework in Frameworks */,
				DB166DE516A1D50C00A1396C /* CoreFoundation.framework in Frameworks */,
				DB166DE716A1D50C00A1396C /* AudioUnit.framework in Frameworks */,
				DB166DE816A1D50C00A1396C /* Carbon.framework in Frameworks */,
				DB166DE916A1D50C00A1396C /* libSDL2.a in Frameworks */,
				DB166DEA16A1D50C00A1396C /* libSDL_test.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166DF616A1D57C00A1396C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E6F203B756A0004D44E /* Metal.framework in Frameworks */,
				FA73674419A54B06004122E4 /* CoreVideo.framework in Frameworks */,
				DB166DF716A1D57C00A1396C /* Cocoa.framework in Frameworks */,
				DB166DF816A1D57C00A1396C /* CoreAudio.framework in Frameworks */,
				DB166DF916A1D57C00A1396C /* ForceFeedback.framework in Frameworks */,
				DB166DFA16A1D57C00A1396C /* IOKit.framework in Frameworks */,
				DB166DFB16A1D57C00A1396C /* AudioToolbox.framework in Frameworks */,
				DB166DFC16A1D57C00A1396C /* CoreFoundation.framework in Frameworks */,
				DB166DFE16A1D57C00A1396C /* AudioUnit.framework in Frameworks */,
				DB166DFF16A1D57C00A1396C /* Carbon.framework in Frameworks */,
				DB166E0016A1D57C00A1396C /* libSDL2.a in Frameworks */,
				DB166E0116A1D57C00A1396C /* libSDL_test.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E0D16A1D5AD00A1396C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E6E203B75620004D44E /* Metal.framework in Frameworks */,
				FA73674519A54B09004122E4 /* CoreVideo.framework in Frameworks */,
				DB166E0E16A1D5AD00A1396C /* Cocoa.framework in Frameworks */,
				DB166E0F16A1D5AD00A1396C /* CoreAudio.framework in Frameworks */,
				DB166E1016A1D5AD00A1396C /* ForceFeedback.framework in Frameworks */,
				DB166E1116A1D5AD00A1396C /* IOKit.framework in Frameworks */,
				DB166E1216A1D5AD00A1396C /* AudioToolbox.framework in Frameworks */,
				DB166E1316A1D5AD00A1396C /* CoreFoundation.framework in Frameworks */,
				DB166E1516A1D5AD00A1396C /* AudioUnit.framework in Frameworks */,
				DB166E1616A1D5AD00A1396C /* Carbon.framework in Frameworks */,
				DB166E1716A1D5AD00A1396C /* libSDL2.a in Frameworks */,
				DB166E1816A1D5AD00A1396C /* libSDL_test.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E2A16A1D64D00A1396C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E6C203B75540004D44E /* Metal.framework in Frameworks */,
				FA73674719A54B0F004122E4 /* CoreVideo.framework in Frameworks */,
				DB166E2B16A1D64D00A1396C /* Cocoa.framework in Frameworks */,
				DB166E2C16A1D64D00A1396C /* CoreAudio.framework in Frameworks */,
				DB166E2D16A1D64D00A1396C /* ForceFeedback.framework in Frameworks */,
				DB166E2E16A1D64D00A1396C /* IOKit.framework in Frameworks */,
				DB166E2F16A1D64D00A1396C /* AudioToolbox.framework in Frameworks */,
				DB166E3016A1D64D00A1396C /* CoreFoundation.framework in Frameworks */,
				DB166E3216A1D64D00A1396C /* AudioUnit.framework in Frameworks */,
				DB166E3316A1D64D00A1396C /* Carbon.framework in Frameworks */,
				DB166E3416A1D64D00A1396C /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E4016A1D69000A1396C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E6B203B754C0004D44E /* Metal.framework in Frameworks */,
				FA73674819A54B13004122E4 /* CoreVideo.framework in Frameworks */,
				DB166E4116A1D69000A1396C /* Cocoa.framework in Frameworks */,
				DB166E4216A1D69000A1396C /* CoreAudio.framework in Frameworks */,
				DB166E4316A1D69000A1396C /* ForceFeedback.framework in Frameworks */,
				DB166E4416A1D69000A1396C /* IOKit.framework in Frameworks */,
				DB166E4516A1D69000A1396C /* AudioToolbox.framework in Frameworks */,
				DB166E4616A1D69000A1396C /* CoreFoundation.framework in Frameworks */,
				DB166E4816A1D69000A1396C /* AudioUnit.framework in Frameworks */,
				DB166E4916A1D69000A1396C /* Carbon.framework in Frameworks */,
				DB166E4A16A1D69000A1396C /* libSDL2.a in Frameworks */,
				DB166E4B16A1D69000A1396C /* libSDL_test.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E5A16A1D6F300A1396C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E69203B75390004D44E /* Metal.framework in Frameworks */,
				FA73674A19A54B19004122E4 /* CoreVideo.framework in Frameworks */,
				DB166E5B16A1D6F300A1396C /* Cocoa.framework in Frameworks */,
				DB166E5C16A1D6F300A1396C /* CoreAudio.framework in Frameworks */,
				DB166E5D16A1D6F300A1396C /* ForceFeedback.framework in Frameworks */,
				DB166E5E16A1D6F300A1396C /* IOKit.framework in Frameworks */,
				DB166E5F16A1D6F300A1396C /* AudioToolbox.framework in Frameworks */,
				DB166E6016A1D6F300A1396C /* CoreFoundation.framework in Frameworks */,
				DB166E6216A1D6F300A1396C /* AudioUnit.framework in Frameworks */,
				DB166E6316A1D6F300A1396C /* Carbon.framework in Frameworks */,
				DB166E6416A1D6F300A1396C /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E7016A1D78400A1396C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E66203B75140004D44E /* Metal.framework in Frameworks */,
				FA73674D19A54B22004122E4 /* CoreVideo.framework in Frameworks */,
				DB166E7116A1D78400A1396C /* Cocoa.framework in Frameworks */,
				DB166E7216A1D78400A1396C /* CoreAudio.framework in Frameworks */,
				DB166E7316A1D78400A1396C /* ForceFeedback.framework in Frameworks */,
				DB166E7416A1D78400A1396C /* IOKit.framework in Frameworks */,
				DB166E7516A1D78400A1396C /* AudioToolbox.framework in Frameworks */,
				DB166E7616A1D78400A1396C /* CoreFoundation.framework in Frameworks */,
				DB166E7816A1D78400A1396C /* AudioUnit.framework in Frameworks */,
				DB166E7916A1D78400A1396C /* Carbon.framework in Frameworks */,
				DB166E7A16A1D78400A1396C /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E8316A1D78C00A1396C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E65203B74EC0004D44E /* Metal.framework in Frameworks */,
				FA73674E19A54B25004122E4 /* CoreVideo.framework in Frameworks */,
				DB166E8416A1D78C00A1396C /* Cocoa.framework in Frameworks */,
				DB166E8516A1D78C00A1396C /* CoreAudio.framework in Frameworks */,
				DB166E8616A1D78C00A1396C /* ForceFeedback.framework in Frameworks */,
				DB166E8716A1D78C00A1396C /* IOKit.framework in Frameworks */,
				DB166E8816A1D78C00A1396C /* AudioToolbox.framework in Frameworks */,
				DB166E8916A1D78C00A1396C /* CoreFoundation.framework in Frameworks */,
				DB166E8B16A1D78C00A1396C /* AudioUnit.framework in Frameworks */,
				DB166E8C16A1D78C00A1396C /* Carbon.framework in Frameworks */,
				DB166E8D16A1D78C00A1396C /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB445EE918184B7000B306B0 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E86203B76340004D44E /* Metal.framework in Frameworks */,
				FA73672D19A54AC7004122E4 /* CoreVideo.framework in Frameworks */,
				DB445EEA18184B7000B306B0 /* Cocoa.framework in Frameworks */,
				DB445EEB18184B7000B306B0 /* CoreAudio.framework in Frameworks */,
				DB445EEC18184B7000B306B0 /* ForceFeedback.framework in Frameworks */,
				DB445EED18184B7000B306B0 /* IOKit.framework in Frameworks */,
				DB445EEE18184B7000B306B0 /* AudioToolbox.framework in Frameworks */,
				DB445EEF18184B7000B306B0 /* CoreFoundation.framework in Frameworks */,
				DB445EF118184B7000B306B0 /* AudioUnit.framework in Frameworks */,
				DB445EF218184B7000B306B0 /* Carbon.framework in Frameworks */,
				DB445EF318184B7000B306B0 /* libSDL2.a in Frameworks */,
				DB445EF418184B7000B306B0 /* libSDL_test.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB89957018A19ABA0092407C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E7E203B75F90004D44E /* Metal.framework in Frameworks */,
				FA73673519A54ADE004122E4 /* CoreVideo.framework in Frameworks */,
				DB89957118A19ABA0092407C /* Cocoa.framework in Frameworks */,
				DB89957218A19ABA0092407C /* CoreAudio.framework in Frameworks */,
				DB89957318A19ABA0092407C /* ForceFeedback.framework in Frameworks */,
				DB89957418A19ABA0092407C /* IOKit.framework in Frameworks */,
				DB89957518A19ABA0092407C /* AudioToolbox.framework in Frameworks */,
				DB89957618A19ABA0092407C /* CoreFoundation.framework in Frameworks */,
				DB89957818A19ABA0092407C /* AudioUnit.framework in Frameworks */,
				DB89957918A19ABA0092407C /* Carbon.framework in Frameworks */,
				DB89957A18A19ABA0092407C /* libSDL2.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DBEC54DC1A1A81C3005B1EAB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				66E88E5D203B73530004D44E /* Metal.framework in Frameworks */,
				DBEC54DD1A1A81C3005B1EAB /* CoreVideo.framework in Frameworks */,
				DBEC54DE1A1A81C3005B1EAB /* Cocoa.framework in Frameworks */,
				DBEC54DF1A1A81C3005B1EAB /* libSDL2.a in Frameworks */,
				DBEC54E01A1A81C3005B1EAB /* CoreAudio.framework in Frameworks */,
				DBEC54E11A1A81C3005B1EAB /* ForceFeedback.framework in Frameworks */,
				DBEC54E21A1A81C3005B1EAB /* IOKit.framework in Frameworks */,
				DBEC54E31A1A81C3005B1EAB /* AudioToolbox.framework in Frameworks */,
				DBEC54E41A1A81C3005B1EAB /* CoreFoundation.framework in Frameworks */,
				DBEC54E51A1A81C3005B1EAB /* AudioUnit.framework in Frameworks */,
				DBEC54E61A1A81C3005B1EAB /* Carbon.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		002F33A209CA183B00EBEB88 /* Linked Frameworks */ = {
			isa = PBXGroup;
			children = (
				FA73672219A54A90004122E4 /* CoreVideo.framework */,
				002A869F10730593007319AE /* AudioToolbox.framework */,
				002A871410730623007319AE /* AudioUnit.framework */,
				002A873910730675007319AE /* Carbon.framework */,
				002F33A709CA188600EBEB88 /* Cocoa.framework */,
				002A863B10730545007319AE /* CoreAudio.framework */,
				002A86A010730593007319AE /* CoreFoundation.framework */,
				002A863C10730545007319AE /* ForceFeedback.framework */,
				002A863D10730545007319AE /* IOKit.framework */,
			);
			name = "Linked Frameworks";
			sourceTree = "<group>";
		};
		003FA63B093FFD41000C53B3 /* Products */ = {
			isa = PBXGroup;
			children = (
				003FA643093FFD41000C53B3 /* SDL2.framework */,
				003FA645093FFD41000C53B3 /* libSDL2.a */,
				DB1D40D717B3F30D00D74CFC /* libSDL2.dylib */,
				003FA649093FFD41000C53B3 /* Standard DMG */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		00794E4609D207B4003FC8A1 /* Resources */ = {
			isa = PBXGroup;
			children = (
				DBEC54D61A1A8145005B1EAB /* axis.bmp */,
				DBEC54D71A1A8145005B1EAB /* button.bmp */,
				DBEC54D81A1A8145005B1EAB /* controllermap.bmp */,
				00794E5D09D20839003FC8A1 /* icon.bmp */,
				00794E5E09D20839003FC8A1 /* moose.dat */,
				00794E5F09D20839003FC8A1 /* picture.xbm */,
				00794E6109D20839003FC8A1 /* sample.bmp */,
				00794E6209D20839003FC8A1 /* sample.wav */,
				DB166ECF16A1D87000A1396C /* shapes */,
				DBBC552C182831D700F3CA8D /* TestDropFile-Info.plist */,
				00794E6309D20839003FC8A1 /* utf8.txt */,
			);
			name = Resources;
			path = ../../test;
			sourceTree = "<group>";
		};
		08FB7794FE84155DC02AAC07 /* SDLTest */ = {
			isa = PBXGroup;
			children = (
				003FA63A093FFD41000C53B3 /* SDL.xcodeproj */,
				08FB7795FE84155DC02AAC07 /* Source */,
				DB166D8316A1D17E00A1396C /* SDL_Test */,
				002F33A209CA183B00EBEB88 /* Linked Frameworks */,
				00794E4609D207B4003FC8A1 /* Resources */,
				1AB674ADFE9D54B511CA2CBB /* Products */,
				66E88E56203B733C0004D44E /* Frameworks */,
			);
			comments = "I made these tests link against our \"default\" framework which includes X11 stuff. If you didn't install the X11 headers with Xcode, you might have problems building the SDL.framework (which is a dependency). You can swap the dependencies around to get around this, or you can modify the default SDL.framework target to not include X11 stuff. (Go into its target build options and remove all the Preprocessor macros.)\n\n\n\nWe are sort of in a half-way state at the moment. Going \"all-the-way\" means we copy the SDL.framework inside the app bundle so we can run the test without the step of the user \"installing\" the framework. But there is an oversight/bug in Xcode that doesn't correctly find the location of the framework when in an embedded/nested Xcode project. We could probably try to hack this with a shell script that checks multiple directories for existence, but this is messier and more work than I prefer, so I rather just wait for Apple to fix this. In the meantime...\n\nThe \"All\" target will build the SDL framework from the Xcode project. The other targets do not have this dependency set (for flexibility reasons in case we make changes). If you have not built the framework, you will probably be unable to link. You will either need to build the framework, or you need to add \"-framework SDL\" to the link options and make sure you have the SDL.framework installed somewhere where it can be seen (like /Library/Frameworks...I think we already set this one up.) \n\nTo run though, you should have a copy of the SDL.framework in /Library/Frameworks or ~/Library/Frameworks.\n\n\n\n\ntestgl and testdyngl need -DHAVE_OPENGL\ntestgl needs to link against OpenGL.framework\n\n";
			name = SDLTest;
			sourceTree = "<group>";
		};
		08FB7795FE84155DC02AAC07 /* Source */ = {
			isa = PBXGroup;
			children = (
				092D6D10FFB30A2C7F000001 /* checkkeys.c */,
				DBEC54D11A1A811D005B1EAB /* controllermap.c */,
				083E4872006D84C97F000001 /* loopwave.c */,
				0017958F1074216E00F5D044 /* testatomic.c */,
				001795B01074222D00F5D044 /* testaudioinfo.c */,
				001797711074320D00F5D044 /* testdraw2.c */,
				DB0F48D717CA51D2008798C5 /* testdrawchessboard.c */,
				DB445EFA18184BB600B306B0 /* testdropfile.c */,
				083E4878006D85357F000001 /* testerror.c */,
				002F341709CA1C5B00EBEB88 /* testfile.c */,
				DB0F48D817CA51D2008798C5 /* testfilesystem.c */,
				BBFC088E164C6820003E6A99 /* testgamecontroller.c */,
				DB166CBB16A1C74100A1396C /* testgesture.c */,
				0017972710742FB900F5D044 /* testgl2.c */,
				DB166CBC16A1C74100A1396C /* testgles.c */,
				0017974E1074315700F5D044 /* testhaptic.c */,
				DB89958318A19B130092407C /* testhotplug.c */,
				002F343609CA1F6F00EBEB88 /* testiconv.c */,
				00179791107432FA00F5D044 /* testime.c */,
				001797B31074339C00F5D044 /* testintersections.c */,
				092D6D62FFB312AA7F000001 /* testjoystick.c */,
				092D6D6CFFB313437F000001 /* testkeys.c */,
				001797D31074343E00F5D044 /* testloadso.c */,
				092D6D75FFB313BB7F000001 /* testlock.c */,
				DB166CBD16A1C74100A1396C /* testmessage.c */,
				001798151074359B00F5D044 /* testmultiaudio.c */,
				0017985A107436ED00F5D044 /* testnative.c */,
				0017985B107436ED00F5D044 /* testnative.h */,
				0017985C107436ED00F5D044 /* testnativecocoa.m */,
				00179872107438D000F5D044 /* testnativex11.c */,
				002F345209CA201C00EBEB88 /* testoverlay2.c */,
				66E88E8A203B778F0004D44E /* testyuv_cvt.c */,
				002F346F09CA20A600EBEB88 /* testplatform.c */,
				001798B910743A4900F5D044 /* testpower.c */,
				DB166CBF16A1C74100A1396C /* testrelative.c */,
				DB166CC016A1C74100A1396C /* testrendercopyex.c */,
				DB166CC116A1C74100A1396C /* testrendertarget.c */,
				001798F910743E9200F5D044 /* testresample.c */,
				DB166CC216A1C74100A1396C /* testrumble.c */,
				DB166CC316A1C74100A1396C /* testscale.c */,
				083E487E006D86A17F000001 /* testsem.c */,
				DB166CC416A1C74100A1396C /* testshader.c */,
				453774A4120915E3002F0F45 /* testshape.c */,
				0017991910743F5300F5D044 /* testsprite2.c */,
				DB166CC516A1C74100A1396C /* testspriteminimal.c */,
				DB166CC616A1C74100A1396C /* teststreaming.c */,
				092D6D58FFB311A97F000001 /* testthread.c */,
				083E4880006D86A17F000001 /* testtimer.c */,
				083E4882006D86A17F000001 /* testver.c */,
				0017993B10743FEF00F5D044 /* testwm2.c */,
				083E4887006D86A17F000001 /* torturethread.c */,
			);
			name = Source;
			path = ../../test;
			sourceTree = "<group>";
		};
		1AB674ADFE9D54B511CA2CBB /* Products */ = {
			isa = PBXGroup;
			children = (
				BEC566B60761D90300A33029 /* checkkeys */,
				BEC566D10761D90300A33029 /* loopwave */,
				BEC567060761D90400A33029 /* testerror */,
				BEC5672E0761D90400A33029 /* testthread */,
				BEC5673B0761D90400A33029 /* testjoystick */,
				BEC567480761D90400A33029 /* testkeys */,
				BEC567550761D90400A33029 /* testlock */,
				BEC5677D0761D90500A33029 /* testsem */,
				BEC567980761D90500A33029 /* testtimer */,
				BEC567B20761D90500A33029 /* testversion */,
				BEC567F50761D90600A33029 /* torturethread */,
				002F341209CA1BFF00EBEB88 /* testfile */,
				002F343109CA1F0300EBEB88 /* testiconv */,
				002F344D09CA1FB300EBEB88 /* testoverlay2 */,
				002F346A09CA204F00EBEB88 /* testplatform */,
				0017958C10741F7900F5D044 /* testatomic */,
				001795AD107421BF00F5D044 /* testaudioinfo */,
				0017972110742F3200F5D044 /* testgl2 */,
				00179748107430D600F5D044 /* testhaptic */,
				0017976E107431B300F5D044 /* testdraw2 */,
				0017978E107432AE00F5D044 /* testime */,
				001797AE1074334C00F5D044 /* testintersections */,
				001797D0107433C600F5D044 /* testloadso */,
				001798121074355200F5D044 /* testmultiaudio */,
				001798941074392D00F5D044 /* testnative */,
				001798B5107439DF00F5D044 /* testpower */,
				001798F210743BEC00F5D044 /* testresample */,
				0017991610743F1000F5D044 /* testsprite2 */,
				0017993810743FB700F5D044 /* testwm2 */,
				4537749212091504002F0F45 /* testshape */,
				BBFC08CD164C6862003E6A99 /* testgamecontroller */,
				DB166D7F16A1D12400A1396C /* libSDL_test.a */,
				DB166DBF16A1D2F600A1396C /* testgesture */,
				DB166DD516A1D36A00A1396C /* testmessage */,
				DB166DEE16A1D50C00A1396C /* testrelative */,
				DB166E0516A1D57C00A1396C /* testrendercopyex */,
				DB166E1C16A1D5AD00A1396C /* testrendertarget */,
				DB166E3816A1D64D00A1396C /* testrumble */,
				DB166E5216A1D69000A1396C /* testscale */,
				DB166E6816A1D6F300A1396C /* testshader */,
				DB166E7E16A1D78400A1396C /* testspriteminimal */,
				DB166E9116A1D78C00A1396C /* teststreaming */,
				DB0F48EC17CA51E5008798C5 /* testdrawchessboard */,
				DB0F490117CA5212008798C5 /* testfilesystem */,
				DB89957E18A19ABA0092407C /* testhotplug */,
				DB445EF818184B7000B306B0 /* testdropfile.app */,
				DBEC54EA1A1A81C3005B1EAB /* controllermap */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		66E88E56203B733C0004D44E /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				66E88E5B203B733C0004D44E /* Metal.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		DB166D8316A1D17E00A1396C /* SDL_Test */ = {
			isa = PBXGroup;
			children = (
				DB166D8416A1D1A500A1396C /* SDL_test_assert.c */,
				DB166D8516A1D1A500A1396C /* SDL_test_common.c */,
				DB166D8616A1D1A500A1396C /* SDL_test_compare.c */,
				DB166D8716A1D1A500A1396C /* SDL_test_crc32.c */,
				DB166D8816A1D1A500A1396C /* SDL_test_font.c */,
				DB166D8916A1D1A500A1396C /* SDL_test_fuzzer.c */,
				DB166D8A16A1D1A500A1396C /* SDL_test_harness.c */,
				DB166D8B16A1D1A500A1396C /* SDL_test_imageBlit.c */,
				DB166D8C16A1D1A500A1396C /* SDL_test_imageBlitBlend.c */,
				DB166D8D16A1D1A500A1396C /* SDL_test_imageFace.c */,
				DB166D8E16A1D1A500A1396C /* SDL_test_imagePrimitives.c */,
				DB166D8F16A1D1A500A1396C /* SDL_test_imagePrimitivesBlend.c */,
				DB166D9016A1D1A500A1396C /* SDL_test_log.c */,
				DB166D9116A1D1A500A1396C /* SDL_test_md5.c */,
				AAF02FF41F90089800B9A9FB /* SDL_test_memory.c */,
				DB166D9216A1D1A500A1396C /* SDL_test_random.c */,
			);
			name = SDL_Test;
			path = ../../src/test;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		DB166D7D16A1D12400A1396C /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		0017957410741F7900F5D044 /* testatomic */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0017958610741F7900F5D044 /* Build configuration list for PBXNativeTarget "testatomic" */;
			buildPhases = (
				0017957910741F7900F5D044 /* Sources */,
				0017957A10741F7900F5D044 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testatomic;
			productName = testalpha;
			productReference = 0017958C10741F7900F5D044 /* testatomic */;
			productType = "com.apple.product-type.tool";
		};
		00179595107421BF00F5D044 /* testaudioinfo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001795A7107421BF00F5D044 /* Build configuration list for PBXNativeTarget "testaudioinfo" */;
			buildPhases = (
				0017959A107421BF00F5D044 /* Sources */,
				0017959B107421BF00F5D044 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testaudioinfo;
			productName = testalpha;
			productReference = 001795AD107421BF00F5D044 /* testaudioinfo */;
			productType = "com.apple.product-type.tool";
		};
		0017970910742F3200F5D044 /* testgl2 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0017971B10742F3200F5D044 /* Build configuration list for PBXNativeTarget "testgl2" */;
			buildPhases = (
				0017970E10742F3200F5D044 /* Sources */,
				0017970F10742F3200F5D044 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testgl2;
			productName = testalpha;
			productReference = 0017972110742F3200F5D044 /* testgl2 */;
			productType = "com.apple.product-type.tool";
		};
		00179730107430D600F5D044 /* testhaptic */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00179742107430D600F5D044 /* Build configuration list for PBXNativeTarget "testhaptic" */;
			buildPhases = (
				00179735107430D600F5D044 /* Sources */,
				00179736107430D600F5D044 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testhaptic;
			productName = testalpha;
			productReference = 00179748107430D600F5D044 /* testhaptic */;
			productType = "com.apple.product-type.tool";
		};
		00179756107431B300F5D044 /* testdraw2 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00179768107431B300F5D044 /* Build configuration list for PBXNativeTarget "testdraw2" */;
			buildPhases = (
				0017975B107431B300F5D044 /* Sources */,
				0017975C107431B300F5D044 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testdraw2;
			productName = testalpha;
			productReference = 0017976E107431B300F5D044 /* testdraw2 */;
			productType = "com.apple.product-type.tool";
		};
		00179776107432AE00F5D044 /* testime */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00179788107432AE00F5D044 /* Build configuration list for PBXNativeTarget "testime" */;
			buildPhases = (
				0017977B107432AE00F5D044 /* Sources */,
				0017977C107432AE00F5D044 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testime;
			productName = testalpha;
			productReference = 0017978E107432AE00F5D044 /* testime */;
			productType = "com.apple.product-type.tool";
		};
		001797961074334C00F5D044 /* testintersections */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001797A81074334C00F5D044 /* Build configuration list for PBXNativeTarget "testintersections" */;
			buildPhases = (
				0017979B1074334C00F5D044 /* Sources */,
				0017979C1074334C00F5D044 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testintersections;
			productName = testalpha;
			productReference = 001797AE1074334C00F5D044 /* testintersections */;
			productType = "com.apple.product-type.tool";
		};
		001797B8107433C600F5D044 /* testloadso */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001797CA107433C600F5D044 /* Build configuration list for PBXNativeTarget "testloadso" */;
			buildPhases = (
				001797BD107433C600F5D044 /* Sources */,
				001797BE107433C600F5D044 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testloadso;
			productName = testalpha;
			productReference = 001797D0107433C600F5D044 /* testloadso */;
			productType = "com.apple.product-type.tool";
		};
		001797FA1074355200F5D044 /* testmultiaudio */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0017980C1074355200F5D044 /* Build configuration list for PBXNativeTarget "testmultiaudio" */;
			buildPhases = (
				001797FF1074355200F5D044 /* Sources */,
				001798001074355200F5D044 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testmultiaudio;
			productName = testalpha;
			productReference = 001798121074355200F5D044 /* testmultiaudio */;
			productType = "com.apple.product-type.tool";
		};
		001798781074392D00F5D044 /* testnative */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0017988E1074392D00F5D044 /* Build configuration list for PBXNativeTarget "testnative" */;
			buildPhases = (
				0017987E1074392D00F5D044 /* Sources */,
				001798821074392D00F5D044 /* Frameworks */,
				DB166DDA16A1D40F00A1396C /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testnative;
			productName = testalpha;
			productReference = 001798941074392D00F5D044 /* testnative */;
			productType = "com.apple.product-type.tool";
		};
		0017989D107439DF00F5D044 /* testpower */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001798AF107439DF00F5D044 /* Build configuration list for PBXNativeTarget "testpower" */;
			buildPhases = (
				001798A2107439DF00F5D044 /* Sources */,
				001798A3107439DF00F5D044 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testpower;
			productName = testalpha;
			productReference = 001798B5107439DF00F5D044 /* testpower */;
			productType = "com.apple.product-type.tool";
		};
		001798DA10743BEC00F5D044 /* testresample */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001798EC10743BEC00F5D044 /* Build configuration list for PBXNativeTarget "testresample" */;
			buildPhases = (
				001798DF10743BEC00F5D044 /* Sources */,
				001798E010743BEC00F5D044 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testresample;
			productName = testalpha;
			productReference = 001798F210743BEC00F5D044 /* testresample */;
			productType = "com.apple.product-type.tool";
		};
		001798FE10743F1000F5D044 /* testsprite2 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0017991010743F1000F5D044 /* Build configuration list for PBXNativeTarget "testsprite2" */;
			buildPhases = (
				0017990310743F1000F5D044 /* Sources */,
				0017990410743F1000F5D044 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testsprite2;
			productName = testalpha;
			productReference = 0017991610743F1000F5D044 /* testsprite2 */;
			productType = "com.apple.product-type.tool";
		};
		0017992010743FB700F5D044 /* testwm2 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0017993210743FB700F5D044 /* Build configuration list for PBXNativeTarget "testwm2" */;
			buildPhases = (
				0017992510743FB700F5D044 /* Sources */,
				0017992610743FB700F5D044 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testwm2;
			productName = testalpha;
			productReference = 0017993810743FB700F5D044 /* testwm2 */;
			productType = "com.apple.product-type.tool";
		};
		002F340109CA1BFF00EBEB88 /* testfile */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 002F340E09CA1BFF00EBEB88 /* Build configuration list for PBXNativeTarget "testfile" */;
			buildPhases = (
				002F340709CA1BFF00EBEB88 /* Sources */,
				002F340809CA1BFF00EBEB88 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testfile;
			productName = testalpha;
			productReference = 002F341209CA1BFF00EBEB88 /* testfile */;
			productType = "com.apple.product-type.tool";
		};
		002F342009CA1F0300EBEB88 /* testiconv */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 002F342D09CA1F0300EBEB88 /* Build configuration list for PBXNativeTarget "testiconv" */;
			buildPhases = (
				002F342609CA1F0300EBEB88 /* Sources */,
				002F342709CA1F0300EBEB88 /* Frameworks */,
				00794EEC09D2371F003FC8A1 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testiconv;
			productName = testalpha;
			productReference = 002F343109CA1F0300EBEB88 /* testiconv */;
			productType = "com.apple.product-type.tool";
		};
		002F343C09CA1FB300EBEB88 /* testoverlay2 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 002F344909CA1FB300EBEB88 /* Build configuration list for PBXNativeTarget "testoverlay2" */;
			buildPhases = (
				002F344209CA1FB300EBEB88 /* Sources */,
				002F344309CA1FB300EBEB88 /* Frameworks */,
				00794EF409D237C7003FC8A1 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testoverlay2;
			productName = testalpha;
			productReference = 002F344D09CA1FB300EBEB88 /* testoverlay2 */;
			productType = "com.apple.product-type.tool";
		};
		002F345909CA204F00EBEB88 /* testplatform */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 002F346609CA204F00EBEB88 /* Build configuration list for PBXNativeTarget "testplatform" */;
			buildPhases = (
				002F345F09CA204F00EBEB88 /* Sources */,
				002F346009CA204F00EBEB88 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testplatform;
			productName = testalpha;
			productReference = 002F346A09CA204F00EBEB88 /* testplatform */;
			productType = "com.apple.product-type.tool";
		};
		4537749112091504002F0F45 /* testshape */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4537749A1209150C002F0F45 /* Build configuration list for PBXNativeTarget "testshape" */;
			buildPhases = (
				4537748F12091504002F0F45 /* Sources */,
				4537749012091504002F0F45 /* Frameworks */,
				DB166ECE16A1D85400A1396C /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testshape;
			productName = testshape;
			productReference = 4537749212091504002F0F45 /* testshape */;
			productType = "com.apple.product-type.tool";
		};
		BBFC08B7164C6862003E6A99 /* testgamecontroller */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = BBFC08CA164C6862003E6A99 /* Build configuration list for PBXNativeTarget "testgamecontroller" */;
			buildPhases = (
				BBFC08BC164C6862003E6A99 /* Sources */,
				BBFC08BE164C6862003E6A99 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testgamecontroller;
			productName = testjoystick;
			productReference = BBFC08CD164C6862003E6A99 /* testgamecontroller */;
			productType = "com.apple.product-type.tool";
		};
		BEC566AB0761D90300A33029 /* checkkeys */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001B593808BDB826006539E9 /* Build configuration list for PBXNativeTarget "checkkeys" */;
			buildPhases = (
				BEC566B00761D90300A33029 /* Sources */,
				BEC566B20761D90300A33029 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = checkkeys;
			productName = checkkeys;
			productReference = BEC566B60761D90300A33029 /* checkkeys */;
			productType = "com.apple.product-type.tool";
		};
		BEC566C50761D90300A33029 /* loopwave */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001B594008BDB826006539E9 /* Build configuration list for PBXNativeTarget "loopwave" */;
			buildPhases = (
				BEC566CA0761D90300A33029 /* Sources */,
				BEC566CC0761D90300A33029 /* Frameworks */,
				00794E6409D2084F003FC8A1 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = loopwave;
			productName = loopwave;
			productReference = BEC566D10761D90300A33029 /* loopwave */;
			productType = "com.apple.product-type.tool";
		};
		BEC566FB0761D90300A33029 /* testerror */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001B595008BDB826006539E9 /* Build configuration list for PBXNativeTarget "testerror" */;
			buildPhases = (
				BEC567000761D90300A33029 /* Sources */,
				BEC567020761D90300A33029 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testerror;
			productName = testerror;
			productReference = BEC567060761D90400A33029 /* testerror */;
			productType = "com.apple.product-type.tool";
		};
		BEC567230761D90400A33029 /* testthread */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001B595C08BDB826006539E9 /* Build configuration list for PBXNativeTarget "testthread" */;
			buildPhases = (
				BEC567280761D90400A33029 /* Sources */,
				BEC5672A0761D90400A33029 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testthread;
			productName = testthread;
			productReference = BEC5672E0761D90400A33029 /* testthread */;
			productType = "com.apple.product-type.tool";
		};
		BEC567300761D90400A33029 /* testjoystick */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001B596008BDB826006539E9 /* Build configuration list for PBXNativeTarget "testjoystick" */;
			buildPhases = (
				BEC567350761D90400A33029 /* Sources */,
				BEC567370761D90400A33029 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testjoystick;
			productName = testjoystick;
			productReference = BEC5673B0761D90400A33029 /* testjoystick */;
			productType = "com.apple.product-type.tool";
		};
		BEC5673D0761D90400A33029 /* testkeys */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001B596408BDB826006539E9 /* Build configuration list for PBXNativeTarget "testkeys" */;
			buildPhases = (
				BEC567420761D90400A33029 /* Sources */,
				BEC567440761D90400A33029 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testkeys;
			productName = testkeys;
			productReference = BEC567480761D90400A33029 /* testkeys */;
			productType = "com.apple.product-type.tool";
		};
		BEC5674A0761D90400A33029 /* testlock */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001B596808BDB826006539E9 /* Build configuration list for PBXNativeTarget "testlock" */;
			buildPhases = (
				BEC5674F0761D90400A33029 /* Sources */,
				BEC567510761D90400A33029 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testlock;
			productName = testlock;
			productReference = BEC567550761D90400A33029 /* testlock */;
			productType = "com.apple.product-type.tool";
		};
		BEC567720761D90500A33029 /* testsem */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001B597008BDB826006539E9 /* Build configuration list for PBXNativeTarget "testsem" */;
			buildPhases = (
				BEC567770761D90500A33029 /* Sources */,
				BEC567790761D90500A33029 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testsem;
			productName = testsem;
			productReference = BEC5677D0761D90500A33029 /* testsem */;
			productType = "com.apple.product-type.tool";
		};
		BEC5678D0761D90500A33029 /* testtimer */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001B597808BDB826006539E9 /* Build configuration list for PBXNativeTarget "testtimer" */;
			buildPhases = (
				BEC567920761D90500A33029 /* Sources */,
				BEC567940761D90500A33029 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testtimer;
			productName = testtimer;
			productReference = BEC567980761D90500A33029 /* testtimer */;
			productType = "com.apple.product-type.tool";
		};
		BEC567A70761D90500A33029 /* testversion */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001B598008BDB826006539E9 /* Build configuration list for PBXNativeTarget "testversion" */;
			buildPhases = (
				BEC567AC0761D90500A33029 /* Sources */,
				BEC567AE0761D90500A33029 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testversion;
			productName = testversion;
			productReference = BEC567B20761D90500A33029 /* testversion */;
			productType = "com.apple.product-type.tool";
		};
		BEC567EA0761D90600A33029 /* torturethread */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 001B599408BDB826006539E9 /* Build configuration list for PBXNativeTarget "torturethread" */;
			buildPhases = (
				BEC567EF0761D90600A33029 /* Sources */,
				BEC567F10761D90600A33029 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = torturethread;
			productName = torturethread;
			productReference = BEC567F50761D90600A33029 /* torturethread */;
			productType = "com.apple.product-type.tool";
		};
		DB0F48D917CA51E5008798C5 /* testdrawchessboard */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB0F48E917CA51E5008798C5 /* Build configuration list for PBXNativeTarget "testdrawchessboard" */;
			buildPhases = (
				DB0F48DA17CA51E5008798C5 /* Sources */,
				DB0F48DC17CA51E5008798C5 /* Frameworks */,
				DB0F48E717CA51E5008798C5 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testdrawchessboard;
			productName = testalpha;
			productReference = DB0F48EC17CA51E5008798C5 /* testdrawchessboard */;
			productType = "com.apple.product-type.tool";
		};
		DB0F48EF17CA5212008798C5 /* testfilesystem */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB0F48FE17CA5212008798C5 /* Build configuration list for PBXNativeTarget "testfilesystem" */;
			buildPhases = (
				DB0F48F017CA5212008798C5 /* Sources */,
				DB0F48F217CA5212008798C5 /* Frameworks */,
				DB0F48FD17CA5212008798C5 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testfilesystem;
			productName = testalpha;
			productReference = DB0F490117CA5212008798C5 /* testfilesystem */;
			productType = "com.apple.product-type.tool";
		};
		DB166D7E16A1D12400A1396C /* SDL_test */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB166D8016A1D12400A1396C /* Build configuration list for PBXNativeTarget "SDL_test" */;
			buildPhases = (
				DB166D7B16A1D12400A1396C /* Sources */,
				DB166D7C16A1D12400A1396C /* Frameworks */,
				DB166D7D16A1D12400A1396C /* Headers */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SDL_test;
			productName = SDL_test;
			productReference = DB166D7F16A1D12400A1396C /* libSDL_test.a */;
			productType = "com.apple.product-type.library.static";
		};
		DB166DAD16A1D2F600A1396C /* testgesture */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB166DBC16A1D2F600A1396C /* Build configuration list for PBXNativeTarget "testgesture" */;
			buildPhases = (
				DB166DAE16A1D2F600A1396C /* Sources */,
				DB166DB016A1D2F600A1396C /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testgesture;
			productName = testalpha;
			productReference = DB166DBF16A1D2F600A1396C /* testgesture */;
			productType = "com.apple.product-type.tool";
		};
		DB166DC416A1D36A00A1396C /* testmessage */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB166DD216A1D36A00A1396C /* Build configuration list for PBXNativeTarget "testmessage" */;
			buildPhases = (
				DB166DC516A1D36A00A1396C /* Sources */,
				DB166DC716A1D36A00A1396C /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testmessage;
			productName = testalpha;
			productReference = DB166DD516A1D36A00A1396C /* testmessage */;
			productType = "com.apple.product-type.tool";
		};
		DB166DDC16A1D50C00A1396C /* testrelative */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB166DEB16A1D50C00A1396C /* Build configuration list for PBXNativeTarget "testrelative" */;
			buildPhases = (
				DB166DDD16A1D50C00A1396C /* Sources */,
				DB166DDF16A1D50C00A1396C /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testrelative;
			productName = testalpha;
			productReference = DB166DEE16A1D50C00A1396C /* testrelative */;
			productType = "com.apple.product-type.tool";
		};
		DB166DF316A1D57C00A1396C /* testrendercopyex */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB166E0216A1D57C00A1396C /* Build configuration list for PBXNativeTarget "testrendercopyex" */;
			buildPhases = (
				DB166DF416A1D57C00A1396C /* Sources */,
				DB166DF616A1D57C00A1396C /* Frameworks */,
				DB166E2116A1D5DF00A1396C /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testrendercopyex;
			productName = testalpha;
			productReference = DB166E0516A1D57C00A1396C /* testrendercopyex */;
			productType = "com.apple.product-type.tool";
		};
		DB166E0A16A1D5AD00A1396C /* testrendertarget */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB166E1916A1D5AD00A1396C /* Build configuration list for PBXNativeTarget "testrendertarget" */;
			buildPhases = (
				DB166E0B16A1D5AD00A1396C /* Sources */,
				DB166E0D16A1D5AD00A1396C /* Frameworks */,
				DB166E2416A1D61000A1396C /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testrendertarget;
			productName = testalpha;
			productReference = DB166E1C16A1D5AD00A1396C /* testrendertarget */;
			productType = "com.apple.product-type.tool";
		};
		DB166E2716A1D64D00A1396C /* testrumble */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB166E3516A1D64D00A1396C /* Build configuration list for PBXNativeTarget "testrumble" */;
			buildPhases = (
				DB166E2816A1D64D00A1396C /* Sources */,
				DB166E2A16A1D64D00A1396C /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testrumble;
			productName = testalpha;
			productReference = DB166E3816A1D64D00A1396C /* testrumble */;
			productType = "com.apple.product-type.tool";
		};
		DB166E3D16A1D69000A1396C /* testscale */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB166E4F16A1D69000A1396C /* Build configuration list for PBXNativeTarget "testscale" */;
			buildPhases = (
				DB166E3E16A1D69000A1396C /* Sources */,
				DB166E4016A1D69000A1396C /* Frameworks */,
				DB166E4C16A1D69000A1396C /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testscale;
			productName = testalpha;
			productReference = DB166E5216A1D69000A1396C /* testscale */;
			productType = "com.apple.product-type.tool";
		};
		DB166E5716A1D6F300A1396C /* testshader */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB166E6516A1D6F300A1396C /* Build configuration list for PBXNativeTarget "testshader" */;
			buildPhases = (
				DB166E5816A1D6F300A1396C /* Sources */,
				DB166E5A16A1D6F300A1396C /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testshader;
			productName = testsem;
			productReference = DB166E6816A1D6F300A1396C /* testshader */;
			productType = "com.apple.product-type.tool";
		};
		DB166E6D16A1D78400A1396C /* testspriteminimal */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB166E7B16A1D78400A1396C /* Build configuration list for PBXNativeTarget "testspriteminimal" */;
			buildPhases = (
				DB166E6E16A1D78400A1396C /* Sources */,
				DB166E7016A1D78400A1396C /* Frameworks */,
				DB166E9B16A1D7FC00A1396C /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testspriteminimal;
			productName = testspriteminimal;
			productReference = DB166E7E16A1D78400A1396C /* testspriteminimal */;
			productType = "com.apple.product-type.tool";
		};
		DB166E8016A1D78C00A1396C /* teststreaming */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB166E8E16A1D78C00A1396C /* Build configuration list for PBXNativeTarget "teststreaming" */;
			buildPhases = (
				DB166E8116A1D78C00A1396C /* Sources */,
				DB166E8316A1D78C00A1396C /* Frameworks */,
				DB166E9916A1D7EE00A1396C /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = teststreaming;
			productName = teststreaming;
			productReference = DB166E9116A1D78C00A1396C /* teststreaming */;
			productType = "com.apple.product-type.tool";
		};
		DB445EE618184B7000B306B0 /* testdropfile */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB445EF518184B7000B306B0 /* Build configuration list for PBXNativeTarget "testdropfile" */;
			buildPhases = (
				DB445EE718184B7000B306B0 /* Sources */,
				DB445EE918184B7000B306B0 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testdropfile;
			productName = testdropfile;
			productReference = DB445EF818184B7000B306B0 /* testdropfile.app */;
			productType = "com.apple.product-type.application";
		};
		DB89956D18A19ABA0092407C /* testhotplug */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DB89957B18A19ABA0092407C /* Build configuration list for PBXNativeTarget "testhotplug" */;
			buildPhases = (
				DB89956E18A19ABA0092407C /* Sources */,
				DB89957018A19ABA0092407C /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = testhotplug;
			productName = testalpha;
			productReference = DB89957E18A19ABA0092407C /* testhotplug */;
			productType = "com.apple.product-type.tool";
		};
		DBEC54D91A1A81C3005B1EAB /* controllermap */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DBEC54E71A1A81C3005B1EAB /* Build configuration list for PBXNativeTarget "controllermap" */;
			buildPhases = (
				DBEC54DA1A1A81C3005B1EAB /* Sources */,
				DBEC54DC1A1A81C3005B1EAB /* Frameworks */,
				DBEC54EC1A1A827C005B1EAB /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = controllermap;
			productName = checkkeys;
			productReference = DBEC54EA1A1A81C3005B1EAB /* controllermap */;
			productType = "com.apple.product-type.tool";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		08FB7793FE84155DC02AAC07 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0420;
			};
			buildConfigurationList = 001B5A0C08BDB826006539E9 /* Build configuration list for PBXProject "SDLTest" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 1;
			knownRegions = (
				English,
				Japanese,
				French,
				German,
				en,
			);
			mainGroup = 08FB7794FE84155DC02AAC07 /* SDLTest */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = 003FA63B093FFD41000C53B3 /* Products */;
					ProjectRef = 003FA63A093FFD41000C53B3 /* SDL.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				BEC566920761D90300A33029 /* All */,
				DB166D7E16A1D12400A1396C /* SDL_test */,
				BEC566AB0761D90300A33029 /* checkkeys */,
				DBEC54D91A1A81C3005B1EAB /* controllermap */,
				BEC566C50761D90300A33029 /* loopwave */,
				0017957410741F7900F5D044 /* testatomic */,
				00179595107421BF00F5D044 /* testaudioinfo */,
				00179756107431B300F5D044 /* testdraw2 */,
				DB0F48D917CA51E5008798C5 /* testdrawchessboard */,
				DB445EE618184B7000B306B0 /* testdropfile */,
				BEC566FB0761D90300A33029 /* testerror */,
				002F340109CA1BFF00EBEB88 /* testfile */,
				DB0F48EF17CA5212008798C5 /* testfilesystem */,
				BBFC08B7164C6862003E6A99 /* testgamecontroller */,
				DB166DAD16A1D2F600A1396C /* testgesture */,
				0017970910742F3200F5D044 /* testgl2 */,
				00179730107430D600F5D044 /* testhaptic */,
				DB89956D18A19ABA0092407C /* testhotplug */,
				002F342009CA1F0300EBEB88 /* testiconv */,
				00179776107432AE00F5D044 /* testime */,
				001797961074334C00F5D044 /* testintersections */,
				BEC567300761D90400A33029 /* testjoystick */,
				BEC5673D0761D90400A33029 /* testkeys */,
				001797B8107433C600F5D044 /* testloadso */,
				BEC5674A0761D90400A33029 /* testlock */,
				DB166DC416A1D36A00A1396C /* testmessage */,
				001797FA1074355200F5D044 /* testmultiaudio */,
				001798781074392D00F5D044 /* testnative */,
				002F343C09CA1FB300EBEB88 /* testoverlay2 */,
				002F345909CA204F00EBEB88 /* testplatform */,
				0017989D107439DF00F5D044 /* testpower */,
				DB166DDC16A1D50C00A1396C /* testrelative */,
				DB166DF316A1D57C00A1396C /* testrendercopyex */,
				DB166E0A16A1D5AD00A1396C /* testrendertarget */,
				001798DA10743BEC00F5D044 /* testresample */,
				DB166E2716A1D64D00A1396C /* testrumble */,
				DB166E3D16A1D69000A1396C /* testscale */,
				BEC567720761D90500A33029 /* testsem */,
				DB166E5716A1D6F300A1396C /* testshader */,
				4537749112091504002F0F45 /* testshape */,
				001798FE10743F1000F5D044 /* testsprite2 */,
				DB166E6D16A1D78400A1396C /* testspriteminimal */,
				DB166E8016A1D78C00A1396C /* teststreaming */,
				BEC567230761D90400A33029 /* testthread */,
				BEC5678D0761D90500A33029 /* testtimer */,
				BEC567A70761D90500A33029 /* testversion */,
				0017992010743FB700F5D044 /* testwm2 */,
				BEC567EA0761D90600A33029 /* torturethread */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		003FA643093FFD41000C53B3 /* SDL2.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = SDL2.framework;
			remoteRef = 003FA642093FFD41000C53B3 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		003FA645093FFD41000C53B3 /* libSDL2.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libSDL2.a;
			remoteRef = 003FA644093FFD41000C53B3 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		003FA649093FFD41000C53B3 /* Standard DMG */ = {
			isa = PBXReferenceProxy;
			fileType = "compiled.mach-o.executable";
			path = "Standard DMG";
			remoteRef = 003FA648093FFD41000C53B3 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		DB1D40D717B3F30D00D74CFC /* libSDL2.dylib */ = {
			isa = PBXReferenceProxy;
			fileType = "compiled.mach-o.dylib";
			path = libSDL2.dylib;
			remoteRef = DB1D40D617B3F30D00D74CFC /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXSourcesBuildPhase section */
		0017957910741F7900F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				001795901074216E00F5D044 /* testatomic.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017959A107421BF00F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				001795B11074222D00F5D044 /* testaudioinfo.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017970E10742F3200F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0017972810742FB900F5D044 /* testgl2.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		00179735107430D600F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0017974F1074315700F5D044 /* testhaptic.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017975B107431B300F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				001797721074320D00F5D044 /* testdraw2.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017977B107432AE00F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				00179792107432FA00F5D044 /* testime.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017979B1074334C00F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				001797B41074339C00F5D044 /* testintersections.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		001797BD107433C600F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				001797D41074343E00F5D044 /* testloadso.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		001797FF1074355200F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				001798161074359B00F5D044 /* testmultiaudio.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017987E1074392D00F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0017987F1074392D00F5D044 /* testnative.c in Sources */,
				001798801074392D00F5D044 /* testnativecocoa.m in Sources */,
				001798811074392D00F5D044 /* testnativex11.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		001798A2107439DF00F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				001798BA10743A4900F5D044 /* testpower.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		001798DF10743BEC00F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				001798FA10743E9200F5D044 /* testresample.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017990310743F1000F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0017991A10743F5300F5D044 /* testsprite2.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0017992510743FB700F5D044 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0017993C10743FEF00F5D044 /* testwm2.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		002F340709CA1BFF00EBEB88 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				002F341809CA1C5B00EBEB88 /* testfile.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		002F342609CA1F0300EBEB88 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				002F343709CA1F6F00EBEB88 /* testiconv.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		002F344209CA1FB300EBEB88 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				002F345409CA202000EBEB88 /* testoverlay2.c in Sources */,
				66E88E8B203B778F0004D44E /* testyuv_cvt.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		002F345F09CA204F00EBEB88 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				002F347009CA20A600EBEB88 /* testplatform.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4537748F12091504002F0F45 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				453774A5120915E3002F0F45 /* testshape.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BBFC08BC164C6862003E6A99 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BBFC08D0164C6876003E6A99 /* testgamecontroller.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC566B00761D90300A33029 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BEC566B10761D90300A33029 /* checkkeys.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC566CA0761D90300A33029 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BEC566CB0761D90300A33029 /* loopwave.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567000761D90300A33029 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BEC567010761D90300A33029 /* testerror.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567280761D90400A33029 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BEC567290761D90400A33029 /* testthread.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567350761D90400A33029 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BEC567360761D90400A33029 /* testjoystick.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567420761D90400A33029 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BEC567430761D90400A33029 /* testkeys.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC5674F0761D90400A33029 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BEC567500761D90400A33029 /* testlock.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567770761D90500A33029 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BEC567780761D90500A33029 /* testsem.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567920761D90500A33029 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BEC567930761D90500A33029 /* testtimer.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567AC0761D90500A33029 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BEC567AD0761D90500A33029 /* testver.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BEC567EF0761D90600A33029 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BEC567F00761D90600A33029 /* torturethread.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB0F48DA17CA51E5008798C5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB0F48EE17CA51F8008798C5 /* testdrawchessboard.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB0F48F017CA5212008798C5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB0F490317CA5225008798C5 /* testfilesystem.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166D7B16A1D12400A1396C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB166D9316A1D1A500A1396C /* SDL_test_assert.c in Sources */,
				DB166D9416A1D1A500A1396C /* SDL_test_common.c in Sources */,
				DB166D9516A1D1A500A1396C /* SDL_test_compare.c in Sources */,
				DB166D9616A1D1A500A1396C /* SDL_test_crc32.c in Sources */,
				DB166D9716A1D1A500A1396C /* SDL_test_font.c in Sources */,
				DB166D9816A1D1A500A1396C /* SDL_test_fuzzer.c in Sources */,
				DB166D9916A1D1A500A1396C /* SDL_test_harness.c in Sources */,
				DB166D9A16A1D1A500A1396C /* SDL_test_imageBlit.c in Sources */,
				DB166D9B16A1D1A500A1396C /* SDL_test_imageBlitBlend.c in Sources */,
				DB166D9C16A1D1A500A1396C /* SDL_test_imageFace.c in Sources */,
				DB166D9D16A1D1A500A1396C /* SDL_test_imagePrimitives.c in Sources */,
				DB166D9E16A1D1A500A1396C /* SDL_test_imagePrimitivesBlend.c in Sources */,
				DB166D9F16A1D1A500A1396C /* SDL_test_log.c in Sources */,
				DB166DA016A1D1A500A1396C /* SDL_test_md5.c in Sources */,
				AAF02FFA1F90092700B9A9FB /* SDL_test_memory.c in Sources */,
				DB166DA116A1D1A500A1396C /* SDL_test_random.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166DAE16A1D2F600A1396C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB166DC116A1D31E00A1396C /* testgesture.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166DC516A1D36A00A1396C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB166DD716A1D37800A1396C /* testmessage.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166DDD16A1D50C00A1396C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB166DF016A1D52500A1396C /* testrelative.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166DF416A1D57C00A1396C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB166E0716A1D59400A1396C /* testrendercopyex.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E0B16A1D5AD00A1396C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB166E1E16A1D5C300A1396C /* testrendertarget.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E2816A1D64D00A1396C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB166E3C16A1D66500A1396C /* testrumble.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E3E16A1D69000A1396C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB166E5416A1D6A300A1396C /* testscale.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E5816A1D6F300A1396C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB166E6A16A1D70C00A1396C /* testshader.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E6E16A1D78400A1396C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB166E9316A1D7BC00A1396C /* testspriteminimal.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB166E8116A1D78C00A1396C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB166E9416A1D7C700A1396C /* teststreaming.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB445EE718184B7000B306B0 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB445EFB18184BB600B306B0 /* testdropfile.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DB89956E18A19ABA0092407C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DB89958418A19B130092407C /* testhotplug.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DBEC54DA1A1A81C3005B1EAB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DBEC54EB1A1A8205005B1EAB /* controllermap.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		001799481074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BEC566AB0761D90300A33029 /* checkkeys */;
			targetProxy = 001799471074403E00F5D044 /* PBXContainerItemProxy */;
		};
		0017994C1074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BEC566C50761D90300A33029 /* loopwave */;
			targetProxy = 0017994B1074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799501074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 0017957410741F7900F5D044 /* testatomic */;
			targetProxy = 0017994F1074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799521074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 00179595107421BF00F5D044 /* testaudioinfo */;
			targetProxy = 001799511074403E00F5D044 /* PBXContainerItemProxy */;
		};
		0017995A1074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 00179756107431B300F5D044 /* testdraw2 */;
			targetProxy = 001799591074403E00F5D044 /* PBXContainerItemProxy */;
		};
		0017995E1074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BEC566FB0761D90300A33029 /* testerror */;
			targetProxy = 0017995D1074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799601074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 002F340109CA1BFF00EBEB88 /* testfile */;
			targetProxy = 0017995F1074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799661074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 0017970910742F3200F5D044 /* testgl2 */;
			targetProxy = 001799651074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799681074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 00179730107430D600F5D044 /* testhaptic */;
			targetProxy = 001799671074403E00F5D044 /* PBXContainerItemProxy */;
		};
		0017996A1074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BEC567230761D90400A33029 /* testthread */;
			targetProxy = 001799691074403E00F5D044 /* PBXContainerItemProxy */;
		};
		0017996C1074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 002F342009CA1F0300EBEB88 /* testiconv */;
			targetProxy = 0017996B1074403E00F5D044 /* PBXContainerItemProxy */;
		};
		0017996E1074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 00179776107432AE00F5D044 /* testime */;
			targetProxy = 0017996D1074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799701074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 001797961074334C00F5D044 /* testintersections */;
			targetProxy = 0017996F1074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799721074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BEC567300761D90400A33029 /* testjoystick */;
			targetProxy = 001799711074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799741074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BEC5673D0761D90400A33029 /* testkeys */;
			targetProxy = 001799731074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799761074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 001797B8107433C600F5D044 /* testloadso */;
			targetProxy = 001799751074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799781074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BEC5674A0761D90400A33029 /* testlock */;
			targetProxy = 001799771074403E00F5D044 /* PBXContainerItemProxy */;
		};
		0017997C1074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 001797FA1074355200F5D044 /* testmultiaudio */;
			targetProxy = 0017997B1074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799801074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 001798781074392D00F5D044 /* testnative */;
			targetProxy = 0017997F1074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799841074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 002F343C09CA1FB300EBEB88 /* testoverlay2 */;
			targetProxy = 001799831074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799881074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 002F345909CA204F00EBEB88 /* testplatform */;
			targetProxy = 001799871074403E00F5D044 /* PBXContainerItemProxy */;
		};
		0017998A1074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 0017989D107439DF00F5D044 /* testpower */;
			targetProxy = 001799891074403E00F5D044 /* PBXContainerItemProxy */;
		};
		0017998C1074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 001798DA10743BEC00F5D044 /* testresample */;
			targetProxy = 0017998B1074403E00F5D044 /* PBXContainerItemProxy */;
		};
		0017998E1074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BEC567720761D90500A33029 /* testsem */;
			targetProxy = 0017998D1074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799921074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 001798FE10743F1000F5D044 /* testsprite2 */;
			targetProxy = 001799911074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799941074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BEC5678D0761D90500A33029 /* testtimer */;
			targetProxy = 001799931074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799961074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BEC567A70761D90500A33029 /* testversion */;
			targetProxy = 001799951074403E00F5D044 /* PBXContainerItemProxy */;
		};
		0017999E1074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 0017992010743FB700F5D044 /* testwm2 */;
			targetProxy = 0017999D1074403E00F5D044 /* PBXContainerItemProxy */;
		};
		001799A21074403E00F5D044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BEC567EA0761D90600A33029 /* torturethread */;
			targetProxy = 001799A11074403E00F5D044 /* PBXContainerItemProxy */;
		};
		DB0F490517CA5249008798C5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DB0F48D917CA51E5008798C5 /* testdrawchessboard */;
			targetProxy = DB0F490417CA5249008798C5 /* PBXContainerItemProxy */;
		};
		DB0F490717CA5249008798C5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DB0F48EF17CA5212008798C5 /* testfilesystem */;
			targetProxy = DB0F490617CA5249008798C5 /* PBXContainerItemProxy */;
		};
		DB166D6E16A1CEAA00A1396C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BBFC08B7164C6862003E6A99 /* testgamecontroller */;
			targetProxy = DB166D6D16A1CEAA00A1396C /* PBXContainerItemProxy */;
		};
		DB166D7016A1CEAF00A1396C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 4537749112091504002F0F45 /* testshape */;
			targetProxy = DB166D6F16A1CEAF00A1396C /* PBXContainerItemProxy */;
		};
		DB166DC316A1D32C00A1396C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DB166DAD16A1D2F600A1396C /* testgesture */;
			targetProxy = DB166DC216A1D32C00A1396C /* PBXContainerItemProxy */;
		};
		DB166DD916A1D38900A1396C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DB166DC416A1D36A00A1396C /* testmessage */;
			targetProxy = DB166DD816A1D38900A1396C /* PBXContainerItemProxy */;
		};
		DB166DF216A1D53700A1396C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DB166DDC16A1D50C00A1396C /* testrelative */;
			targetProxy = DB166DF116A1D53700A1396C /* PBXContainerItemProxy */;
		};
		DB166E0916A1D5A400A1396C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DB166DF316A1D57C00A1396C /* testrendercopyex */;
			targetProxy = DB166E0816A1D5A400A1396C /* PBXContainerItemProxy */;
		};
		DB166E2016A1D5D000A1396C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DB166E0A16A1D5AD00A1396C /* testrendertarget */;
			targetProxy = DB166E1F16A1D5D000A1396C /* PBXContainerItemProxy */;
		};
		DB166E3B16A1D65A00A1396C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DB166E2716A1D64D00A1396C /* testrumble */;
			targetProxy = DB166E3A16A1D65A00A1396C /* PBXContainerItemProxy */;
		};
		DB166E5616A1D6B800A1396C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DB166E3D16A1D69000A1396C /* testscale */;
			targetProxy = DB166E5516A1D6B800A1396C /* PBXContainerItemProxy */;
		};
		DB166E6C16A1D72000A1396C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DB166E5716A1D6F300A1396C /* testshader */;
			targetProxy = DB166E6B16A1D72000A1396C /* PBXContainerItemProxy */;
		};
		DB166E9616A1D7CD00A1396C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DB166E6D16A1D78400A1396C /* testspriteminimal */;
			targetProxy = DB166E9516A1D7CD00A1396C /* PBXContainerItemProxy */;
		};
		DB166E9816A1D7CF00A1396C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DB166E8016A1D78C00A1396C /* teststreaming */;
			targetProxy = DB166E9716A1D7CF00A1396C /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		0017958910741F7900F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testatomic;
			};
			name = Debug;
		};
		0017958A10741F7900F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testatomic;
			};
			name = Release;
		};
		001795AA107421BF00F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testaudioinfo;
			};
			name = Debug;
		};
		001795AB107421BF00F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testaudioinfo;
			};
			name = Release;
		};
		0017971E10742F3200F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				GCC_PREPROCESSOR_DEFINITIONS = HAVE_OPENGL;
				PRODUCT_NAME = testgl2;
			};
			name = Debug;
		};
		0017971F10742F3200F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				GCC_PREPROCESSOR_DEFINITIONS = HAVE_OPENGL;
				PRODUCT_NAME = testgl2;
			};
			name = Release;
		};
		00179745107430D600F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testhaptic;
			};
			name = Debug;
		};
		00179746107430D600F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testhaptic;
			};
			name = Release;
		};
		0017976B107431B300F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testdraw2;
			};
			name = Debug;
		};
		0017976C107431B300F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testdraw2;
			};
			name = Release;
		};
		0017978B107432AE00F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testime;
			};
			name = Debug;
		};
		0017978C107432AE00F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testime;
			};
			name = Release;
		};
		001797AB1074334C00F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testintersections;
			};
			name = Debug;
		};
		001797AC1074334C00F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testintersections;
			};
			name = Release;
		};
		001797CD107433C600F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testloadso;
			};
			name = Debug;
		};
		001797CE107433C600F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testloadso;
			};
			name = Release;
		};
		0017980F1074355200F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testmultiaudio;
			};
			name = Debug;
		};
		001798101074355200F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testmultiaudio;
			};
			name = Release;
		};
		001798911074392D00F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				LIBRARY_SEARCH_PATHS = /usr/X11/lib;
				OTHER_LDFLAGS = "-lX11";
				PRODUCT_NAME = testnative;
			};
			name = Debug;
		};
		001798921074392D00F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				LIBRARY_SEARCH_PATHS = /usr/X11/lib;
				OTHER_LDFLAGS = "-lX11";
				PRODUCT_NAME = testnative;
			};
			name = Release;
		};
		001798B2107439DF00F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testpower;
			};
			name = Debug;
		};
		001798B3107439DF00F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testpower;
			};
			name = Release;
		};
		001798EF10743BEC00F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testresample;
			};
			name = Debug;
		};
		001798F010743BEC00F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testresample;
			};
			name = Release;
		};
		0017991310743F1000F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testsprite2;
			};
			name = Debug;
		};
		0017991410743F1000F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testsprite2;
			};
			name = Release;
		};
		0017993510743FB700F5D044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testwm2;
			};
			name = Debug;
		};
		0017993610743FB700F5D044 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testwm2;
			};
			name = Release;
		};
		002A85B21073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				FRAMEWORK_SEARCH_PATHS = (
					"$(SRCROOT)/../SDL/build/$(CONFIGURATION)",
					"$(HOME)/Library/Frameworks",
					/Library/Frameworks,
				);
				GCC_OPTIMIZATION_LEVEL = 0;
				HEADER_SEARCH_PATHS = ../../include;
				MACOSX_DEPLOYMENT_TARGET = 10.6;
			};
			name = Debug;
		};
		002A85B31073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = "Build All";
			};
			name = Debug;
		};
		002A85B41073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = checkkeys;
			};
			name = Debug;
		};
		002A85B61073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = loopwave;
			};
			name = Debug;
		};
		002A85BC1073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testerror;
			};
			name = Debug;
		};
		002A85BD1073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testfile;
			};
			name = Debug;
		};
		002A85C01073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testiconv;
			};
			name = Debug;
		};
		002A85C11073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testjoystick;
			};
			name = Debug;
		};
		002A85C21073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testkeys;
			};
			name = Debug;
		};
		002A85C31073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testlock;
			};
			name = Debug;
		};
		002A85C51073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testoverlay2;
			};
			name = Debug;
		};
		002A85C71073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testplatform;
			};
			name = Debug;
		};
		002A85C81073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testsem;
			};
			name = Debug;
		};
		002A85CA1073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testthread;
			};
			name = Debug;
		};
		002A85CB1073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testtimer;
			};
			name = Debug;
		};
		002A85CC1073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testversion;
			};
			name = Debug;
		};
		002A85D11073008E007319AE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = torturethread;
			};
			name = Debug;
		};
		002A85D41073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				FRAMEWORK_SEARCH_PATHS = (
					"$(SRCROOT)/../SDL/build/$(CONFIGURATION)",
					"$(HOME)/Library/Frameworks",
					/Library/Frameworks,
				);
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				HEADER_SEARCH_PATHS = ../../include;
				MACOSX_DEPLOYMENT_TARGET = 10.6;
			};
			name = Release;
		};
		002A85D51073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = "Build All";
			};
			name = Release;
		};
		002A85D61073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = checkkeys;
			};
			name = Release;
		};
		002A85D81073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = loopwave;
			};
			name = Release;
		};
		002A85DE1073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testerror;
			};
			name = Release;
		};
		002A85DF1073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testfile;
			};
			name = Release;
		};
		002A85E21073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testiconv;
			};
			name = Release;
		};
		002A85E31073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testjoystick;
			};
			name = Release;
		};
		002A85E41073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testkeys;
			};
			name = Release;
		};
		002A85E51073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testlock;
			};
			name = Release;
		};
		002A85E71073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testoverlay2;
			};
			name = Release;
		};
		002A85E91073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testplatform;
			};
			name = Release;
		};
		002A85EA1073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testsem;
			};
			name = Release;
		};
		002A85EC1073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testthread;
			};
			name = Release;
		};
		002A85ED1073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testtimer;
			};
			name = Release;
		};
		002A85EE1073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testversion;
			};
			name = Release;
		};
		002A85F31073009D007319AE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = torturethread;
			};
			name = Release;
		};
		4537749712091509002F0F45 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testshape;
			};
			name = Debug;
		};
		4537749812091509002F0F45 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testshape;
			};
			name = Release;
		};
		BBFC08CB164C6862003E6A99 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testgamecontroller;
			};
			name = Debug;
		};
		BBFC08CC164C6862003E6A99 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testgamecontroller;
			};
			name = Release;
		};
		DB0F48EA17CA51E5008798C5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testdrawchessboard;
			};
			name = Debug;
		};
		DB0F48EB17CA51E5008798C5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testdrawchessboard;
			};
			name = Release;
		};
		DB0F48FF17CA5212008798C5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testfilesystem;
			};
			name = Debug;
		};
		DB0F490017CA5212008798C5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testfilesystem;
			};
			name = Release;
		};
		DB166D8116A1D12400A1396C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				EXECUTABLE_PREFIX = lib;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		DB166D8216A1D12400A1396C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				EXECUTABLE_PREFIX = lib;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
		DB166DBD16A1D2F600A1396C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testgesture;
			};
			name = Debug;
		};
		DB166DBE16A1D2F600A1396C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testgesture;
			};
			name = Release;
		};
		DB166DD316A1D36A00A1396C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testmessage;
			};
			name = Debug;
		};
		DB166DD416A1D36A00A1396C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testmessage;
			};
			name = Release;
		};
		DB166DEC16A1D50C00A1396C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testrelative;
			};
			name = Debug;
		};
		DB166DED16A1D50C00A1396C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testrelative;
			};
			name = Release;
		};
		DB166E0316A1D57C00A1396C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testrendercopyex;
			};
			name = Debug;
		};
		DB166E0416A1D57C00A1396C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testrendercopyex;
			};
			name = Release;
		};
		DB166E1A16A1D5AD00A1396C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testrendertarget;
			};
			name = Debug;
		};
		DB166E1B16A1D5AD00A1396C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testrendertarget;
			};
			name = Release;
		};
		DB166E3616A1D64D00A1396C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testrumble;
			};
			name = Debug;
		};
		DB166E3716A1D64D00A1396C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testrumble;
			};
			name = Release;
		};
		DB166E5016A1D69000A1396C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testscale;
			};
			name = Debug;
		};
		DB166E5116A1D69000A1396C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testscale;
			};
			name = Release;
		};
		DB166E6616A1D6F300A1396C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testshader;
			};
			name = Debug;
		};
		DB166E6716A1D6F300A1396C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testshader;
			};
			name = Release;
		};
		DB166E7C16A1D78400A1396C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testspriteminimal;
			};
			name = Debug;
		};
		DB166E7D16A1D78400A1396C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testspriteminimal;
			};
			name = Release;
		};
		DB166E8F16A1D78C00A1396C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = teststreaming;
			};
			name = Debug;
		};
		DB166E9016A1D78C00A1396C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = teststreaming;
			};
			name = Release;
		};
		DB445EF618184B7000B306B0 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				INFOPLIST_FILE = "TestDropFile-Info.plist";
				PRODUCT_NAME = testdropfile;
			};
			name = Debug;
		};
		DB445EF718184B7000B306B0 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				INFOPLIST_FILE = "TestDropFile-Info.plist";
				PRODUCT_NAME = testdropfile;
			};
			name = Release;
		};
		DB89957C18A19ABA0092407C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testhotplug;
			};
			name = Debug;
		};
		DB89957D18A19ABA0092407C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = testhotplug;
			};
			name = Release;
		};
		DBEC54E81A1A81C3005B1EAB /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = controllermap;
			};
			name = Debug;
		};
		DBEC54E91A1A81C3005B1EAB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = controllermap;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		0017958610741F7900F5D044 /* Build configuration list for PBXNativeTarget "testatomic" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0017958910741F7900F5D044 /* Debug */,
				0017958A10741F7900F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001795A7107421BF00F5D044 /* Build configuration list for PBXNativeTarget "testaudioinfo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				001795AA107421BF00F5D044 /* Debug */,
				001795AB107421BF00F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		0017971B10742F3200F5D044 /* Build configuration list for PBXNativeTarget "testgl2" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0017971E10742F3200F5D044 /* Debug */,
				0017971F10742F3200F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		00179742107430D600F5D044 /* Build configuration list for PBXNativeTarget "testhaptic" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00179745107430D600F5D044 /* Debug */,
				00179746107430D600F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		00179768107431B300F5D044 /* Build configuration list for PBXNativeTarget "testdraw2" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0017976B107431B300F5D044 /* Debug */,
				0017976C107431B300F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		00179788107432AE00F5D044 /* Build configuration list for PBXNativeTarget "testime" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0017978B107432AE00F5D044 /* Debug */,
				0017978C107432AE00F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001797A81074334C00F5D044 /* Build configuration list for PBXNativeTarget "testintersections" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				001797AB1074334C00F5D044 /* Debug */,
				001797AC1074334C00F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001797CA107433C600F5D044 /* Build configuration list for PBXNativeTarget "testloadso" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				001797CD107433C600F5D044 /* Debug */,
				001797CE107433C600F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		0017980C1074355200F5D044 /* Build configuration list for PBXNativeTarget "testmultiaudio" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0017980F1074355200F5D044 /* Debug */,
				001798101074355200F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		0017988E1074392D00F5D044 /* Build configuration list for PBXNativeTarget "testnative" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				001798911074392D00F5D044 /* Debug */,
				001798921074392D00F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001798AF107439DF00F5D044 /* Build configuration list for PBXNativeTarget "testpower" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				001798B2107439DF00F5D044 /* Debug */,
				001798B3107439DF00F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001798EC10743BEC00F5D044 /* Build configuration list for PBXNativeTarget "testresample" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				001798EF10743BEC00F5D044 /* Debug */,
				001798F010743BEC00F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		0017991010743F1000F5D044 /* Build configuration list for PBXNativeTarget "testsprite2" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0017991310743F1000F5D044 /* Debug */,
				0017991410743F1000F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		0017993210743FB700F5D044 /* Build configuration list for PBXNativeTarget "testwm2" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0017993510743FB700F5D044 /* Debug */,
				0017993610743FB700F5D044 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001B593808BDB826006539E9 /* Build configuration list for PBXNativeTarget "checkkeys" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85B41073008E007319AE /* Debug */,
				002A85D61073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001B594008BDB826006539E9 /* Build configuration list for PBXNativeTarget "loopwave" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85B61073008E007319AE /* Debug */,
				002A85D81073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001B595008BDB826006539E9 /* Build configuration list for PBXNativeTarget "testerror" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85BC1073008E007319AE /* Debug */,
				002A85DE1073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001B595C08BDB826006539E9 /* Build configuration list for PBXNativeTarget "testthread" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85CA1073008E007319AE /* Debug */,
				002A85EC1073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001B596008BDB826006539E9 /* Build configuration list for PBXNativeTarget "testjoystick" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85C11073008E007319AE /* Debug */,
				002A85E31073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001B596408BDB826006539E9 /* Build configuration list for PBXNativeTarget "testkeys" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85C21073008E007319AE /* Debug */,
				002A85E41073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001B596808BDB826006539E9 /* Build configuration list for PBXNativeTarget "testlock" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85C31073008E007319AE /* Debug */,
				002A85E51073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001B597008BDB826006539E9 /* Build configuration list for PBXNativeTarget "testsem" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85C81073008E007319AE /* Debug */,
				002A85EA1073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001B597808BDB826006539E9 /* Build configuration list for PBXNativeTarget "testtimer" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85CB1073008E007319AE /* Debug */,
				002A85ED1073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001B598008BDB826006539E9 /* Build configuration list for PBXNativeTarget "testversion" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85CC1073008E007319AE /* Debug */,
				002A85EE1073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001B599408BDB826006539E9 /* Build configuration list for PBXNativeTarget "torturethread" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85D11073008E007319AE /* Debug */,
				002A85F31073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001B599808BDB826006539E9 /* Build configuration list for PBXAggregateTarget "All" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85B31073008E007319AE /* Debug */,
				002A85D51073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		001B5A0C08BDB826006539E9 /* Build configuration list for PBXProject "SDLTest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85B21073008E007319AE /* Debug */,
				002A85D41073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		002F340E09CA1BFF00EBEB88 /* Build configuration list for PBXNativeTarget "testfile" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85BD1073008E007319AE /* Debug */,
				002A85DF1073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		002F342D09CA1F0300EBEB88 /* Build configuration list for PBXNativeTarget "testiconv" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85C01073008E007319AE /* Debug */,
				002A85E21073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		002F344909CA1FB300EBEB88 /* Build configuration list for PBXNativeTarget "testoverlay2" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85C51073008E007319AE /* Debug */,
				002A85E71073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		002F346609CA204F00EBEB88 /* Build configuration list for PBXNativeTarget "testplatform" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				002A85C71073008E007319AE /* Debug */,
				002A85E91073009D007319AE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		4537749A1209150C002F0F45 /* Build configuration list for PBXNativeTarget "testshape" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4537749712091509002F0F45 /* Debug */,
				4537749812091509002F0F45 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		BBFC08CA164C6862003E6A99 /* Build configuration list for PBXNativeTarget "testgamecontroller" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				BBFC08CB164C6862003E6A99 /* Debug */,
				BBFC08CC164C6862003E6A99 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB0F48E917CA51E5008798C5 /* Build configuration list for PBXNativeTarget "testdrawchessboard" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB0F48EA17CA51E5008798C5 /* Debug */,
				DB0F48EB17CA51E5008798C5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB0F48FE17CA5212008798C5 /* Build configuration list for PBXNativeTarget "testfilesystem" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB0F48FF17CA5212008798C5 /* Debug */,
				DB0F490017CA5212008798C5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB166D8016A1D12400A1396C /* Build configuration list for PBXNativeTarget "SDL_test" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB166D8116A1D12400A1396C /* Debug */,
				DB166D8216A1D12400A1396C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB166DBC16A1D2F600A1396C /* Build configuration list for PBXNativeTarget "testgesture" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB166DBD16A1D2F600A1396C /* Debug */,
				DB166DBE16A1D2F600A1396C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB166DD216A1D36A00A1396C /* Build configuration list for PBXNativeTarget "testmessage" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB166DD316A1D36A00A1396C /* Debug */,
				DB166DD416A1D36A00A1396C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB166DEB16A1D50C00A1396C /* Build configuration list for PBXNativeTarget "testrelative" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB166DEC16A1D50C00A1396C /* Debug */,
				DB166DED16A1D50C00A1396C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB166E0216A1D57C00A1396C /* Build configuration list for PBXNativeTarget "testrendercopyex" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB166E0316A1D57C00A1396C /* Debug */,
				DB166E0416A1D57C00A1396C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB166E1916A1D5AD00A1396C /* Build configuration list for PBXNativeTarget "testrendertarget" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB166E1A16A1D5AD00A1396C /* Debug */,
				DB166E1B16A1D5AD00A1396C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB166E3516A1D64D00A1396C /* Build configuration list for PBXNativeTarget "testrumble" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB166E3616A1D64D00A1396C /* Debug */,
				DB166E3716A1D64D00A1396C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB166E4F16A1D69000A1396C /* Build configuration list for PBXNativeTarget "testscale" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB166E5016A1D69000A1396C /* Debug */,
				DB166E5116A1D69000A1396C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB166E6516A1D6F300A1396C /* Build configuration list for PBXNativeTarget "testshader" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB166E6616A1D6F300A1396C /* Debug */,
				DB166E6716A1D6F300A1396C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB166E7B16A1D78400A1396C /* Build configuration list for PBXNativeTarget "testspriteminimal" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB166E7C16A1D78400A1396C /* Debug */,
				DB166E7D16A1D78400A1396C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB166E8E16A1D78C00A1396C /* Build configuration list for PBXNativeTarget "teststreaming" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB166E8F16A1D78C00A1396C /* Debug */,
				DB166E9016A1D78C00A1396C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB445EF518184B7000B306B0 /* Build configuration list for PBXNativeTarget "testdropfile" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB445EF618184B7000B306B0 /* Debug */,
				DB445EF718184B7000B306B0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DB89957B18A19ABA0092407C /* Build configuration list for PBXNativeTarget "testhotplug" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DB89957C18A19ABA0092407C /* Debug */,
				DB89957D18A19ABA0092407C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DBEC54E71A1A81C3005B1EAB /* Build configuration list for PBXNativeTarget "controllermap" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DBEC54E81A1A81C3005B1EAB /* Debug */,
				DBEC54E91A1A81C3005B1EAB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = 08FB7793FE84155DC02AAC07 /* Project object */;
}
