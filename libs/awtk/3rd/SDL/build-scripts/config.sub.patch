--- config.sub.orig	2017-09-09 08:01:02.********* -0700
+++ config.sub	2017-09-09 07:59:35.********* -0700
@@ -364,6 +364,19 @@
 	i*86 | x86_64)
 	  basic_machine=$basic_machine-pc
 	  ;;
+	nacl64*)
+		basic_machine=x86_64-pc
+		os=-nacl
+		;;
+	nacl*)
+		basic_machine=i686-pc
+		os=-nacl
+		;;
+	pnacl*)
+		# le32-unknown-pnacl comes from http://www.chromium.org/nativeclient/pnacl/stability-of-the-pnacl-bitcode-abi
+		basic_machine=le32-unknown
+		os=-pnacl
+		;;
 	# Object if more than one company name word.
 	*-*-*)
 		echo Invalid configuration \`$1\': machine \`$basic_machine\' not recognized 1>&2
@@ -877,6 +890,10 @@
 		basic_machine=le32-unknown
 		os=-nacl
 		;;
+	pnacl)
+		basic_machine=le32-unknown
+		os=-pnacl
+		;;
 	ncr3000)
 		basic_machine=i486-ncr
 		os=-sysv4
@@ -1429,6 +1446,12 @@
 			;;
 		esac
 		;;
+	-nacl*)
+		os=-nacl
+		;;
+	-pnacl*)
+		os=-pnacl
+		;;
 	-nto-qnx*)
 		;;
 	-nto*)
@@ -1459,6 +1482,9 @@
 	-os400*)
 		os=-os400
 		;;
+	-cegcc*)
+		os=-cegcc
+		;;
 	-wince*)
 		os=-wince
 		;;
@@ -1548,9 +1574,15 @@
 		os=-dicos
 		;;
 	-nacl*)
+        os=-nacl
+        ;;
+	-pnacl*)
+		os=-pnacl
 		;;
 	-ios)
 		;;
+	-emscripten*)
+		;;
 	-none)
 		;;
 	*)
